System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, error, Sprite, SpriteFrame, v3, _dec, _class, _class2, _descriptor, _crd, ccclass, property, FrameAnimationCmpt;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfAnimFrameInfo(extras) {
    _reporterNs.report("AnimFrameInfo", "../../common/constant/DataType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFrameAnimConfInfo(extras) {
    _reporterNs.report("FrameAnimConfInfo", "../../common/constant/DataType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIUpdateModel(extras) {
    _reporterNs.report("IUpdateModel", "../../common/constant/interface", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      error = _cc.error;
      Sprite = _cc.Sprite;
      SpriteFrame = _cc.SpriteFrame;
      v3 = _cc.v3;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3c551J0Ej1EYIOm2jbTuL1H", "FrameAnimationCmpt", undefined);

      __checkObsolete__(['_decorator', 'Component', 'error', 'Sprite', 'SpriteFrame', 'v3']);

      ({
        ccclass,
        property
      } = _decorator); // 帧动画

      _export("default", FrameAnimationCmpt = (_dec = property([SpriteFrame]), ccclass(_class = (_class2 = class FrameAnimationCmpt extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "sfs", _descriptor, this);

          this._sprite = null;
          //动画精灵
          this.frames = {};
          this.currFrameIndex = 0;
          //当前帧
          this.loading = false;
          this.conf = null;
          this.updateModel = null;
          //外部更新的模块
          this.anim = null;
          //当前播放的动画配置
          this.playInterval = 0;
          this.playElapsed = 0;
          this.playFrameIndex = 0;
          this.playCallback = null;
          //播放回调
          this.delayPlayAnim = 0;
        }

        //延迟播放
        get sprite() {
          if (!this._sprite) {
            this._sprite = this.getComponent(Sprite);
          }

          return this._sprite;
        }

        async init(conf, key) {
          this.conf = conf;
          this.anim = null;
          this.playCallback = null;
          this.currFrameIndex = 0;
          this.delayPlayAnim = 0;
          this.frames = {};
          this.sprite.spriteFrame = null;
          this.sfs.forEach(m => this.frames[m.name.split('_').last()] = m);
          await this.loadFrames(this.getAnimConfAllFrames(), key);
        } // 设置更新模块


        setUpdateModel(model) {
          var _this$updateModel;

          this.updateModel = model;
          (_this$updateModel = this.updateModel) == null || _this$updateModel.addFrameAnimation(this);
        }

        clean() {
          var _this$updateModel2;

          (_this$updateModel2 = this.updateModel) == null || _this$updateModel2.removeFrameAnimation(this.uuid);
          this.updateModel = null;
          this.conf = null;
          this.frames = {};
          this.anim = null;
          this.playCallback = null;
          this.currFrameIndex = 0;
          this.delayPlayAnim = 0;
        }

        onDestroy() {
          this.clean();
        } // 获取所有动画需要的帧


        getAnimConfAllFrames() {
          var _this$conf;

          const frames = [],
                obj = {};
          (_this$conf = this.conf) == null || _this$conf.anims.forEach(m => m.frameIndexs.forEach(frame => {
            if (frame !== '00' && !obj[frame] && !this.frames[frame]) {
              obj[frame] = true;
              frames.push(frame);
            }
          }));
          return frames;
        } // 加载所有帧


        async loadFrames(frames, key) {
          if (this.loading || frames.length === 0 || !this.conf) {
            return;
          }

          this.loading = true;
          const url = this.conf.url;
          const sfs = await Promise.all(frames.map(m => assetsMgr.loadTempRes(url + m, SpriteFrame, key)));

          if (this.isValid) {
            sfs.forEach((m, i) => {
              if (m) {
                this.frames[m.name.split('_').last()] = m;
              } else {
                error('loadFrames error, frame: ' + frames[i] + ', url: ' + url);
              }
            });
            this.loading = false;
            this.setFrame(this.currFrameIndex);
          }
        }

        update(dt) {
          if (!this.updateModel) {
            this.updateFrame(dt * 1000);
          }
        } // 每帧刷新 毫秒


        updateFrame(dt) {
          if (this.loading || !this.anim) {
            return;
          } else if (this.delayPlayAnim > 0) {
            this.delayPlayAnim -= dt;

            if (this.delayPlayAnim <= 0) {
              this.node.opacity = 255;
            }
          } else {
            this.playElapsed += dt;

            if (this.playElapsed >= this.playInterval) {
              this.playElapsed -= this.playInterval;
              this.setFrame(this.playFrameIndex);

              if (this.playFrameIndex < this.anim.frameIndexs.length - 1) {
                this.playFrameIndex += 1;
              } else if (this.anim.loop) {
                this.playFrameIndex = 0;
              } else {
                this.anim = null;
                this.playCallback && this.playCallback();
              }
            }
          }
        } // 设置一帧


        setFrame(index) {
          this.currFrameIndex = index;

          if (this.anim && !this.loading) {
            const name = this.anim.frameIndexs[index];

            if (name) {
              this.sprite.spriteFrame = this.frames[name];
            }
          }
        }

        get playAnimName() {
          var _this$anim;

          return ((_this$anim = this.anim) == null ? void 0 : _this$anim.name) || '';
        }

        getAnimConfPositionOffset() {
          var _this$conf2;

          return ((_this$conf2 = this.conf) == null ? void 0 : _this$conf2.offset) || v3();
        } // 播放动画


        play(name, cb, startTime) {
          var _this$conf3;

          const anim = this.anim = (_this$conf3 = this.conf) == null ? void 0 : _this$conf3.anims.find(m => m.name === name);

          if (!anim) {
            return cb && cb();
          }

          this.playCallback = cb;
          this.playInterval = anim.interval || 1;
          startTime = startTime || 0;
          const index = Math.floor(startTime / this.playInterval);
          this.playFrameIndex = index + 1;
          this.playElapsed = startTime % this.playInterval;
          this.setFrame(index);
        }

        delayPlay(delay, name, cb, startTime) {
          if (delay > 0) {
            this.delayPlayAnim = delay;
          } else {
            this.node.opacity = 255;
          }

          this.play(name, cb, startTime);
        }

        stop() {
          this.setFrame(0);
          this.anim = null;
          this.playCallback && this.playCallback();
          this.playCallback = null;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "sfs", [_dec], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0fd128ede2768d857787ffd7548fb0cb2e1d992b.js.map