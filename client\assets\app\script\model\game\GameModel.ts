import { IFrameAnimation } from "../../common/constant/interface"
import EventType from "../../common/event/EventType"
import FSPModel from "../battle/FSPModel"
import NetworkModel from "../common/NetworkModel"
import EncounterObj from "./EncounterObj"
import MapNodeObj from "./MapNodeObj"
import PlayerModel from "./PlayerModel"

/**
 * 游戏模块
 */
@mc.addmodel('game')
export default class GameModel extends mc.BaseModel {

    private runing: boolean = false //是否运行中

    private net: NetworkModel = null
    private player: PlayerModel = null

    private maps: MapNodeObj[][] = [] //地图数据
    private mapPaths: number[] = [] //已经走的路径
    private encounter: EncounterObj = new EncounterObj() //遭遇信息

    private frameAnimations: IFrameAnimation[] = [] //动画组件 在这里统一每帧调用
    private fspModel: FSPModel = null

    public onCreate() {
        this.net = this.getModel('net')
        this.player = this.getModel('player')
    }

    public init(data: any) {
        console.log('init game', data)
        const gameData = data.gameData
        this.maps = data.mapData.maps.map((layer, day) => layer.nodes.map((n, i) => {
            n.children = n.children.map(id => (day + 1) * 10 + id)
            return new MapNodeObj().init(day * 10 + i, n)
        }))
        this.player.init(gameData.player)
        this.encounter.init(gameData.encounter)
        this.mapPaths = gameData.mapPaths
    }

    public enter() {
        this.runing = true
    }

    public leave() {
        this.runing = false
        this.frameAnimations = []
    }

    public isRuning() { return this.runing }
    public getMaps() { return this.maps }
    public getEncounter() { return this.encounter }

    public addFrameAnimation(cmpt: IFrameAnimation) {
        if (!this.frameAnimations.has('uuid', cmpt.uuid)) {
            this.frameAnimations.push(cmpt)
        }
    }

    public removeFrameAnimation(uuid: string) {
        this.frameAnimations.remove('uuid', uuid)
    }

    public update(dt: number) {
        if (!this.runing) {
            return
        } else if (this.fspModel) {
            this.fspModel.update(dt)
        } else {
            this.updateAnimationFrame(dt * 1000)
        }
    }

    // 刷新动画帧 毫秒
    public updateAnimationFrame(dt: number) {
        for (let i = this.frameAnimations.length - 1; i >= 0; i--) {
            const cmpt = this.frameAnimations[i]
            if (cmpt.isValid) {
                cmpt.updateFrame(dt)
            } else {
                this.frameAnimations.splice(i, 1)
            }
        }
    }

    // 选择地图节点
    public async selectMapNode(index: number) {
        const { err, data } = await this.net.request('game/HD_SelectMapNode', { index }, true)
        if (!err) {
            const gameData = data.gameData
            this.player.init(gameData.player)
            this.encounter.init(gameData.encounter)
            this.emit(EventType.UPDATE_GAME_INFO)
        }
        return err
    }

    // 购买英雄
    public async buyHero(heroUid: string, areaType: number, useIndex: number) {
        const { err, data } = await this.net.request('game/HD_BuyHero', { heroUid, areaType, useIndex }, true)
        if (!err) {
            const gameData = data.gameData
            this.player.init(gameData.player)
            this.encounter.init(gameData.encounter)
        }
        return err
    }

    // 出售英雄
    public async sellHero(heroUid: string) {
        const { err, data } = await this.net.request('game/HD_SellHero', { heroUid }, true)
        if (!err) {
            this.player.updateAreaHero(data)
            this.player.updateGold(data.gold)
        }
        return err
    }

    // 移动英雄
    public async moveHero(heroUid: string, areaType: number, useIndex: number) {
        const { err, data } = await this.net.request('game/HD_MoveHero', { heroUid, areaType, useIndex }, true)
        if (!err) {
            this.player.updateAreaHero(data)
        }
        return err
    }
    //////////////////////////////////////////////////////////[ 战斗相关 ]///////////////////////////////////////////////////////////////////

    public getFspModel() { return this.fspModel }

    // 战斗开始
    public battleBegin() {
        const fighters = []
        // fighters.pushArr(this.battleAnimals.map(m => m.toFighter()))
        // fighters.pushArr(this.enemyAnimals.map(m => m.toFighter()))
        this.fspModel = new FSPModel().init({
            randSeed: ut.random(10000, 99999),
            fighters: fighters,
        })
    }

    // 战斗结束 本地
    public battleEndByLocal() {
        this.fspModel.stop()
        this.fspModel = null
    }

    // 根据阵营删除动物 来至战斗
    public removeAnimalByBattle(camp: number, uid: string) {
        if (camp === 1) {
            // this.battleAnimals.remove('uid', uid)
        } else {
            // this.enemyAnimals.remove('uid', uid)
        }
    }

}