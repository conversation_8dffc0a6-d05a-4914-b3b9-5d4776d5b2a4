System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, HeroObj, _dec, _class, _crd, PlayerModel;

  function _reportPossibleCrUseOfNetworkModel(extras) {
    _reporterNs.report("NetworkModel", "../common/NetworkModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHeroObj(extras) {
    _reporterNs.report("HeroObj", "./HeroObj", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      HeroObj = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3dad9tniCpK0bqnbB9RA8tY", "PlayerModel", undefined);

      /**
       * 玩家信息
       */
      _export("default", PlayerModel = (_dec = mc.addmodel('player'), _dec(_class = class PlayerModel extends mc.BaseModel {
        constructor(...args) {
          super(...args);
          this.net = null;
          this.battleAreas = {};
          //战斗区域
          this.prepareAreas = {};
          //备战区域
          this.roleId = 0;
          //角色id
          this.day = 0;
          //天数
          this.hour = 0;
          //小时
          this.hp = 0;
          //血量
          this.winCount = 0;
          //胜利次数
          this.gold = 0;
          //金币
          this.earnings = 0;
        }

        //收益
        onCreate() {
          this.net = this.getModel('net');
        }

        initBaseData(data) {
          this.day = data.day || 0; //天数

          this.hp = data.hp;
          this.winCount = data.winCount || 0; //胜利次数
        }

        init(data) {
          this.roleId = data.roleId || 0; //角色id

          this.day = data.day || 0; //天数

          this.hour = data.hour || 0; //小时

          this.hp = data.hp || 0; //血量

          this.winCount = data.winCount || 0; //胜利次数

          this.gold = data.gold || 0; //金币

          this.earnings = data.earnings || 0; //收益

          this.updateAreaHero(data); //刷新区域英雄信息
        }

        initAreaData(data) {
          const areas = {};

          for (let key in data) {
            areas[key] = new (_crd && HeroObj === void 0 ? (_reportPossibleCrUseOfHeroObj({
              error: Error()
            }), HeroObj) : HeroObj)().init(data[key]); //初始化武将数据
          }

          return areas;
        }

        clean() {
          this.day = 0;
        }

        getDay() {
          return this.day;
        }

        getBattleAreas() {
          return this.battleAreas;
        }

        updateGold(val) {
          this.gold = val;
        } // 获取英雄列表


        getHeros() {
          const heros = [];

          for (let k in this.battleAreas) {
            const hero = this.battleAreas[k];

            if (hero) {
              heros.push(hero);
            }
          }

          for (let k in this.prepareAreas) {
            const hero = this.prepareAreas[k];

            if (hero) {
              heros.push(hero);
            }
          }

          return heros;
        } // 刷新区域英雄信息


        updateAreaHero(data) {
          this.battleAreas = this.initAreaData(data.battleAreas || []); //战斗区域

          this.prepareAreas = this.initAreaData(data.prepareAreas || []); //备战区域
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d8f4e665c0176b36fe0ad01b337fe228797d7729.js.map