import { _decorator, Component, Node } from "cc";
import <PERSON><PERSON><PERSON><PERSON> from "../../model/game/HeroObj";

const { ccclass, property } = _decorator;

// 属性条
@ccclass
export default class AttrBarCmpt extends Component {

    private main: Node = null
    private data: SoldierObj = null

    public init(data: SoldierObj) {
        this.data = data
        this.main = this.Child('main')
        this.reset()
        return this
    }

    public clean() {
        this.node.destroy()
        this.data = null
    }

    // 初始化信息
    public reset() {
        if (!this.data) {
            return
        }
        this.node.active = !this.data.isDie()
        this.main.Child('1/val', mc.LabelRollNumber).set(this.data.hp)
        this.main.Child('2/val', mc.LabelRollNumber).set(this.data.attack)
    }

    // 同步信息
    public play() {
        if (!this.data) {
            return
        }
        this.main.Child('1/val', mc.LabelRollNumber).to(this.data.hp)
        this.main.Child('2/val', mc.LabelRollNumber).to(this.data.attack)
    }
}