{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts"], "names": ["_decorator", "Component", "error", "Sprite", "SpriteFrame", "v3", "ccclass", "property", "FrameAnimationCmpt", "_sprite", "frames", "currFrameIndex", "loading", "conf", "updateModel", "anim", "playInterval", "playElapsed", "playFrameIndex", "play<PERSON>allback", "delayPlayAnim", "sprite", "getComponent", "init", "key", "spriteFrame", "sfs", "for<PERSON>ach", "m", "name", "split", "last", "loadFrames", "getAnimConfAllFrames", "setUpdateModel", "model", "addFrameAnimation", "clean", "removeFrameAnimation", "uuid", "onDestroy", "obj", "anims", "frameIndexs", "frame", "push", "length", "url", "Promise", "all", "map", "assetsMgr", "loadTempRes", "<PERSON><PERSON><PERSON><PERSON>", "i", "set<PERSON>rame", "update", "dt", "updateFrame", "node", "opacity", "loop", "index", "playAnimName", "getAnimConfPositionOffset", "offset", "play", "cb", "startTime", "find", "interval", "Math", "floor", "delayPlay", "delay", "stop"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;;;;;;;;OAItD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U,GAE9B;;yBAEqBQ,kB,WAEhBD,QAAQ,CAAC,CAACH,WAAD,CAAD,C,EAHZE,O,qBAAD,MACqBE,kBADrB,SACgDP,SADhD,CAC0D;AAAA;AAAA;;AAAA;;AAAA,eAK9CQ,OAL8C,GAK5B,IAL4B;AAKvB;AALuB,eAM9CC,MAN8C,GAML,EANK;AAAA,eAO9CC,cAP8C,GAOrB,CAPqB;AAOnB;AAPmB,eAQ9CC,OAR8C,GAQ3B,KAR2B;AAAA,eAS9CC,IAT8C,GASpB,IAToB;AAAA,eAU9CC,WAV8C,GAUlB,IAVkB;AAUb;AAVa,eAY9CC,IAZ8C,GAYxB,IAZwB;AAYnB;AAZmB,eAa9CC,YAb8C,GAavB,CAbuB;AAAA,eAc9CC,WAd8C,GAcxB,CAdwB;AAAA,eAe9CC,cAf8C,GAerB,CAfqB;AAAA,eAgB9CC,YAhB8C,GAgBrB,IAhBqB;AAgBhB;AAhBgB,eAiB9CC,aAjB8C,GAiBtB,CAjBsB;AAAA;;AAiBpB;AAEhB,YAANC,MAAM,GAAG;AACjB,cAAI,CAAC,KAAKZ,OAAV,EAAmB;AACf,iBAAKA,OAAL,GAAe,KAAKa,YAAL,CAAkBnB,MAAlB,CAAf;AACH;;AACD,iBAAO,KAAKM,OAAZ;AACH;;AAEYc,QAAAA,IAAI,CAACV,IAAD,EAA0BW,GAA1B,EAAuC;AAAA;;AAAA;AACpD,YAAA,KAAI,CAACX,IAAL,GAAYA,IAAZ;AACA,YAAA,KAAI,CAACE,IAAL,GAAY,IAAZ;AACA,YAAA,KAAI,CAACI,YAAL,GAAoB,IAApB;AACA,YAAA,KAAI,CAACR,cAAL,GAAsB,CAAtB;AACA,YAAA,KAAI,CAACS,aAAL,GAAqB,CAArB;AACA,YAAA,KAAI,CAACV,MAAL,GAAc,EAAd;AACA,YAAA,KAAI,CAACW,MAAL,CAAYI,WAAZ,GAA0B,IAA1B;;AACA,YAAA,KAAI,CAACC,GAAL,CAASC,OAAT,CAAiBC,CAAC,IAAI,KAAI,CAAClB,MAAL,CAAYkB,CAAC,CAACC,IAAF,CAAOC,KAAP,CAAa,GAAb,EAAkBC,IAAlB,EAAZ,IAAwCH,CAA9D;;AACA,kBAAM,KAAI,CAACI,UAAL,CAAgB,KAAI,CAACC,oBAAL,EAAhB,EAA6CT,GAA7C,CAAN;AAToD;AAUvD,SApCqD,CAsCtD;;;AACOU,QAAAA,cAAc,CAACC,KAAD,EAAsB;AAAA;;AACvC,eAAKrB,WAAL,GAAmBqB,KAAnB;AACA,oCAAKrB,WAAL,+BAAkBsB,iBAAlB,CAAoC,IAApC;AACH;;AAEMC,QAAAA,KAAK,GAAG;AAAA;;AACX,qCAAKvB,WAAL,gCAAkBwB,oBAAlB,CAAuC,KAAKC,IAA5C;AACA,eAAKzB,WAAL,GAAmB,IAAnB;AACA,eAAKD,IAAL,GAAY,IAAZ;AACA,eAAKH,MAAL,GAAc,EAAd;AACA,eAAKK,IAAL,GAAY,IAAZ;AACA,eAAKI,YAAL,GAAoB,IAApB;AACA,eAAKR,cAAL,GAAsB,CAAtB;AACA,eAAKS,aAAL,GAAqB,CAArB;AACH;;AAEDoB,QAAAA,SAAS,GAAG;AACR,eAAKH,KAAL;AACH,SAzDqD,CA2DtD;;;AACQJ,QAAAA,oBAAoB,GAAG;AAAA;;AAC3B,cAAMvB,MAAgB,GAAG,EAAzB;AAAA,cAA6B+B,GAAG,GAAG,EAAnC;AACA,6BAAK5B,IAAL,wBAAW6B,KAAX,CAAiBf,OAAjB,CAAyBC,CAAC,IAAIA,CAAC,CAACe,WAAF,CAAchB,OAAd,CAAsBiB,KAAK,IAAI;AACzD,gBAAIA,KAAK,KAAK,IAAV,IAAkB,CAACH,GAAG,CAACG,KAAD,CAAtB,IAAiC,CAAC,KAAKlC,MAAL,CAAYkC,KAAZ,CAAtC,EAA0D;AACtDH,cAAAA,GAAG,CAACG,KAAD,CAAH,GAAa,IAAb;AACAlC,cAAAA,MAAM,CAACmC,IAAP,CAAYD,KAAZ;AACH;AACJ,WAL6B,CAA9B;AAMA,iBAAOlC,MAAP;AACH,SArEqD,CAuEtD;;;AACcsB,QAAAA,UAAU,CAACtB,MAAD,EAAmBc,GAAnB,EAAgC;AAAA;;AAAA;AACpD,gBAAI,MAAI,CAACZ,OAAL,IAAgBF,MAAM,CAACoC,MAAP,KAAkB,CAAlC,IAAuC,CAAC,MAAI,CAACjC,IAAjD,EAAuD;AACnD;AACH;;AACD,YAAA,MAAI,CAACD,OAAL,GAAe,IAAf;AACA,gBAAMmC,GAAG,GAAG,MAAI,CAAClC,IAAL,CAAUkC,GAAtB;AACA,gBAAMrB,GAAG,SAASsB,OAAO,CAACC,GAAR,CAAYvC,MAAM,CAACwC,GAAP,CAAWtB,CAAC,IAAIuB,SAAS,CAACC,WAAV,CAAsBL,GAAG,GAAGnB,CAA5B,EAA+BxB,WAA/B,EAA4CoB,GAA5C,CAAhB,CAAZ,CAAlB;;AACA,gBAAI,MAAI,CAAC6B,OAAT,EAAkB;AACd3B,cAAAA,GAAG,CAACC,OAAJ,CAAY,CAACC,CAAD,EAAI0B,CAAJ,KAAU;AAClB,oBAAI1B,CAAJ,EAAO;AACH,kBAAA,MAAI,CAAClB,MAAL,CAAYkB,CAAC,CAACC,IAAF,CAAOC,KAAP,CAAa,GAAb,EAAkBC,IAAlB,EAAZ,IAAwCH,CAAxC;AACH,iBAFD,MAEO;AACH1B,kBAAAA,KAAK,CAAC,8BAA8BQ,MAAM,CAAC4C,CAAD,CAApC,GAA0C,SAA1C,GAAsDP,GAAvD,CAAL;AACH;AACJ,eAND;AAOA,cAAA,MAAI,CAACnC,OAAL,GAAe,KAAf;;AACA,cAAA,MAAI,CAAC2C,QAAL,CAAc,MAAI,CAAC5C,cAAnB;AACH;AAjBmD;AAkBvD;;AAED6C,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,cAAI,CAAC,KAAK3C,WAAV,EAAuB;AACnB,iBAAK4C,WAAL,CAAiBD,EAAE,GAAG,IAAtB;AACH;AACJ,SAhGqD,CAkGtD;;;AACOC,QAAAA,WAAW,CAACD,EAAD,EAAa;AAC3B,cAAI,KAAK7C,OAAL,IAAgB,CAAC,KAAKG,IAA1B,EAAgC;AAC5B;AACH,WAFD,MAEO,IAAI,KAAKK,aAAL,GAAqB,CAAzB,EAA4B;AAC/B,iBAAKA,aAAL,IAAsBqC,EAAtB;;AACA,gBAAI,KAAKrC,aAAL,IAAsB,CAA1B,EAA6B;AACzB,mBAAKuC,IAAL,CAAUC,OAAV,GAAoB,GAApB;AACH;AACJ,WALM,MAKA;AACH,iBAAK3C,WAAL,IAAoBwC,EAApB;;AACA,gBAAI,KAAKxC,WAAL,IAAoB,KAAKD,YAA7B,EAA2C;AACvC,mBAAKC,WAAL,IAAoB,KAAKD,YAAzB;AACA,mBAAKuC,QAAL,CAAc,KAAKrC,cAAnB;;AACA,kBAAI,KAAKA,cAAL,GAAsB,KAAKH,IAAL,CAAU4B,WAAV,CAAsBG,MAAtB,GAA+B,CAAzD,EAA4D;AACxD,qBAAK5B,cAAL,IAAuB,CAAvB;AACH,eAFD,MAEO,IAAI,KAAKH,IAAL,CAAU8C,IAAd,EAAoB;AACvB,qBAAK3C,cAAL,GAAsB,CAAtB;AACH,eAFM,MAEA;AACH,qBAAKH,IAAL,GAAY,IAAZ;AACA,qBAAKI,YAAL,IAAqB,KAAKA,YAAL,EAArB;AACH;AACJ;AACJ;AACJ,SA1HqD,CA4HtD;;;AACQoC,QAAAA,QAAQ,CAACO,KAAD,EAAgB;AAC5B,eAAKnD,cAAL,GAAsBmD,KAAtB;;AACA,cAAI,KAAK/C,IAAL,IAAa,CAAC,KAAKH,OAAvB,EAAgC;AAC5B,gBAAMiB,IAAI,GAAG,KAAKd,IAAL,CAAU4B,WAAV,CAAsBmB,KAAtB,CAAb;;AACA,gBAAIjC,IAAJ,EAAU;AACN,mBAAKR,MAAL,CAAYI,WAAZ,GAA0B,KAAKf,MAAL,CAAYmB,IAAZ,CAA1B;AACH;AACJ;AACJ;;AAEsB,YAAZkC,YAAY,GAAG;AAAA;;AAAE,iBAAO,oBAAKhD,IAAL,gCAAWc,IAAX,KAAmB,EAA1B;AAA8B;;AACnDmC,QAAAA,yBAAyB,GAAG;AAAA;;AAAE,iBAAO,qBAAKnD,IAAL,iCAAWoD,MAAX,KAAqB5D,EAAE,EAA9B;AAAkC,SAxIjB,CA0ItD;;;AACO6D,QAAAA,IAAI,CAACrC,IAAD,EAAesC,EAAf,EAA8BC,SAA9B,EAAkD;AAAA;;AACzD,cAAMrD,IAAI,GAAG,KAAKA,IAAL,kBAAY,KAAKF,IAAjB,qBAAY,YAAW6B,KAAX,CAAiB2B,IAAjB,CAAsBzC,CAAC,IAAIA,CAAC,CAACC,IAAF,KAAWA,IAAtC,CAAzB;;AACA,cAAI,CAACd,IAAL,EAAW;AACP,mBAAOoD,EAAE,IAAIA,EAAE,EAAf;AACH;;AACD,eAAKhD,YAAL,GAAoBgD,EAApB;AACA,eAAKnD,YAAL,GAAoBD,IAAI,CAACuD,QAAL,IAAiB,CAArC;AACAF,UAAAA,SAAS,GAAGA,SAAS,IAAI,CAAzB;AACA,cAAMN,KAAK,GAAGS,IAAI,CAACC,KAAL,CAAWJ,SAAS,GAAG,KAAKpD,YAA5B,CAAd;AACA,eAAKE,cAAL,GAAsB4C,KAAK,GAAG,CAA9B;AACA,eAAK7C,WAAL,GAAmBmD,SAAS,GAAG,KAAKpD,YAApC;AACA,eAAKuC,QAAL,CAAcO,KAAd;AACH;;AAEMW,QAAAA,SAAS,CAACC,KAAD,EAAgB7C,IAAhB,EAA8BsC,EAA9B,EAA6CC,SAA7C,EAAiE;AAC7E,cAAIM,KAAK,GAAG,CAAZ,EAAe;AACX,iBAAKtD,aAAL,GAAqBsD,KAArB;AACH,WAFD,MAEO;AACH,iBAAKf,IAAL,CAAUC,OAAV,GAAoB,GAApB;AACH;;AACD,eAAKM,IAAL,CAAUrC,IAAV,EAAgBsC,EAAhB,EAAoBC,SAApB;AACH;;AAEMO,QAAAA,IAAI,GAAG;AACV,eAAKpB,QAAL,CAAc,CAAd;AACA,eAAKxC,IAAL,GAAY,IAAZ;AACA,eAAKI,YAAL,IAAqB,KAAKA,YAAL,EAArB;AACA,eAAKA,YAAL,GAAoB,IAApB;AACH;;AAvKqD,O;;;;;iBAGzB,E", "sourcesContent": ["import { _decorator, Component, error, Sprite, SpriteFrame, v3 } from \"cc\";\r\nimport { AnimFrameInfo, FrameAnimConfInfo } from \"../../common/constant/DataType\";\r\nimport { IUpdateModel } from \"../../common/constant/interface\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n// 帧动画\r\n@ccclass\r\nexport default class FrameAnimationCmpt extends Component {\r\n\r\n    @property([SpriteFrame])\r\n    private sfs: SpriteFrame[] = []\r\n\r\n    private _sprite: Sprite = null //动画精灵\r\n    private frames: { [key: string]: SpriteFrame } = {}\r\n    private currFrameIndex: number = 0 //当前帧\r\n    private loading: boolean = false\r\n    private conf: FrameAnimConfInfo = null\r\n    private updateModel: IUpdateModel = null //外部更新的模块\r\n\r\n    private anim: AnimFrameInfo = null //当前播放的动画配置\r\n    private playInterval: number = 0\r\n    private playElapsed: number = 0\r\n    private playFrameIndex: number = 0\r\n    private playCallback: Function = null //播放回调\r\n    private delayPlayAnim: number = 0 //延迟播放\r\n\r\n    private get sprite() {\r\n        if (!this._sprite) {\r\n            this._sprite = this.getComponent(Sprite)\r\n        }\r\n        return this._sprite\r\n    }\r\n\r\n    public async init(conf: FrameAnimConfInfo, key: string) {\r\n        this.conf = conf\r\n        this.anim = null\r\n        this.playCallback = null\r\n        this.currFrameIndex = 0\r\n        this.delayPlayAnim = 0\r\n        this.frames = {}\r\n        this.sprite.spriteFrame = null\r\n        this.sfs.forEach(m => this.frames[m.name.split('_').last()] = m)\r\n        await this.loadFrames(this.getAnimConfAllFrames(), key)\r\n    }\r\n\r\n    // 设置更新模块\r\n    public setUpdateModel(model: IUpdateModel) {\r\n        this.updateModel = model\r\n        this.updateModel?.addFrameAnimation(this)\r\n    }\r\n\r\n    public clean() {\r\n        this.updateModel?.removeFrameAnimation(this.uuid)\r\n        this.updateModel = null\r\n        this.conf = null\r\n        this.frames = {}\r\n        this.anim = null\r\n        this.playCallback = null\r\n        this.currFrameIndex = 0\r\n        this.delayPlayAnim = 0\r\n    }\r\n\r\n    onDestroy() {\r\n        this.clean()\r\n    }\r\n\r\n    // 获取所有动画需要的帧\r\n    private getAnimConfAllFrames() {\r\n        const frames: string[] = [], obj = {}\r\n        this.conf?.anims.forEach(m => m.frameIndexs.forEach(frame => {\r\n            if (frame !== '00' && !obj[frame] && !this.frames[frame]) {\r\n                obj[frame] = true\r\n                frames.push(frame)\r\n            }\r\n        }))\r\n        return frames\r\n    }\r\n\r\n    // 加载所有帧\r\n    private async loadFrames(frames: string[], key: string) {\r\n        if (this.loading || frames.length === 0 || !this.conf) {\r\n            return\r\n        }\r\n        this.loading = true\r\n        const url = this.conf.url\r\n        const sfs = await Promise.all(frames.map(m => assetsMgr.loadTempRes(url + m, SpriteFrame, key)))\r\n        if (this.isValid) {\r\n            sfs.forEach((m, i) => {\r\n                if (m) {\r\n                    this.frames[m.name.split('_').last()] = m\r\n                } else {\r\n                    error('loadFrames error, frame: ' + frames[i] + ', url: ' + url)\r\n                }\r\n            })\r\n            this.loading = false\r\n            this.setFrame(this.currFrameIndex)\r\n        }\r\n    }\r\n\r\n    update(dt: number) {\r\n        if (!this.updateModel) {\r\n            this.updateFrame(dt * 1000)\r\n        }\r\n    }\r\n\r\n    // 每帧刷新 毫秒\r\n    public updateFrame(dt: number) {\r\n        if (this.loading || !this.anim) {\r\n            return\r\n        } else if (this.delayPlayAnim > 0) {\r\n            this.delayPlayAnim -= dt\r\n            if (this.delayPlayAnim <= 0) {\r\n                this.node.opacity = 255\r\n            }\r\n        } else {\r\n            this.playElapsed += dt\r\n            if (this.playElapsed >= this.playInterval) {\r\n                this.playElapsed -= this.playInterval\r\n                this.setFrame(this.playFrameIndex)\r\n                if (this.playFrameIndex < this.anim.frameIndexs.length - 1) {\r\n                    this.playFrameIndex += 1\r\n                } else if (this.anim.loop) {\r\n                    this.playFrameIndex = 0\r\n                } else {\r\n                    this.anim = null\r\n                    this.playCallback && this.playCallback()\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // 设置一帧\r\n    private setFrame(index: number) {\r\n        this.currFrameIndex = index\r\n        if (this.anim && !this.loading) {\r\n            const name = this.anim.frameIndexs[index]\r\n            if (name) {\r\n                this.sprite.spriteFrame = this.frames[name]\r\n            }\r\n        }\r\n    }\r\n\r\n    public get playAnimName() { return this.anim?.name || '' }\r\n    public getAnimConfPositionOffset() { return this.conf?.offset || v3() }\r\n\r\n    // 播放动画\r\n    public play(name: string, cb?: Function, startTime?: number) {\r\n        const anim = this.anim = this.conf?.anims.find(m => m.name === name)\r\n        if (!anim) {\r\n            return cb && cb()\r\n        }\r\n        this.playCallback = cb\r\n        this.playInterval = anim.interval || 1\r\n        startTime = startTime || 0\r\n        const index = Math.floor(startTime / this.playInterval)\r\n        this.playFrameIndex = index + 1\r\n        this.playElapsed = startTime % this.playInterval\r\n        this.setFrame(index)\r\n    }\r\n\r\n    public delayPlay(delay: number, name: string, cb?: Function, startTime?: number) {\r\n        if (delay > 0) {\r\n            this.delayPlayAnim = delay\r\n        } else {\r\n            this.node.opacity = 255\r\n        }\r\n        this.play(name, cb, startTime)\r\n    }\r\n\r\n    public stop() {\r\n        this.setFrame(0)\r\n        this.anim = null\r\n        this.playCallback && this.playCallback()\r\n        this.playCallback = null\r\n    }\r\n}"]}