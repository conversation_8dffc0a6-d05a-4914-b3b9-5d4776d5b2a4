{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/model/game/GameModel.ts"], "names": ["EventType", "FSPModel", "EncounterObj", "MapNodeObj", "GameModel", "mc", "addmodel", "BaseModel", "runing", "net", "player", "maps", "mapPaths", "encounter", "frameAnimations", "fspModel", "onCreate", "getModel", "init", "data", "console", "log", "gameData", "mapData", "map", "layer", "day", "nodes", "n", "i", "children", "id", "enter", "leave", "isRuning", "getMaps", "get<PERSON><PERSON>unter", "addFrameAnimation", "cmpt", "has", "uuid", "push", "removeFrameAnimation", "remove", "update", "dt", "updateAnimationFrame", "length", "<PERSON><PERSON><PERSON><PERSON>", "updateFrame", "splice", "selectMapNode", "index", "err", "request", "emit", "UPDATE_GAME_INFO", "buyHero", "hero<PERSON>id", "areaType", "useIndex", "sellHero", "updateAreaHero", "updateGold", "gold", "moveHero", "getFspModel", "battleBegin", "fighters", "randSeed", "ut", "random", "battleEndByLocal", "stop", "removeAnimalByBattle", "camp", "uid"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACOA,MAAAA,S;;AACAC,MAAAA,Q;;AAEAC,MAAAA,Y;;AACAC,MAAAA,U;;;;;;;AAGP;AACA;AACA;yBAEqBC,S,WADpBC,EAAE,CAACC,QAAH,CAAY,MAAZ,C,gBAAD,MACqBF,SADrB,SACuCC,EAAE,CAACE,SAD1C,CACoD;AAAA;AAAA;AAAA,eAExCC,MAFwC,GAEtB,KAFsB;AAEhB;AAFgB,eAIxCC,GAJwC,GAIpB,IAJoB;AAAA,eAKxCC,MALwC,GAKlB,IALkB;AAAA,eAOxCC,IAPwC,GAOjB,EAPiB;AAOd;AAPc,eAQxCC,QARwC,GAQnB,EARmB;AAQhB;AARgB,eASxCC,SATwC,GASd;AAAA;AAAA,6CATc;AASK;AATL,eAWxCC,eAXwC,GAWH,EAXG;AAWA;AAXA,eAYxCC,QAZwC,GAYnB,IAZmB;AAAA;;AAczCC,QAAAA,QAAQ,GAAG;AACd,eAAKP,GAAL,GAAW,KAAKQ,QAAL,CAAc,KAAd,CAAX;AACA,eAAKP,MAAL,GAAc,KAAKO,QAAL,CAAc,QAAd,CAAd;AACH;;AAEMC,QAAAA,IAAI,CAACC,IAAD,EAAY;AACnBC,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyBF,IAAzB;AACA,gBAAMG,QAAQ,GAAGH,IAAI,CAACG,QAAtB;AACA,eAAKX,IAAL,GAAYQ,IAAI,CAACI,OAAL,CAAaZ,IAAb,CAAkBa,GAAlB,CAAsB,CAACC,KAAD,EAAQC,GAAR,KAAgBD,KAAK,CAACE,KAAN,CAAYH,GAAZ,CAAgB,CAACI,CAAD,EAAIC,CAAJ,KAAU;AACxED,YAAAA,CAAC,CAACE,QAAF,GAAaF,CAAC,CAACE,QAAF,CAAWN,GAAX,CAAeO,EAAE,IAAI,CAACL,GAAG,GAAG,CAAP,IAAY,EAAZ,GAAiBK,EAAtC,CAAb;AACA,mBAAO;AAAA;AAAA,4CAAiBb,IAAjB,CAAsBQ,GAAG,GAAG,EAAN,GAAWG,CAAjC,EAAoCD,CAApC,CAAP;AACH,WAHiD,CAAtC,CAAZ;AAIA,eAAKlB,MAAL,CAAYQ,IAAZ,CAAiBI,QAAQ,CAACZ,MAA1B;AACA,eAAKG,SAAL,CAAeK,IAAf,CAAoBI,QAAQ,CAACT,SAA7B;AACA,eAAKD,QAAL,GAAgBU,QAAQ,CAACV,QAAzB;AACH;;AAEMoB,QAAAA,KAAK,GAAG;AACX,eAAKxB,MAAL,GAAc,IAAd;AACH;;AAEMyB,QAAAA,KAAK,GAAG;AACX,eAAKzB,MAAL,GAAc,KAAd;AACA,eAAKM,eAAL,GAAuB,EAAvB;AACH;;AAEMoB,QAAAA,QAAQ,GAAG;AAAE,iBAAO,KAAK1B,MAAZ;AAAoB;;AACjC2B,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKxB,IAAZ;AAAkB;;AAC9ByB,QAAAA,YAAY,GAAG;AAAE,iBAAO,KAAKvB,SAAZ;AAAuB;;AAExCwB,QAAAA,iBAAiB,CAACC,IAAD,EAAwB;AAC5C,cAAI,CAAC,KAAKxB,eAAL,CAAqByB,GAArB,CAAyB,MAAzB,EAAiCD,IAAI,CAACE,IAAtC,CAAL,EAAkD;AAC9C,iBAAK1B,eAAL,CAAqB2B,IAArB,CAA0BH,IAA1B;AACH;AACJ;;AAEMI,QAAAA,oBAAoB,CAACF,IAAD,EAAe;AACtC,eAAK1B,eAAL,CAAqB6B,MAArB,CAA4B,MAA5B,EAAoCH,IAApC;AACH;;AAEMI,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,cAAI,CAAC,KAAKrC,MAAV,EAAkB;AACd;AACH,WAFD,MAEO,IAAI,KAAKO,QAAT,EAAmB;AACtB,iBAAKA,QAAL,CAAc6B,MAAd,CAAqBC,EAArB;AACH,WAFM,MAEA;AACH,iBAAKC,oBAAL,CAA0BD,EAAE,GAAG,IAA/B;AACH;AACJ,SA9D+C,CAgEhD;;;AACOC,QAAAA,oBAAoB,CAACD,EAAD,EAAa;AACpC,eAAK,IAAIhB,CAAC,GAAG,KAAKf,eAAL,CAAqBiC,MAArB,GAA8B,CAA3C,EAA8ClB,CAAC,IAAI,CAAnD,EAAsDA,CAAC,EAAvD,EAA2D;AACvD,kBAAMS,IAAI,GAAG,KAAKxB,eAAL,CAAqBe,CAArB,CAAb;;AACA,gBAAIS,IAAI,CAACU,OAAT,EAAkB;AACdV,cAAAA,IAAI,CAACW,WAAL,CAAiBJ,EAAjB;AACH,aAFD,MAEO;AACH,mBAAK/B,eAAL,CAAqBoC,MAArB,CAA4BrB,CAA5B,EAA+B,CAA/B;AACH;AACJ;AACJ,SA1E+C,CA4EhD;;;AAC0B,cAAbsB,aAAa,CAACC,KAAD,EAAgB;AACtC,gBAAM;AAAEC,YAAAA,GAAF;AAAOlC,YAAAA;AAAP,cAAgB,MAAM,KAAKV,GAAL,CAAS6C,OAAT,CAAiB,uBAAjB,EAA0C;AAAEF,YAAAA;AAAF,WAA1C,EAAqD,IAArD,CAA5B;;AACA,cAAI,CAACC,GAAL,EAAU;AACN,kBAAM/B,QAAQ,GAAGH,IAAI,CAACG,QAAtB;AACA,iBAAKZ,MAAL,CAAYQ,IAAZ,CAAiBI,QAAQ,CAACZ,MAA1B;AACA,iBAAKG,SAAL,CAAeK,IAAf,CAAoBI,QAAQ,CAACT,SAA7B;AACA,iBAAK0C,IAAL,CAAU;AAAA;AAAA,wCAAUC,gBAApB;AACH;;AACD,iBAAOH,GAAP;AACH,SAtF+C,CAwFhD;;;AACoB,cAAPI,OAAO,CAACC,OAAD,EAAkBC,QAAlB,EAAoCC,QAApC,EAAsD;AACtE,gBAAM;AAAEP,YAAAA,GAAF;AAAOlC,YAAAA;AAAP,cAAgB,MAAM,KAAKV,GAAL,CAAS6C,OAAT,CAAiB,iBAAjB,EAAoC;AAAEI,YAAAA,OAAF;AAAWC,YAAAA,QAAX;AAAqBC,YAAAA;AAArB,WAApC,EAAqE,IAArE,CAA5B;;AACA,cAAI,CAACP,GAAL,EAAU;AACN,kBAAM/B,QAAQ,GAAGH,IAAI,CAACG,QAAtB;AACA,iBAAKZ,MAAL,CAAYQ,IAAZ,CAAiBI,QAAQ,CAACZ,MAA1B;AACA,iBAAKG,SAAL,CAAeK,IAAf,CAAoBI,QAAQ,CAACT,SAA7B;AACH;;AACD,iBAAOwC,GAAP;AACH,SAjG+C,CAmGhD;;;AACqB,cAARQ,QAAQ,CAACH,OAAD,EAAkB;AACnC,gBAAM;AAAEL,YAAAA,GAAF;AAAOlC,YAAAA;AAAP,cAAgB,MAAM,KAAKV,GAAL,CAAS6C,OAAT,CAAiB,kBAAjB,EAAqC;AAAEI,YAAAA;AAAF,WAArC,EAAkD,IAAlD,CAA5B;;AACA,cAAI,CAACL,GAAL,EAAU;AACN,iBAAK3C,MAAL,CAAYoD,cAAZ,CAA2B3C,IAA3B;AACA,iBAAKT,MAAL,CAAYqD,UAAZ,CAAuB5C,IAAI,CAAC6C,IAA5B;AACH;;AACD,iBAAOX,GAAP;AACH,SA3G+C,CA6GhD;;;AACqB,cAARY,QAAQ,CAACP,OAAD,EAAkBC,QAAlB,EAAoCC,QAApC,EAAsD;AACvE,gBAAM;AAAEP,YAAAA,GAAF;AAAOlC,YAAAA;AAAP,cAAgB,MAAM,KAAKV,GAAL,CAAS6C,OAAT,CAAiB,kBAAjB,EAAqC;AAAEI,YAAAA,OAAF;AAAWC,YAAAA,QAAX;AAAqBC,YAAAA;AAArB,WAArC,EAAsE,IAAtE,CAA5B;;AACA,cAAI,CAACP,GAAL,EAAU;AACN,iBAAK3C,MAAL,CAAYoD,cAAZ,CAA2B3C,IAA3B;AACH;;AACD,iBAAOkC,GAAP;AACH,SApH+C,CAqHhD;;;AAEOa,QAAAA,WAAW,GAAG;AAAE,iBAAO,KAAKnD,QAAZ;AAAsB,SAvHG,CAyHhD;;;AACOoD,QAAAA,WAAW,GAAG;AACjB,gBAAMC,QAAQ,GAAG,EAAjB,CADiB,CAEjB;AACA;;AACA,eAAKrD,QAAL,GAAgB;AAAA;AAAA,sCAAeG,IAAf,CAAoB;AAChCmD,YAAAA,QAAQ,EAAEC,EAAE,CAACC,MAAH,CAAU,KAAV,EAAiB,KAAjB,CADsB;AAEhCH,YAAAA,QAAQ,EAAEA;AAFsB,WAApB,CAAhB;AAIH,SAlI+C,CAoIhD;;;AACOI,QAAAA,gBAAgB,GAAG;AACtB,eAAKzD,QAAL,CAAc0D,IAAd;AACA,eAAK1D,QAAL,GAAgB,IAAhB;AACH,SAxI+C,CA0IhD;;;AACO2D,QAAAA,oBAAoB,CAACC,IAAD,EAAeC,GAAf,EAA4B;AACnD,cAAID,IAAI,KAAK,CAAb,EAAgB,CACZ;AACH,WAFD,MAEO,CACH;AACH;AACJ;;AAjJ+C,O", "sourcesContent": ["import { IFrameAnimation } from \"../../common/constant/interface\"\r\nimport EventType from \"../../common/event/EventType\"\r\nimport FSPModel from \"../battle/FSPModel\"\r\nimport NetworkModel from \"../common/NetworkModel\"\r\nimport EncounterObj from \"./EncounterObj\"\r\nimport MapNodeObj from \"./MapNodeObj\"\r\nimport PlayerModel from \"./PlayerModel\"\r\n\r\n/**\r\n * 游戏模块\r\n */\r\*************('game')\r\nexport default class GameModel extends mc.BaseModel {\r\n\r\n    private runing: boolean = false //是否运行中\r\n\r\n    private net: NetworkModel = null\r\n    private player: PlayerModel = null\r\n\r\n    private maps: MapNodeObj[][] = [] //地图数据\r\n    private mapPaths: number[] = [] //已经走的路径\r\n    private encounter: EncounterObj = new EncounterObj() //遭遇信息\r\n\r\n    private frameAnimations: IFrameAnimation[] = [] //动画组件 在这里统一每帧调用\r\n    private fspModel: FSPModel = null\r\n\r\n    public onCreate() {\r\n        this.net = this.getModel('net')\r\n        this.player = this.getModel('player')\r\n    }\r\n\r\n    public init(data: any) {\r\n        console.log('init game', data)\r\n        const gameData = data.gameData\r\n        this.maps = data.mapData.maps.map((layer, day) => layer.nodes.map((n, i) => {\r\n            n.children = n.children.map(id => (day + 1) * 10 + id)\r\n            return new MapNodeObj().init(day * 10 + i, n)\r\n        }))\r\n        this.player.init(gameData.player)\r\n        this.encounter.init(gameData.encounter)\r\n        this.mapPaths = gameData.mapPaths\r\n    }\r\n\r\n    public enter() {\r\n        this.runing = true\r\n    }\r\n\r\n    public leave() {\r\n        this.runing = false\r\n        this.frameAnimations = []\r\n    }\r\n\r\n    public isRuning() { return this.runing }\r\n    public getMaps() { return this.maps }\r\n    public getEncounter() { return this.encounter }\r\n\r\n    public addFrameAnimation(cmpt: IFrameAnimation) {\r\n        if (!this.frameAnimations.has('uuid', cmpt.uuid)) {\r\n            this.frameAnimations.push(cmpt)\r\n        }\r\n    }\r\n\r\n    public removeFrameAnimation(uuid: string) {\r\n        this.frameAnimations.remove('uuid', uuid)\r\n    }\r\n\r\n    public update(dt: number) {\r\n        if (!this.runing) {\r\n            return\r\n        } else if (this.fspModel) {\r\n            this.fspModel.update(dt)\r\n        } else {\r\n            this.updateAnimationFrame(dt * 1000)\r\n        }\r\n    }\r\n\r\n    // 刷新动画帧 毫秒\r\n    public updateAnimationFrame(dt: number) {\r\n        for (let i = this.frameAnimations.length - 1; i >= 0; i--) {\r\n            const cmpt = this.frameAnimations[i]\r\n            if (cmpt.isValid) {\r\n                cmpt.updateFrame(dt)\r\n            } else {\r\n                this.frameAnimations.splice(i, 1)\r\n            }\r\n        }\r\n    }\r\n\r\n    // 选择地图节点\r\n    public async selectMapNode(index: number) {\r\n        const { err, data } = await this.net.request('game/HD_SelectMapNode', { index }, true)\r\n        if (!err) {\r\n            const gameData = data.gameData\r\n            this.player.init(gameData.player)\r\n            this.encounter.init(gameData.encounter)\r\n            this.emit(EventType.UPDATE_GAME_INFO)\r\n        }\r\n        return err\r\n    }\r\n\r\n    // 购买英雄\r\n    public async buyHero(heroUid: string, areaType: number, useIndex: number) {\r\n        const { err, data } = await this.net.request('game/HD_BuyHero', { heroUid, areaType, useIndex }, true)\r\n        if (!err) {\r\n            const gameData = data.gameData\r\n            this.player.init(gameData.player)\r\n            this.encounter.init(gameData.encounter)\r\n        }\r\n        return err\r\n    }\r\n\r\n    // 出售英雄\r\n    public async sellHero(heroUid: string) {\r\n        const { err, data } = await this.net.request('game/HD_SellHero', { heroUid }, true)\r\n        if (!err) {\r\n            this.player.updateAreaHero(data)\r\n            this.player.updateGold(data.gold)\r\n        }\r\n        return err\r\n    }\r\n\r\n    // 移动英雄\r\n    public async moveHero(heroUid: string, areaType: number, useIndex: number) {\r\n        const { err, data } = await this.net.request('game/HD_MoveHero', { heroUid, areaType, useIndex }, true)\r\n        if (!err) {\r\n            this.player.updateAreaHero(data)\r\n        }\r\n        return err\r\n    }\r\n    //////////////////////////////////////////////////////////[ 战斗相关 ]///////////////////////////////////////////////////////////////////\r\n\r\n    public getFspModel() { return this.fspModel }\r\n\r\n    // 战斗开始\r\n    public battleBegin() {\r\n        const fighters = []\r\n        // fighters.pushArr(this.battleAnimals.map(m => m.toFighter()))\r\n        // fighters.pushArr(this.enemyAnimals.map(m => m.toFighter()))\r\n        this.fspModel = new FSPModel().init({\r\n            randSeed: ut.random(10000, 99999),\r\n            fighters: fighters,\r\n        })\r\n    }\r\n\r\n    // 战斗结束 本地\r\n    public battleEndByLocal() {\r\n        this.fspModel.stop()\r\n        this.fspModel = null\r\n    }\r\n\r\n    // 根据阵营删除动物 来至战斗\r\n    public removeAnimalByBattle(camp: number, uid: string) {\r\n        if (camp === 1) {\r\n            // this.battleAnimals.remove('uid', uid)\r\n        } else {\r\n            // this.enemyAnimals.remove('uid', uid)\r\n        }\r\n    }\r\n\r\n}"]}