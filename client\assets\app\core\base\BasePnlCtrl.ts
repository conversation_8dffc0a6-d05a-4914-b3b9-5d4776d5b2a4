import BaseViewCtrl from "./BaseViewCtrl"
import CoreEventType from "../event/CoreEventType"
import { Layers, Node } from "cc"

// 基础UI视图控制器
export default class BasePnlCtrl extends BaseViewCtrl {

    public key: string = '' // 传入名
    public mod: string = '' // 模块名
    public url: string = '' // UI地址
    public isClean: boolean = true
    public isAct: boolean = true //是否播放动作
    public isMask: boolean = true //是否显示遮照
    public adaptHeight: number = 400 //适应高度距离

    public mask: Node = null // 当前的遮照
    public __open_index: number = 0 // 打开顺序

    public async __create() {
        this._state = 'create'
        this.node.layer = Layers.Enum.UI_2D
        this.__listenMaps()
        this.__register('create')
        await this.onCreate()
    }

    public __enter(...params: any) {
        if (this._state !== 'enter') {
            this._state = 'enter'
            this.__register('enter')
        }
        this.onEnter(...params)
    }

    public __remove() {
        this._state = 'remove'
        this.__unregister('enter')
        this.onRemove()
    }

    public __clean() {
        this._state = 'clean'
        this.__unregister()
        this.onClean()
    }

    public async onCreate() {
    }

    public onEnter(...params: any) {
    }

    public onRemove() {
    }

    public onClean() {
    }

    public onPlayActionComplete() {
    }

    public isEnter() {
        return this._state === 'enter'
    }

    public hide() {
        this.emit(CoreEventType.HIDE_PNL, this)
    }

    public close() {
        this.emit(CoreEventType.CLOSE_PNL, this)
    }

    public setOpacity(val: number) {
        this.node.opacity = val
        if (this.mask) {
            this.mask.opacity = Math.min(val, 150)
        }
    }

    public showMask(val: boolean) {
        if (this.mask) {
            this.mask.active = val
        }
    }

    public setIndex(index: number) {
        this.node.readyIndex = index
        if (this.mask) {
            this.mask.readyIndex = index - 1
        }
    }

    public setParam(opts: PnlParam) {
        this.isClean = opts.isClean ?? this.isClean
        this.isAct = opts.isAct ?? this.isAct
        this.isMask = opts.isMask ?? this.isMask
        this.adaptHeight = opts.adaptHeight ?? this.adaptHeight
    }
}