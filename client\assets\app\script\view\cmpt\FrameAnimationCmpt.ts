import { _decorator, Component, error, Sprite, SpriteFrame, v3 } from "cc";
import { AnimFrameInfo, FrameAnimConfInfo } from "../../common/constant/DataType";
import { IUpdateModel } from "../../common/constant/interface";

const { ccclass, property } = _decorator;

// 帧动画
@ccclass
export default class FrameAnimationCmpt extends Component {

    @property([SpriteFrame])
    private sfs: SpriteFrame[] = []

    private _sprite: Sprite = null //动画精灵
    private frames: { [key: string]: SpriteFrame } = {}
    private currFrameIndex: number = 0 //当前帧
    private loading: boolean = false
    private conf: FrameAnimConfInfo = null
    private updateModel: IUpdateModel = null //外部更新的模块

    private anim: AnimFrameInfo = null //当前播放的动画配置
    private playInterval: number = 0
    private playElapsed: number = 0
    private playFrameIndex: number = 0
    private playCallback: Function = null //播放回调
    private delayPlayAnim: number = 0 //延迟播放

    private get sprite() {
        if (!this._sprite) {
            this._sprite = this.getComponent(Sprite)
        }
        return this._sprite
    }

    public async init(conf: FrameAnimConfInfo, key: string) {
        this.conf = conf
        this.anim = null
        this.playCallback = null
        this.currFrameIndex = 0
        this.delayPlayAnim = 0
        this.frames = {}
        this.sprite.spriteFrame = null
        this.sfs.forEach(m => this.frames[m.name.split('_').last()] = m)
        await this.loadFrames(this.getAnimConfAllFrames(), key)
    }

    // 设置更新模块
    public setUpdateModel(model: IUpdateModel) {
        this.updateModel = model
        this.updateModel?.addFrameAnimation(this)
    }

    public clean() {
        this.updateModel?.removeFrameAnimation(this.uuid)
        this.updateModel = null
        this.conf = null
        this.frames = {}
        this.anim = null
        this.playCallback = null
        this.currFrameIndex = 0
        this.delayPlayAnim = 0
    }

    onDestroy() {
        this.clean()
    }

    // 获取所有动画需要的帧
    private getAnimConfAllFrames() {
        const frames: string[] = [], obj = {}
        this.conf?.anims.forEach(m => m.frameIndexs.forEach(frame => {
            if (frame !== '00' && !obj[frame] && !this.frames[frame]) {
                obj[frame] = true
                frames.push(frame)
            }
        }))
        return frames
    }

    // 加载所有帧
    private async loadFrames(frames: string[], key: string) {
        if (this.loading || frames.length === 0 || !this.conf) {
            return
        }
        this.loading = true
        const url = this.conf.url
        const sfs = await Promise.all(frames.map(m => assetsMgr.loadTempRes(url + m, SpriteFrame, key)))
        if (this.isValid) {
            sfs.forEach((m, i) => {
                if (m) {
                    this.frames[m.name.split('_').last()] = m
                } else {
                    error('loadFrames error, frame: ' + frames[i] + ', url: ' + url)
                }
            })
            this.loading = false
            this.setFrame(this.currFrameIndex)
        }
    }

    update(dt: number) {
        if (!this.updateModel) {
            this.updateFrame(dt * 1000)
        }
    }

    // 每帧刷新 毫秒
    public updateFrame(dt: number) {
        if (this.loading || !this.anim) {
            return
        } else if (this.delayPlayAnim > 0) {
            this.delayPlayAnim -= dt
            if (this.delayPlayAnim <= 0) {
                this.node.opacity = 255
            }
        } else {
            this.playElapsed += dt
            if (this.playElapsed >= this.playInterval) {
                this.playElapsed -= this.playInterval
                this.setFrame(this.playFrameIndex)
                if (this.playFrameIndex < this.anim.frameIndexs.length - 1) {
                    this.playFrameIndex += 1
                } else if (this.anim.loop) {
                    this.playFrameIndex = 0
                } else {
                    this.anim = null
                    this.playCallback && this.playCallback()
                }
            }
        }
    }

    // 设置一帧
    private setFrame(index: number) {
        this.currFrameIndex = index
        if (this.anim && !this.loading) {
            const name = this.anim.frameIndexs[index]
            if (name) {
                this.sprite.spriteFrame = this.frames[name]
            }
        }
    }

    public get playAnimName() { return this.anim?.name || '' }
    public getAnimConfPositionOffset() { return this.conf?.offset || v3() }

    // 播放动画
    public play(name: string, cb?: Function, startTime?: number) {
        const anim = this.anim = this.conf?.anims.find(m => m.name === name)
        if (!anim) {
            return cb && cb()
        }
        this.playCallback = cb
        this.playInterval = anim.interval || 1
        startTime = startTime || 0
        const index = Math.floor(startTime / this.playInterval)
        this.playFrameIndex = index + 1
        this.playElapsed = startTime % this.playInterval
        this.setFrame(index)
    }

    public delayPlay(delay: number, name: string, cb?: Function, startTime?: number) {
        if (delay > 0) {
            this.delayPlayAnim = delay
        } else {
            this.node.opacity = 255
        }
        this.play(name, cb, startTime)
    }

    public stop() {
        this.setFrame(0)
        this.anim = null
        this.playCallback && this.playCallback()
        this.playCallback = null
    }
}