{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAggC,uCAAhgC,EAAulC,uCAAvlC,EAA2rC,uCAA3rC,EAA4xC,wCAA5xC,EAA43C,wCAA53C,EAA89C,wCAA99C,EAAmkD,wCAAnkD,EAAqqD,wCAArqD,EAAwwD,wCAAxwD,EAA02D,wCAA12D,EAA68D,wCAA78D,EAAijE,wCAAjjE,EAA4pE,wCAA5pE,EAAkwE,wCAAlwE,EAA02E,wCAA12E,EAAg9E,wCAAh9E,EAAujF,wCAAvjF,EAAiqF,wCAAjqF,EAAywF,wCAAzwF,EAA+2F,wCAA/2F,EAAq9F,wCAAr9F,EAA6jG,wCAA7jG,EAAuqG,wCAAvqG,EAA4wG,wCAA5wG,EAAo3G,wCAAp3G,EAAw9G,wCAAx9G,EAA6jH,wCAA7jH,EAA8pH,wCAA9pH,EAAswH,wCAAtwH,EAA42H,wCAA52H,EAAg9H,wCAAh9H,EAAmjI,wCAAnjI,EAA4pI,wCAA5pI,EAAiwI,wCAAjwI,EAA+2I,wCAA/2I,EAAi9I,wCAAj9I,EAAwjJ,wCAAxjJ,EAA6pJ,wCAA7pJ,EAAkwJ,wCAAlwJ,EAAo2J,wCAAp2J,EAAq8J,wCAAr8J,EAAsiK,wCAAtiK,EAA0oK,wCAA1oK,EAAgvK,wCAAhvK,EAAm1K,wCAAn1K,EAAu7K,wCAAv7K,EAA2hL,wCAA3hL,EAA8nL,wCAA9nL,EAA4tL,wCAA5tL,EAA6zL,wCAA7zL,EAA05L,wCAA15L,EAAi/L,wCAAj/L,EAA0kM,wCAA1kM,EAAuqM,wCAAvqM,EAA4wM,wCAA5wM,EAA02M,wCAA12M,EAAg8M,wCAAh8M,EAAsiN,wCAAtiN,EAAypN,wCAAzpN,EAA4wN,wCAA5wN,EAAw3N,wCAAx3N,EAAo+N,wCAAp+N,EAA6kO,wCAA7kO,EAAsrO,wCAAtrO,EAAmyO,wCAAnyO,EAA64O,wCAA74O,EAAs/O,wCAAt/O,EAA+lP,wCAA/lP,EAAwsP,wCAAxsP,EAAozP,wCAApzP,EAAw6P,wCAAx6P,EAAohQ,wCAAphQ,EAA6nQ,wCAA7nQ,EAA0uQ,wCAA1uQ,EAAq1Q,wCAAr1Q,EAAk8Q,wCAAl8Q,EAAgjR,wCAAhjR,EAA2pR,wCAA3pR,EAAowR,wCAApwR,EAAg3R,wCAAh3R,EAA49R,wCAA59R,EAAwkS,wCAAxkS,EAAurS,wCAAvrS,EAAsyS,wCAAtyS,EAAq5S,wCAAr5S,EAAqgT,wCAArgT,EAA+mT,wCAA/mT,EAAytT,wCAAztT,EAAm0T,wCAAn0T,EAA26T,wCAA36T,EAAwhU,wCAAxhU,EAAkoU,wCAAloU,EAA4uU,wCAA5uU,EAAu1U,yCAAv1U,EAA+7U,yCAA/7U,EAAqiV,yCAAriV,EAAgpV,yCAAhpV,EAAyvV,yCAAzvV,EAAm2V,yCAAn2V,EAAy8V,yCAAz8V,EAAmjW,yCAAnjW,EAAmqW,yCAAnqW,EAA6wW,yCAA7wW,EAAs3W,yCAAt3W,EAAi+W,yCAAj+W,EAA2kX,yCAA3kX,EAAirX,yCAAjrX,EAAyxX,yCAAzxX,EAA+3X,yCAA/3X,EAA2+X,yCAA3+X,EAAulY,yCAAvlY,EAAmsY,yCAAnsY,EAAozY,yCAApzY,EAAk6Y,yCAAl6Y,EAAghZ,yCAAhhZ,EAA+nZ,yCAA/nZ,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/App.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/CCMvc.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/base/BaseLayerCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/base/BaseLocale.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/base/BaseModel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/base/BaseMvcCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/base/BaseNoticeCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/base/BasePnlCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/base/BaseViewCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/base/BaseWdtCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/base/BaseWindCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/ButtonEx.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/LabelRollNumber.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/LabelTimer.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/LabelWaitDot.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/LocaleFont.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/LocaleLabel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/LocaleRichText.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/LocaleSprite.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/MultiColor.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/MultiFrame.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/ScrollViewEx.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/component/ScrollViewPlus.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/event/CoreEventType.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendAnimation.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendArray.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendButton.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendCC.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendComponent.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendEditBox.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendLabel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendNode.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendScrollView.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendSprite.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendToggleContainer.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/extend/ExtendVec.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/layer/NoticeLayerCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/layer/ViewLayerCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/layer/WindLayerCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/manage/AssetsMgr.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/manage/AudioMgr.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/manage/ModelMgr.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/manage/NodePoolMgr.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/manage/NoticeCtrlMgr.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/manage/StorageMgr.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/manage/ViewCtrlMgr.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/manage/WindCtrlMgr.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/utils/EventCenter.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/utils/Logger.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/utils/ResLoader.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/core/utils/Utils.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/lib/base64.js\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/lib/mqttws31.js\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/lib/pb/long/long.js\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/lib/pb/protobuf/protobuf.js\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/proto/ProtoHelper.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/proto/msg.js\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/LocalConfig.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/config/HeroFrameAnimConf.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/constant/Constant.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/constant/DataType.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/constant/ECode.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/constant/Enums.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/constant/interface.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/event/EventType.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/event/JsbEvent.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/event/NetEvent.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/event/NotEvent.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/helper/GameHelper.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/helper/LoadProgressHelper.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/common/helper/ViewHelper.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/BTAttack.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/BTRoundBegin.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/BTRoundEnd.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/BehaviorTree.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/FSPController.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/FSPFighter.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/FSPModel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_BTConstant.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_BaseAction.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_BaseBTNode.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_BaseComposite.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_BaseCondition.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_BaseDecorator.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_BevTreeFactory.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_Parallel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_Priority.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/battle/_Sequence.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/common/BuffObj.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/common/NetworkModel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/common/RandomObj.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/common/UserModel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/game/EncounterObj.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/game/GameModel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/game/HeroObj.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/game/HeroStateObj.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/game/MapNodeObj.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/game/PlayerModel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/game/RoleObj.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/model/login/LoginModel.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/AnimPlayCmpt.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/AttrBarCmpt.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/DragTouchCmpt.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/GameWindCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/HeroCmpt.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/MapPnlCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/RoleCmpt.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/login/LoginWindCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/notice/EventNotCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts\"), () => import(\"file:///D:/Projects/auto-chess-client/client/assets/scene/ac.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}