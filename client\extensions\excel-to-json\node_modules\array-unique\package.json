{"name": "array-unique", "description": "Remove duplicate values from an array. Fastest ES5 implementation.", "version": "0.3.2", "homepage": "https://github.com/jonschlinkert/array-unique", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/array-unique", "bugs": {"url": "https://github.com/jonschlinkert/array-unique/issues"}, "license": "MIT", "files": ["index.js", "LICENSE", "README.md"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"array-uniq": "^1.0.2", "benchmarked": "^0.1.3", "gulp-format-md": "^0.1.9", "mocha": "^2.5.3", "should": "^10.0.0"}, "keywords": ["array", "unique"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-diff", "arr-union", "arr-flatten", "arr-reduce", "arr-map", "arr-pluck"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}}