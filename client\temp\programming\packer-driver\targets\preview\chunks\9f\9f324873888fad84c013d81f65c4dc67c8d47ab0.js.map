{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/GameWindCtrl.ts"], "names": ["_decorator", "Prefab", "v2", "v3", "HeroCmpt", "EventType", "AnimPlayCmpt", "RoleCmpt", "RoleObj", "vHelper", "HeroObj", "AreaType", "MapNodeType", "ccclass", "GameWindCtrl", "mc", "BaseWindCtrl", "chessboardNode_", "itemsNode_", "flutterNode_", "animPlay", "encounterArea", "root", "recycleRoot", "areaPositionMap", "recyclePosition", "model", "player", "heros", "<PERSON><PERSON><PERSON>", "myRole", "_temp_vec2_1", "listenEventMaps", "UPDATE_GAME_INFO", "onUpdateGameInfo", "enter", "UPDATE_BATTLE_AREA", "onUpdateBattleArea", "UPDATE_ENCOUNTER_AREA", "onUpdateEncounterArea", "DRAG_HERO_BEGIN", "onDragHeroBegin", "DRAG_HERO_MOVE", "onDragHeroMove", "DRAG_HERO_END", "onDragHeroEnd", "REMOVE_HERO", "onRemoveHero", "PLAY_FLUTTER_HP", "onPlayFlutterHp", "onCreate", "<PERSON><PERSON><PERSON><PERSON>", "node", "addComponent", "children", "for<PERSON>ach", "m", "area", "name", "n", "ut", "convertToNodeAR", "clone", "toVec3", "getModel", "createRole", "init", "key", "onEnter", "data", "playAnimation", "active", "initHeroArea", "initEncounterArea", "onClean", "leave", "clean", "assetsMgr", "releaseTempResByTag", "onClickGoon", "event", "showPnl", "hero", "Child", "opacity", "checkInRecycleRange", "getTempPosition", "heroPosition", "areaType", "BATTLE", "PREPARE", "sellHero", "index", "findMinDisAreaIndex", "restorePosition", "moveHero", "SHOP", "x", "buyHero", "uid", "release", "cleanHero", "remove", "find", "playFlutter", "update", "dt", "<PERSON><PERSON><PERSON><PERSON>", "pfb", "loadTempRes", "getPrefabUrl", "pos", "getPosition", "role", "instantiate", "getComponent", "createHero", "isDie", "resync", "nodePoolMgr", "get", "parent", "Component", "push", "runBattle", "cleanAreaAllHero", "delete", "getHeros", "encounter", "get<PERSON><PERSON>unter", "type", "getData", "Swih", "TAVERN", "length", "ENEMY_BATTLE", "<PERSON><PERSON><PERSON>", "y", "Height", "temp", "minMag", "MAX_VALUE", "_areaType", "_index", "p", "set", "mag", "subtract", "Number", "err", "show<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,M,OAAAA,M;AAAgCC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,E,OAAAA,E;;AACxDC,MAAAA,Q;;AAEAC,MAAAA,S;;AACAC,MAAAA,Y;;AACAC,MAAAA,Q;;AACAC,MAAAA,O;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,O;;AAEEC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;;;;;;;;;OACb;AAAEC,QAAAA;AAAF,O,GAAcb,U;;yBAGCc,Y,GADpBD,O,UAAD,MACqBC,YADrB,SAC0CC,EAAE,CAACC,YAD7C,CAC0D;AAAA;AAAA;AAEtD;AAFsD,eAG9CC,eAH8C,GAGtB,IAHsB;AAGjB;AAHiB,eAI9CC,UAJ8C,GAI3B,IAJ2B;AAItB;AAJsB,eAK9CC,YAL8C,GAKzB,IALyB;AAKpB;AAClC;AANsD,eAQ9CC,QAR8C,GAQrB,IARqB;AAAA,eAS9CC,aAT8C,GASxB,IATwB;AAAA,eAU9CC,IAV8C,GAUjC,IAViC;AAAA,eAW9CC,WAX8C,GAW1B,IAX0B;AAAA,eAY9CC,eAZ8C,GAYgB,EAZhB;AAAA,eAa9CC,eAb8C,GAatBvB,EAAE,EAboB;AAAA,eAe9CwB,KAf8C,GAe3B,IAf2B;AAAA,eAgB9CC,MAhB8C,GAgBxB,IAhBwB;AAAA,eAkB9CC,KAlB8C,GAkB1B,EAlB0B;AAAA,eAmB9CC,OAnB8C,GAmBP,EAnBO;AAAA,eAoB9CC,MApB8C,GAoB3B,IApB2B;AAAA,eAsB9CC,YAtB8C,GAsBzB7B,EAAE,EAtBuB;AAAA;;AAwB/C8B,QAAAA,eAAe,GAAG;AACrB,iBAAO,CACH;AAAE,aAAC;AAAA;AAAA,wCAAUC,gBAAX,GAA8B,KAAKC,gBAArC;AAAuDC,YAAAA,KAAK,EAAE;AAA9D,WADG,EAEH;AAAE,aAAC;AAAA;AAAA,wCAAUC,kBAAX,GAAgC,KAAKC,kBAAvC;AAA2DF,YAAAA,KAAK,EAAE;AAAlE,WAFG,EAGH;AAAE,aAAC;AAAA;AAAA,wCAAUG,qBAAX,GAAmC,KAAKC,qBAA1C;AAAiEJ,YAAAA,KAAK,EAAE;AAAxE,WAHG,EAIH;AAAE,aAAC;AAAA;AAAA,wCAAUK,eAAX,GAA6B,KAAKC,eAApC;AAAqDN,YAAAA,KAAK,EAAE;AAA5D,WAJG,EAKH;AAAE,aAAC;AAAA;AAAA,wCAAUO,cAAX,GAA4B,KAAKC,cAAnC;AAAmDR,YAAAA,KAAK,EAAE;AAA1D,WALG,EAMH;AAAE,aAAC;AAAA;AAAA,wCAAUS,aAAX,GAA2B,KAAKC,aAAlC;AAAiDV,YAAAA,KAAK,EAAE;AAAxD,WANG,EAOH;AAAE,aAAC;AAAA;AAAA,wCAAUW,WAAX,GAAyB,KAAKC,YAAhC;AAA8CZ,YAAAA,KAAK,EAAE;AAArD,WAPG,EAQH;AAAE,aAAC;AAAA;AAAA,wCAAUa,eAAX,GAA6B,KAAKC,eAApC;AAAqDd,YAAAA,KAAK,EAAE;AAA5D,WARG,CAAP;AAUH;;AAEYe,QAAAA,QAAQ,GAAG;AAAA;;AAAA;AACpB,YAAA,KAAI,CAAC7B,aAAL,GAAqB,KAAI,CAACJ,eAAL,CAAqBkC,SAArB,CAA+B,WAA/B,CAArB;AACA,YAAA,KAAI,CAAC7B,IAAL,GAAY,KAAI,CAACL,eAAL,CAAqBkC,SAArB,CAA+B,MAA/B,CAAZ;AACA,YAAA,KAAI,CAAC5B,WAAL,GAAmB,KAAI,CAACN,eAAL,CAAqBkC,SAArB,CAA+B,cAA/B,CAAnB;AACA,YAAA,KAAI,CAAC/B,QAAL,GAAgB,KAAI,CAACgC,IAAL,CAAUC,YAAV;AAAA;AAAA,6CAAhB;AACA,YAAA,KAAI,CAAC7B,eAAL,GAAuB,EAAvB;;AACA,YAAA,KAAI,CAACP,eAAL,CAAqBkC,SAArB,CAA+B,MAA/B,EAAuCG,QAAvC,CAAgDC,OAAhD,CAAwDC,CAAC,IAAI;AACzD,kBAAMC,IAAI,GAAG,KAAI,CAACjC,eAAL,CAAqBgC,CAAC,CAACE,IAAvB,IAA+B,EAA5C;AACAF,cAAAA,CAAC,CAACL,SAAF,CAAY,KAAZ,EAAmBG,QAAnB,CAA4BC,OAA5B,CAAoCI,CAAC,IAAIF,IAAI,CAACE,CAAC,CAACD,IAAH,CAAJ,GAAeE,EAAE,CAACC,eAAH,CAAmBF,CAAnB,EAAsB,KAAI,CAACrC,IAA3B,EAAiCwC,KAAjC,GAAyCC,MAAzC,EAAxD;AACH,aAHD;;AAIAH,YAAAA,EAAE,CAACC,eAAH,CAAmB,KAAI,CAACtC,WAAxB,EAAqC,KAAI,CAACD,IAA1C,EAAgD,KAAI,CAACG,eAArD;AACA,YAAA,KAAI,CAACC,KAAL,GAAa,KAAI,CAACsC,QAAL,CAAc,MAAd,CAAb;AACA,YAAA,KAAI,CAACrC,MAAL,GAAc,KAAI,CAACqC,QAAL,CAAc,QAAd,CAAd;AACA,YAAA,KAAI,CAAClC,MAAL,SAAoB,KAAI,CAACmC,UAAL,CAAgB;AAAA;AAAA,sCAAcC,IAAd,CAAmB,MAAnB,CAAhB,EAA4C,CAA5C,CAApB;AACA,kBAAM,KAAI,CAAC9C,QAAL,CAAc8C,IAAd,CAAmB,KAAI,CAACC,GAAxB,CAAN;AAdoB;AAevB;;AAEMC,QAAAA,OAAO,CAACC,IAAD,EAAY;AACtB,eAAK3C,KAAL,CAAWS,KAAX;AACA,eAAKL,MAAL,CAAYwC,aAAZ,CAA0B,MAA1B;AACA,eAAK/C,WAAL,CAAiBgD,MAAjB,GAA0B,KAA1B;AACA,eAAKC,YAAL;AACA,eAAKC,iBAAL;AACH;;AAEMC,QAAAA,OAAO,GAAG;AACb,eAAKhD,KAAL,CAAWiD,KAAX;AACA,eAAKvD,QAAL,CAAcwD,KAAd;AACA,eAAKxD,QAAL,GAAgB,IAAhB;AACAyD,UAAAA,SAAS,CAACC,mBAAV,CAA8B,KAAKX,GAAnC;AACH,SAnEqD,CAqEtD;AACA;AAEA;;;AACAY,QAAAA,WAAW,CAACC,KAAD,EAAoBX,IAApB,EAAkC;AACzC,eAAKvC,MAAL,CAAYwC,aAAZ,CAA0B,QAA1B,EAAoC,MAAM,KAAKxC,MAAL,CAAYwC,aAAZ,CAA0B,MAA1B,CAA1C;AACA;AAAA;AAAA,kCAAQW,OAAR,CAAgB,UAAhB;AACH,SA5EqD,CA6EtD;AACA;AAEA;;;AACQ/C,QAAAA,gBAAgB,GAAG;AACvB,eAAKsC,YAAL;AACA,eAAKC,iBAAL;AACH,SApFqD,CAsFtD;;;AACQpC,QAAAA,kBAAkB,GAAG;AACzB,eAAKmC,YAAL;AACH,SAzFqD,CA2FtD;;;AACQjC,QAAAA,qBAAqB,GAAG;AAC5B,eAAKkC,iBAAL;AACH,SA9FqD,CAgGtD;;;AACQhC,QAAAA,eAAe,CAACyC,IAAD,EAAiB;AACpC,eAAK3D,WAAL,CAAiBgD,MAAjB,GAA0B,IAA1B;AACH,SAnGqD,CAqGtD;;;AACQ5B,QAAAA,cAAc,CAACuC,IAAD,EAAiB;AACnC,eAAK3D,WAAL,CAAiB4D,KAAjB,CAAuB,IAAvB,EAA6BC,OAA7B,GAAuC,KAAKC,mBAAL,CAAyBH,IAAI,CAACI,eAAL,EAAzB,IAAmD,GAAnD,GAAyD,GAAhG;AACH,SAxGqD,CA0GtD;;;AACQzC,QAAAA,aAAa,CAACqC,IAAD,EAAiB;AAClC,eAAK3D,WAAL,CAAiBgD,MAAjB,GAA0B,KAA1B;AACA,cAAMgB,YAAY,GAAGL,IAAI,CAACI,eAAL,EAArB;AACA,cAAMjB,IAAI,GAAGa,IAAI,CAACb,IAAlB;AAAA,cAAwBmB,QAAQ,GAAGnB,IAAI,CAACmB,QAAxC;;AACA,cAAIA,QAAQ,KAAK;AAAA;AAAA,oCAASC,MAAtB,IAAgCD,QAAQ,KAAK;AAAA;AAAA,oCAASE,OAA1D,EAAmE;AAAE;AACjE,gBAAI,KAAKL,mBAAL,CAAyBE,YAAzB,CAAJ,EAA4C;AACxC,qBAAO,KAAKI,QAAL,CAAcT,IAAd,CAAP,CADwC,CACb;AAC9B;;AACD,gBAAM,CAACM,UAAD,EAAWI,KAAX,IAAoB,KAAKC,mBAAL,CAAyBN,YAAzB,CAA1B;;AACA,gBAAIC,UAAQ,KAAKnB,IAAI,CAACmB,QAAlB,IAA8BI,KAAK,KAAKvB,IAAI,CAACuB,KAAjD,EAAwD;AACpD,qBAAOV,IAAI,CAACY,eAAL,EAAP;AACH;;AACD,iBAAKC,QAAL,CAAcb,IAAd,EAAoBM,UAApB,EAA8BI,KAA9B;AACH,WATD,MASO,IAAIJ,QAAQ,KAAK;AAAA;AAAA,oCAASQ,IAA1B,EAAgC;AAAE;AACrC,gBAAId,IAAI,CAAC9B,IAAL,CAAU6C,CAAV,IAAe,CAAnB,EAAsB;AAAE;AACpB,qBAAOf,IAAI,CAACY,eAAL,EAAP;AACH;;AACD,gBAAM,CAACN,UAAD,EAAWI,OAAX,IAAoB,KAAKC,mBAAL,CAAyBN,YAAzB,CAA1B;;AACA,gBAAIC,UAAQ,KAAKnB,IAAI,CAACmB,QAAlB,IAA8BI,OAAK,KAAKvB,IAAI,CAACuB,KAAjD,EAAwD;AACpD,qBAAOV,IAAI,CAACY,eAAL,EAAP;AACH;;AACD,iBAAKI,OAAL,CAAahB,IAAb,EAAmBM,UAAnB,EAA6BI,OAA7B;AACH;AACJ,SAlIqD,CAoItD;;;AACQ7C,QAAAA,YAAY,CAACoD,GAAD,EAAcC,OAAd,EAAgC;AAChD,eAAKC,SAAL,CAAe,KAAKzE,KAAL,CAAW0E,MAAX,CAAkB,KAAlB,EAAyBH,GAAzB,CAAf,EAA8CC,OAA9C;AACH,SAvIqD,CAyItD;;;AACQnD,QAAAA,eAAe,CAACoB,IAAD,EAAY;AAAA;;AAC/B,cAAMjB,IAAI,uBAAG,KAAKxB,KAAL,CAAW2E,IAAX,CAAgB/C,CAAC,IAAIA,CAAC,CAAC2C,GAAF,KAAU9B,IAAI,CAAC8B,GAApC,CAAH,qBAAG,iBAA0C/C,IAAvD;;AACA,cAAIA,IAAJ,EAAU;AACN,iBAAKhC,QAAL,CAAcoF,WAAd,CAA0BnC,IAA1B,EAAgC,KAAKlD,YAArC,EAAmDyC,EAAE,CAACC,eAAH,CAAmBT,IAAnB,EAAyB,KAAKjC,YAA9B,CAAnD;AACH;AACJ,SA/IqD,CAgJtD;;;AAEAsF,QAAAA,MAAM,CAACC,EAAD,EAAa;AAAA;;AACf,8BAAKhF,KAAL,yBAAY+E,MAAZ,CAAmBC,EAAnB;AACH,SApJqD,CAsJtD;;;AACczC,QAAAA,UAAU,CAACI,IAAD,EAAgBuB,KAAhB,EAA+B;AAAA;;AAAA;AACnD,gBAAI,CAAC,MAAI,CAACe,OAAN,IAAiB,CAACtC,IAAtB,EAA4B;AACxB,qBAAO,IAAP;AACH;;AACD,gBAAMuC,GAAG,SAAS/B,SAAS,CAACgC,WAAV,CAAsBxC,IAAI,CAACyC,YAAL,EAAtB,EAA2C7G,MAA3C,EAAmD,MAAI,CAACkE,GAAxD,CAAlB;;AACA,gBAAI,CAACyC,GAAD,IAAQ,CAAC,MAAI,CAACD,OAAlB,EAA2B;AACvB,qBAAO,IAAP;AACH;;AACD,gBAAMI,GAAG,GAAG,MAAI,CAAC9F,eAAL,CAAqBkE,KAArB,CAA2B,cAAcS,KAAzC,EAAgDoB,WAAhD,EAAZ;;AACA,gBAAMC,IAAI,SAASlG,EAAE,CAACmG,WAAH,CAAeN,GAAf,EAAoB,MAAI,CAAC1F,UAAzB,EAAqCiG,YAArC;AAAA;AAAA,sCAA4DjD,IAA5D,CAAiEG,IAAjE,EAAuE0C,GAAvE,EAA4E,MAAI,CAAC5C,GAAjF,CAAnB;AACA,mBAAO8C,IAAP;AAVmD;AAWtD,SAlKqD,CAoKtD;;;AACcG,QAAAA,UAAU,CAAC/C,IAAD,EAAgB0C,GAAhB,EAA2B;AAAA;;AAAA;AAC/C,gBAAI,CAAC,MAAI,CAACJ,OAAN,IAAiB,CAACtC,IAAtB,EAA4B;AACxB;AACH;;AACD,gBAAIa,IAAI,GAAG,MAAI,CAACtD,KAAL,CAAW2E,IAAX,CAAgB/C,CAAC,IAAIA,CAAC,CAAC2C,GAAF,KAAU9B,IAAI,CAAC8B,GAApC,CAAX;;AACA,gBAAI,CAACjB,IAAL,EAAW,CACV,CADD,MACO,IAAIb,IAAI,CAACgD,KAAL,EAAJ,EAAkB;AACrB,cAAA,MAAI,CAACzF,KAAL,CAAW0E,MAAX,CAAkB,KAAlB,EAAyBjC,IAAI,CAAC8B,GAA9B;;AACA,cAAA,MAAI,CAACE,SAAL,CAAenB,IAAf;;AACA;AACH,aAJM,MAIA;AACH,qBAAOA,IAAI,CAACoC,MAAL,CAAYjD,IAAZ,EAAkB0C,GAAlB,CAAP;AACH;;AACD,gBAAM3D,IAAI,SAASmE,WAAW,CAACC,GAAZ,CAAgB,WAAhB,EAA6B,MAAI,CAACrD,GAAlC,CAAnB;;AACA,gBAAI,CAACf,IAAD,IAAS,CAACA,IAAI,CAACuD,OAAf,IAA0B,CAAC,MAAI,CAACA,OAApC,EAA6C;AACzC;AACH,aAFD,MAEO,IAAI,MAAI,CAAC9E,OAAL,CAAawC,IAAI,CAAC8B,GAAlB,CAAJ,EAA4B;AAC/B,qBAD+B,CACxB;AACV,aAFM,MAEA,IAAI,CAAC9B,IAAD,IAASA,IAAI,CAACgD,KAAL,EAAb,EAA2B;AAC9B;AACH;;AACDjE,YAAAA,IAAI,CAACqE,MAAL,GAAc,MAAI,CAACnG,IAAnB;AACA4D,YAAAA,IAAI,SAAS9B,IAAI,CAACsE,SAAL;AAAA;AAAA,sCAAyBxD,IAAzB,CAA8BG,IAA9B,EAAoC0C,GAApC,EAAyC,MAAI,CAAC5C,GAA9C,CAAb;;AACA,gBAAI,MAAI,CAACwC,OAAT,EAAkB;AACd,cAAA,MAAI,CAAC/E,KAAL,CAAW+F,IAAX,CAAgBzC,IAAhB;;AACA,cAAA,MAAI,CAACrD,OAAL,CAAawC,IAAI,CAAC8B,GAAlB,IAAyBjB,IAAzB;AACH;AA1B8C;AA2BlD;;AAEOmB,QAAAA,SAAS,CAACnB,IAAD,EAAiBkB,OAAjB,EAAoC;AACjD,cAAIlB,IAAJ,EAAU;AACN,mBAAO,KAAKrD,OAAL,CAAaqD,IAAI,CAACiB,GAAlB,CAAP;AACAjB,YAAAA,IAAI,CAACN,KAAL,CAAWwB,OAAX;AACH;AACJ,SAvMqD,CAyMtD;;;AACcwB,QAAAA,SAAS,GAAG,CACtB;AACA;;AAFsB;AAGzB,SA7MqD,CA+MtD;;;AACQC,QAAAA,gBAAgB,GAAG;AACvB,eAAKjG,KAAL,CAAWkG,MAAX,CAAkBtE,CAAC,IAAIA,CAAC,CAACgC,QAAF,KAAe;AAAA;AAAA,oCAASC,MAAxB,IAAkCjC,CAAC,CAACgC,QAAF,KAAe;AAAA;AAAA,oCAASE,OAAjF,EAA0FnC,OAA1F,CAAkGC,CAAC,IAAI,KAAK6C,SAAL,CAAe7C,CAAf,CAAvG;AACH,SAlNqD,CAoNtD;;;AACQgB,QAAAA,YAAY,GAAG;AACnB,eAAKqD,gBAAL;AACA,eAAKlG,MAAL,CAAYoG,QAAZ,GAAuBxE,OAAvB,CAA+BC,CAAC,IAAI,KAAK4D,UAAL,CAAgB5D,CAAhB,EAAmB,KAAKhC,eAAL,CAAqBgC,CAAC,CAACgC,QAAvB,EAAiChC,CAAC,CAACoC,KAAnC,CAAnB,CAApC;AACH,SAxNqD,CA0NtD;;;AACQnB,QAAAA,iBAAiB,GAAG;AACxB,cAAMuD,SAAS,GAAG,KAAKtG,KAAL,CAAWuG,YAAX,EAAlB;AAAA,cAA6CC,IAAI,GAAGF,SAAS,CAACE,IAA9D;AAAA,cAAoE7D,IAAI,GAAG2D,SAAS,CAACG,OAAV,EAA3E;AACA,cAAM/E,IAAI,GAAG,KAAK/B,aAAL,CAAmB+G,IAAnB,CAAwBF,IAAxB,EAA8B,CAA9B,CAAb;;AACA,cAAIA,IAAI,KAAK;AAAA;AAAA,0CAAYG,MAAzB,EAAiC;AAAA;;AAAE;AAC/B,gBAAIhE,IAAJ,2BAAIA,IAAI,CAAEzC,KAAV,aAAI,YAAa0G,MAAjB,EAAyB;AACrBjE,cAAAA,IAAI,CAACzC,KAAL,CAAW2B,OAAX,CAAmBC,CAAC,IAAI;AACpB,oBAAM0B,IAAI,GAAG;AAAA;AAAA,0CAAchB,IAAd,CAAmBV,CAAnB,CAAb;AACA,qBAAK4D,UAAL,CAAgBlC,IAAhB,EAAsBtB,EAAE,CAACC,eAAH,CAAmBT,IAAI,CAAC+B,KAAL,CAAW,SAASD,IAAI,CAACU,KAAzB,CAAnB,EAAoD,KAAKtE,IAAzD,EAA+DwC,KAA/D,GAAuEC,MAAvE,EAAtB;AACH,eAHD;AAIH,aALD,MAKO;AAAE;AACL,mBAAKnC,KAAL,CAAWkG,MAAX,CAAkBtE,CAAC,IAAIA,CAAC,CAACgC,QAAF,KAAe;AAAA;AAAA,wCAASQ,IAA/C,EAAqDzC,OAArD,CAA6DC,CAAC,IAAI,KAAK6C,SAAL,CAAe7C,CAAf,CAAlE;AACH;AACJ,WATD,MASO,IAAI0E,IAAI,KAAK;AAAA;AAAA,0CAAYK,YAAzB,EAAuC,CAAE;AAE/C;AACJ,SA1OqD,CA4OtD;;;AACQlD,QAAAA,mBAAmB,CAAC0B,GAAD,EAAY;AACnC,iBAAOA,GAAG,CAACd,CAAJ,GAAQ,KAAKxE,eAAL,CAAqBwE,CAA7B,IAAkCc,GAAG,CAACd,CAAJ,GAAQ,KAAK1E,WAAL,CAAiBiH,KAA3D,IAAoEzB,GAAG,CAAC0B,CAAJ,GAAQ,KAAKhH,eAAL,CAAqBgH,CAAjG,IAAsG1B,GAAG,CAAC0B,CAAJ,GAAQ,KAAKlH,WAAL,CAAiBmH,MAAtI;AACH,SA/OqD,CAiPtD;;;AACQ7C,QAAAA,mBAAmB,CAACkB,GAAD,EAAY;AACnC,cAAI4B,IAAI,GAAGxI,EAAE,EAAb;AAAA,cAAiByI,MAAM,GAAGhF,EAAE,CAACiF,SAA7B;AAAA,cAAwCrD,QAAQ,GAAG,CAAC,CAApD;AAAA,cAAuDI,KAAK,GAAG,CAAC,CAAhE;;AACA,eAAK,IAAIkD,SAAT,IAAsB,KAAKtH,eAA3B,EAA4C;AACxC,gBAAMiC,IAAI,GAAG,KAAKjC,eAAL,CAAqBsH,SAArB,CAAb;;AACA,iBAAK,IAAIC,MAAT,IAAmBtF,IAAnB,EAAyB;AACrB,kBAAMuF,CAAC,GAAGL,IAAI,CAACM,GAAL,CAASxF,IAAI,CAACsF,MAAD,CAAb,CAAV;AACA,kBAAMG,GAAG,GAAGF,CAAC,CAACG,QAAF,CAAWpC,GAAX,EAAgBuB,MAAhB,EAAZ;;AACA,kBAAIY,GAAG,GAAGN,MAAV,EAAkB;AACdpD,gBAAAA,QAAQ,GAAG4D,MAAM,CAACN,SAAD,CAAjB;AACAlD,gBAAAA,KAAK,GAAGwD,MAAM,CAACL,MAAD,CAAd;AACAH,gBAAAA,MAAM,GAAGM,GAAT;AACH;AACJ;AACJ;;AACD,iBAAO,CAAC1D,QAAD,EAAWI,KAAX,CAAP;AACH,SAjQqD,CAmQtD;;;AACcM,QAAAA,OAAO,CAAChB,IAAD,EAAiBM,QAAjB,EAAmCI,KAAnC,EAAkD;AAAA;;AAAA;AACnE,gBAAMyD,GAAG,SAAS,MAAI,CAAC3H,KAAL,CAAWwE,OAAX,CAAmBhB,IAAI,CAACiB,GAAxB,EAA6BX,QAA7B,EAAuCI,KAAvC,CAAlB;;AACA,gBAAIyD,GAAJ,EAAS;AACLnE,cAAAA,IAAI,CAACY,eAAL;AACA,qBAAO;AAAA;AAAA,sCAAQwD,SAAR,CAAkBD,GAAlB,CAAP;AACH;;AACD,YAAA,MAAI,CAAC7E,YAAL;;AACA,YAAA,MAAI,CAACC,iBAAL;AAPmE;AAQtE,SA5QqD,CA8QtD;;;AACckB,QAAAA,QAAQ,CAACT,IAAD,EAAiB;AAAA;;AAAA;AACnC,gBAAMmE,GAAG,SAAS,MAAI,CAAC3H,KAAL,CAAWiE,QAAX,CAAoBT,IAAI,CAACiB,GAAzB,CAAlB;;AACA,gBAAIkD,GAAJ,EAAS;AACLnE,cAAAA,IAAI,CAACY,eAAL;AACA,qBAAO;AAAA;AAAA,sCAAQwD,SAAR,CAAkBD,GAAlB,CAAP;AACH;;AACD,YAAA,MAAI,CAAC7E,YAAL;AANmC;AAOtC,SAtRqD,CAwRtD;;;AACcuB,QAAAA,QAAQ,CAACb,IAAD,EAAiBM,QAAjB,EAAmCI,KAAnC,EAAkD;AAAA;;AAAA;AACpE,gBAAMyD,GAAG,SAAS,MAAI,CAAC3H,KAAL,CAAWqE,QAAX,CAAoBb,IAAI,CAACiB,GAAzB,EAA8BX,QAA9B,EAAwCI,KAAxC,CAAlB;;AACA,gBAAIyD,GAAJ,EAAS;AACLnE,cAAAA,IAAI,CAACY,eAAL;AACA,qBAAO;AAAA;AAAA,sCAAQwD,SAAR,CAAkBD,GAAlB,CAAP;AACH;;AACD,YAAA,MAAI,CAAC7E,YAAL;AANoE;AAOvE;;AAhSqD,O", "sourcesContent": ["import { _decorator, Node, Prefab, EventTouch, Vec3, Vec2, v2, v3, Size, size } from \"cc\";\r\nimport HeroCmpt from \"./HeroCmpt\";\r\nimport GameModel from \"../../model/game/GameModel\";\r\nimport EventType from \"../../common/event/EventType\";\r\nimport AnimPlayCmpt from \"./AnimPlayCmpt\";\r\nimport RoleCmpt from \"./RoleCmpt\";\r\nimport RoleObj from \"../../model/game/RoleObj\";\r\nimport { vHelper } from \"../../common/helper/ViewHelper\";\r\nimport HeroObj from \"../../model/game/HeroObj\";\r\nimport PlayerModel from \"../../model/game/PlayerModel\";\r\nimport { AreaType, MapNodeType } from \"../../common/constant/Enums\";\r\nconst { ccclass } = _decorator;\r\n\r\n@ccclass\r\nexport default class GameWindCtrl extends mc.BaseWindCtrl {\r\n\r\n    //@autocode property begin\r\n    private chessboardNode_: Node = null // path://chessboard_n\r\n    private itemsNode_: Node = null // path://items_n\r\n    private flutterNode_: Node = null // path://flutter_n\r\n    //@end\r\n\r\n    private animPlay: AnimPlayCmpt = null\r\n    private encounterArea: Node = null\r\n    private root: Node = null\r\n    private recycleRoot: Node = null\r\n    private areaPositionMap: { [key: number]: { [key: number]: Vec3 } } = {}\r\n    private recyclePosition: Vec2 = v2()\r\n\r\n    private model: GameModel = null\r\n    private player: PlayerModel = null\r\n\r\n    private heros: HeroCmpt[] = []\r\n    private heroMap: { [key: string]: HeroCmpt } = {}\r\n    private myRole: RoleCmpt = null\r\n\r\n    private _temp_vec2_1: Vec2 = v2()\r\n\r\n    public listenEventMaps() {\r\n        return [\r\n            { [EventType.UPDATE_GAME_INFO]: this.onUpdateGameInfo, enter: true },\r\n            { [EventType.UPDATE_BATTLE_AREA]: this.onUpdateBattleArea, enter: true },\r\n            { [EventType.UPDATE_ENCOUNTER_AREA]: this.onUpdateEncounterArea, enter: true },\r\n            { [EventType.DRAG_HERO_BEGIN]: this.onDragHeroBegin, enter: true },\r\n            { [EventType.DRAG_HERO_MOVE]: this.onDragHeroMove, enter: true },\r\n            { [EventType.DRAG_HERO_END]: this.onDragHeroEnd, enter: true },\r\n            { [EventType.REMOVE_HERO]: this.onRemoveHero, enter: true },\r\n            { [EventType.PLAY_FLUTTER_HP]: this.onPlayFlutterHp, enter: true },\r\n        ]\r\n    }\r\n\r\n    public async onCreate() {\r\n        this.encounterArea = this.chessboardNode_.FindChild('encounter')\r\n        this.root = this.chessboardNode_.FindChild('root')\r\n        this.recycleRoot = this.chessboardNode_.FindChild('recycle/root')\r\n        this.animPlay = this.node.addComponent(AnimPlayCmpt)\r\n        this.areaPositionMap = {}\r\n        this.chessboardNode_.FindChild('area').children.forEach(m => {\r\n            const area = this.areaPositionMap[m.name] = {}\r\n            m.FindChild('pos').children.forEach(n => area[n.name] = ut.convertToNodeAR(n, this.root).clone().toVec3())\r\n        })\r\n        ut.convertToNodeAR(this.recycleRoot, this.root, this.recyclePosition)\r\n        this.model = this.getModel('game')\r\n        this.player = this.getModel('player')\r\n        this.myRole = await this.createRole(new RoleObj().init(110001), 0)\r\n        await this.animPlay.init(this.key)\r\n    }\r\n\r\n    public onEnter(data: any) {\r\n        this.model.enter()\r\n        this.myRole.playAnimation('idle')\r\n        this.recycleRoot.active = false\r\n        this.initHeroArea()\r\n        this.initEncounterArea()\r\n    }\r\n\r\n    public onClean() {\r\n        this.model.leave()\r\n        this.animPlay.clean()\r\n        this.animPlay = null\r\n        assetsMgr.releaseTempResByTag(this.key)\r\n    }\r\n\r\n    // ----------------------------------------- button listener function -------------------------------------------\r\n    //@autocode button listener\r\n\r\n    // path://chessboard_n/goon_be\r\n    onClickGoon(event: EventTouch, data: string) {\r\n        this.myRole.playAnimation('attack', () => this.myRole.playAnimation('idle'))\r\n        vHelper.showPnl('game/Map')\r\n    }\r\n    //@end\r\n    // ----------------------------------------- event listener function --------------------------------------------\r\n\r\n    // 刷新游戏信息\r\n    private onUpdateGameInfo() {\r\n        this.initHeroArea()\r\n        this.initEncounterArea()\r\n    }\r\n\r\n    // 刷新战斗区域\r\n    private onUpdateBattleArea() {\r\n        this.initHeroArea()\r\n    }\r\n\r\n    // 刷新遭遇区域\r\n    private onUpdateEncounterArea() {\r\n        this.initEncounterArea()\r\n    }\r\n\r\n    // 拖动英雄开始\r\n    private onDragHeroBegin(hero: HeroCmpt) {\r\n        this.recycleRoot.active = true\r\n    }\r\n\r\n    // 拖动英雄移动\r\n    private onDragHeroMove(hero: HeroCmpt) {\r\n        this.recycleRoot.Child('bg').opacity = this.checkInRecycleRange(hero.getTempPosition()) ? 255 : 100\r\n    }\r\n\r\n    // 拖动英雄结束\r\n    private onDragHeroEnd(hero: HeroCmpt) {\r\n        this.recycleRoot.active = false\r\n        const heroPosition = hero.getTempPosition()\r\n        const data = hero.data, areaType = data.areaType\r\n        if (areaType === AreaType.BATTLE || areaType === AreaType.PREPARE) { //英雄区域\r\n            if (this.checkInRecycleRange(heroPosition)) {\r\n                return this.sellHero(hero) //卖出\r\n            }\r\n            const [areaType, index] = this.findMinDisAreaIndex(heroPosition)\r\n            if (areaType === data.areaType && index === data.index) {\r\n                return hero.restorePosition()\r\n            }\r\n            this.moveHero(hero, areaType, index)\r\n        } else if (areaType === AreaType.SHOP) { //商店区域\r\n            if (hero.node.x >= 0) { //没有超过 中线就不算购买\r\n                return hero.restorePosition()\r\n            }\r\n            const [areaType, index] = this.findMinDisAreaIndex(heroPosition)\r\n            if (areaType === data.areaType && index === data.index) {\r\n                return hero.restorePosition()\r\n            }\r\n            this.buyHero(hero, areaType, index)\r\n        }\r\n    }\r\n\r\n    // 删除英雄\r\n    private onRemoveHero(uid: string, release: boolean) {\r\n        this.cleanHero(this.heros.remove('uid', uid), release)\r\n    }\r\n\r\n    // 播放飘字\r\n    private onPlayFlutterHp(data: any) {\r\n        const node = this.heros.find(m => m.uid === data.uid)?.node\r\n        if (node) {\r\n            this.animPlay.playFlutter(data, this.flutterNode_, ut.convertToNodeAR(node, this.flutterNode_))\r\n        }\r\n    }\r\n    // ----------------------------------------- custom function ----------------------------------------------------\r\n\r\n    update(dt: number) {\r\n        this.model?.update(dt)\r\n    }\r\n\r\n    // 创建一个角色\r\n    private async createRole(data: RoleObj, index: number) {\r\n        if (!this.isValid || !data) {\r\n            return null\r\n        }\r\n        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, this.key)\r\n        if (!pfb || !this.isValid) {\r\n            return null\r\n        }\r\n        const pos = this.chessboardNode_.Child('role_pos/' + index).getPosition()\r\n        const role = await mc.instantiate(pfb, this.itemsNode_).getComponent(RoleCmpt).init(data, pos, this.key)\r\n        return role\r\n    }\r\n\r\n    // 创建一个英雄\r\n    private async createHero(data: HeroObj, pos: Vec3) {\r\n        if (!this.isValid || !data) {\r\n            return\r\n        }\r\n        let hero = this.heros.find(m => m.uid === data.uid)\r\n        if (!hero) {\r\n        } else if (data.isDie()) {\r\n            this.heros.remove('uid', data.uid)\r\n            this.cleanHero(hero)\r\n            return\r\n        } else {\r\n            return hero.resync(data, pos)\r\n        }\r\n        const node = await nodePoolMgr.get('hero/HERO', this.key)\r\n        if (!node || !node.isValid || !this.isValid) {\r\n            return\r\n        } else if (this.heroMap[data.uid]) {\r\n            return //防止多次创建\r\n        } else if (!data || data.isDie()) {\r\n            return\r\n        }\r\n        node.parent = this.root\r\n        hero = await node.Component(HeroCmpt).init(data, pos, this.key)\r\n        if (this.isValid) {\r\n            this.heros.push(hero)\r\n            this.heroMap[data.uid] = hero\r\n        }\r\n    }\r\n\r\n    private cleanHero(hero: HeroCmpt, release?: boolean) {\r\n        if (hero) {\r\n            delete this.heroMap[hero.uid]\r\n            hero.clean(release)\r\n        }\r\n    }\r\n\r\n    // 运行战斗\r\n    private async runBattle() {\r\n        // await Promise.all(this.model.getEnemyAnimals().map(m => this.createHero(m, 'enemy')))\r\n        // this.model.battleBegin()\r\n    }\r\n\r\n    // 清理区域所有英雄\r\n    private cleanAreaAllHero() {\r\n        this.heros.delete(m => m.areaType === AreaType.BATTLE || m.areaType === AreaType.PREPARE).forEach(m => this.cleanHero(m))\r\n    }\r\n\r\n    // 初始化英雄区域\r\n    private initHeroArea() {\r\n        this.cleanAreaAllHero()\r\n        this.player.getHeros().forEach(m => this.createHero(m, this.areaPositionMap[m.areaType][m.index]))\r\n    }\r\n\r\n    // 初始化遭遇区域\r\n    private initEncounterArea() {\r\n        const encounter = this.model.getEncounter(), type = encounter.type, data = encounter.getData()\r\n        const node = this.encounterArea.Swih(type)[0]\r\n        if (type === MapNodeType.TAVERN) { //酒馆\r\n            if (data?.heros?.length) {\r\n                data.heros.forEach(m => {\r\n                    const hero = new HeroObj().init(m)\r\n                    this.createHero(hero, ut.convertToNodeAR(node.Child('pos/' + hero.index), this.root).clone().toVec3())\r\n                })\r\n            } else { //清理英雄\r\n                this.heros.delete(m => m.areaType === AreaType.SHOP).forEach(m => this.cleanHero(m))\r\n            }\r\n        } else if (type === MapNodeType.ENEMY_BATTLE) { //野怪\r\n\r\n        }\r\n    }\r\n\r\n    // 检测是否在回收区域\r\n    private checkInRecycleRange(pos: Vec3) {\r\n        return pos.x > this.recyclePosition.x && pos.x < this.recycleRoot.Width && pos.y > this.recyclePosition.y && pos.y < this.recycleRoot.Height\r\n    }\r\n\r\n    // 找出英雄最近的区域位置\r\n    private findMinDisAreaIndex(pos: Vec3) {\r\n        let temp = v3(), minMag = ut.MAX_VALUE, areaType = -1, index = -1\r\n        for (let _areaType in this.areaPositionMap) {\r\n            const area = this.areaPositionMap[_areaType]\r\n            for (let _index in area) {\r\n                const p = temp.set(area[_index])\r\n                const mag = p.subtract(pos).length()\r\n                if (mag < minMag) {\r\n                    areaType = Number(_areaType)\r\n                    index = Number(_index)\r\n                    minMag = mag\r\n                }\r\n            }\r\n        }\r\n        return [areaType, index]\r\n    }\r\n\r\n    // 购买英雄\r\n    private async buyHero(hero: HeroCmpt, areaType: number, index: number) {\r\n        const err = await this.model.buyHero(hero.uid, areaType, index)\r\n        if (err) {\r\n            hero.restorePosition()\r\n            return vHelper.showAlert(err)\r\n        }\r\n        this.initHeroArea()\r\n        this.initEncounterArea()\r\n    }\r\n\r\n    // 出售英雄\r\n    private async sellHero(hero: HeroCmpt) {\r\n        const err = await this.model.sellHero(hero.uid)\r\n        if (err) {\r\n            hero.restorePosition()\r\n            return vHelper.showAlert(err)\r\n        }\r\n        this.initHeroArea()\r\n    }\r\n\r\n    // 移动英雄\r\n    private async moveHero(hero: HeroCmpt, areaType: number, index: number) {\r\n        const err = await this.model.moveHero(hero.uid, areaType, index)\r\n        if (err) {\r\n            hero.restorePosition()\r\n            return vHelper.showAlert(err)\r\n        }\r\n        this.initHeroArea()\r\n    }\r\n}\r\n"]}