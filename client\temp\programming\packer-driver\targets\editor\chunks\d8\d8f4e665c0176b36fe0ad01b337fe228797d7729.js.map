{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/model/game/PlayerModel.ts"], "names": ["HeroObj", "PlayerModel", "mc", "addmodel", "BaseModel", "net", "battleAreas", "<PERSON><PERSON><PERSON><PERSON>", "roleId", "day", "hour", "hp", "winCount", "gold", "earnings", "onCreate", "getModel", "initBaseData", "data", "init", "updateAreaHero", "initAreaData", "areas", "key", "clean", "getDay", "getBattleAreas", "updateGold", "val", "getHeros", "heros", "k", "hero", "push"], "mappings": ";;;;;;;;;;;;;;;;;;;AACOA,MAAAA,O;;;;;;;AAEP;AACA;AACA;yBAEqBC,W,WADpBC,EAAE,CAACC,QAAH,CAAY,QAAZ,C,gBAAD,MACqBF,WADrB,SACyCC,EAAE,CAACE,SAD5C,CACsD;AAAA;AAAA;AAAA,eAE1CC,GAF0C,GAEtB,IAFsB;AAAA,eAI1CC,WAJ0C,GAIA,EAJA;AAIG;AAJH,eAK1CC,YAL0C,GAKC,EALD;AAKI;AALJ,eAM1CC,MAN0C,GAMzB,CANyB;AAMvB;AANuB,eAO1CC,GAP0C,GAO5B,CAP4B;AAO1B;AAP0B,eAQ1CC,IAR0C,GAQ3B,CAR2B;AAQzB;AARyB,eAS1CC,EAT0C,GAS7B,CAT6B;AAS3B;AAT2B,eAU1CC,QAV0C,GAUvB,CAVuB;AAUrB;AAVqB,eAW1CC,IAX0C,GAW3B,CAX2B;AAWzB;AAXyB,eAY1CC,QAZ0C,GAYvB,CAZuB;AAAA;;AAYrB;AAEtBC,QAAAA,QAAQ,GAAG;AACd,eAAKV,GAAL,GAAW,KAAKW,QAAL,CAAc,KAAd,CAAX;AACH;;AAEMC,QAAAA,YAAY,CAACC,IAAD,EAAY;AAC3B,eAAKT,GAAL,GAAWS,IAAI,CAACT,GAAL,IAAY,CAAvB,CAD2B,CACF;;AACzB,eAAKE,EAAL,GAAUO,IAAI,CAACP,EAAf;AACA,eAAKC,QAAL,GAAgBM,IAAI,CAACN,QAAL,IAAiB,CAAjC,CAH2B,CAGQ;AACtC;;AAEMO,QAAAA,IAAI,CAACD,IAAD,EAAY;AACnB,eAAKV,MAAL,GAAcU,IAAI,CAACV,MAAL,IAAe,CAA7B,CADmB,CACY;;AAC/B,eAAKC,GAAL,GAAWS,IAAI,CAACT,GAAL,IAAY,CAAvB,CAFmB,CAEM;;AACzB,eAAKC,IAAL,GAAYQ,IAAI,CAACR,IAAL,IAAa,CAAzB,CAHmB,CAGQ;;AAC3B,eAAKC,EAAL,GAAUO,IAAI,CAACP,EAAL,IAAW,CAArB,CAJmB,CAII;;AACvB,eAAKC,QAAL,GAAgBM,IAAI,CAACN,QAAL,IAAiB,CAAjC,CALmB,CAKgB;;AACnC,eAAKC,IAAL,GAAYK,IAAI,CAACL,IAAL,IAAa,CAAzB,CANmB,CAMQ;;AAC3B,eAAKC,QAAL,GAAgBI,IAAI,CAACJ,QAAL,IAAiB,CAAjC,CAPmB,CAOgB;;AACnC,eAAKM,cAAL,CAAoBF,IAApB,EARmB,CAQO;AAC7B;;AAEMG,QAAAA,YAAY,CAACH,IAAD,EAAY;AAC3B,gBAAMI,KAAiC,GAAG,EAA1C;;AACA,eAAK,IAAIC,GAAT,IAAgBL,IAAhB,EAAsB;AAClBI,YAAAA,KAAK,CAACC,GAAD,CAAL,GAAa;AAAA;AAAA,sCAAcJ,IAAd,CAAmBD,IAAI,CAACK,GAAD,CAAvB,CAAb,CADkB,CACyB;AAC9C;;AACD,iBAAOD,KAAP;AACH;;AAEME,QAAAA,KAAK,GAAG;AACX,eAAKf,GAAL,GAAW,CAAX;AACH;;AAEMgB,QAAAA,MAAM,GAAG;AAAE,iBAAO,KAAKhB,GAAZ;AAAiB;;AAC5BiB,QAAAA,cAAc,GAAG;AAAE,iBAAO,KAAKpB,WAAZ;AAAyB;;AAE5CqB,QAAAA,UAAU,CAACC,GAAD,EAAc;AAC3B,eAAKf,IAAL,GAAYe,GAAZ;AACH,SApDiD,CAsDlD;;;AACOC,QAAAA,QAAQ,GAAG;AACd,gBAAMC,KAAgB,GAAG,EAAzB;;AACA,eAAK,IAAIC,CAAT,IAAc,KAAKzB,WAAnB,EAAgC;AAC5B,kBAAM0B,IAAI,GAAG,KAAK1B,WAAL,CAAiByB,CAAjB,CAAb;;AACA,gBAAIC,IAAJ,EAAU;AACNF,cAAAA,KAAK,CAACG,IAAN,CAAWD,IAAX;AACH;AACJ;;AACD,eAAK,IAAID,CAAT,IAAc,KAAKxB,YAAnB,EAAiC;AAC7B,kBAAMyB,IAAI,GAAG,KAAKzB,YAAL,CAAkBwB,CAAlB,CAAb;;AACA,gBAAIC,IAAJ,EAAU;AACNF,cAAAA,KAAK,CAACG,IAAN,CAAWD,IAAX;AACH;AACJ;;AACD,iBAAOF,KAAP;AACH,SAtEiD,CAwElD;;;AACOV,QAAAA,cAAc,CAACF,IAAD,EAAY;AAC7B,eAAKZ,WAAL,GAAmB,KAAKe,YAAL,CAAkBH,IAAI,CAACZ,WAAL,IAAoB,EAAtC,CAAnB,CAD6B,CACgC;;AAC7D,eAAKC,YAAL,GAAoB,KAAKc,YAAL,CAAkBH,IAAI,CAACX,YAAL,IAAqB,EAAvC,CAApB,CAF6B,CAEkC;AAClE;;AA5EiD,O", "sourcesContent": ["import NetworkModel from \"../common/NetworkModel\"\r\nimport HeroObj from \"./HeroObj\"\r\n\r\n/**\r\n * 玩家信息\r\n */\r\*************('player')\r\nexport default class PlayerModel extends mc.BaseModel {\r\n\r\n    private net: NetworkModel = null\r\n\r\n    private battleAreas: { [key: number]: <PERSON>Obj } = {} //战斗区域\r\n    private prepareAreas: { [key: number]: HeroObj } = {} //备战区域\r\n    private roleId: number = 0 //角色id\r\n    private day: number = 0 //天数\r\n    private hour: number = 0 //小时\r\n    private hp: number = 0 //血量\r\n    private winCount: number = 0 //胜利次数\r\n    private gold: number = 0 //金币\r\n    private earnings: number = 0 //收益\r\n\r\n    public onCreate() {\r\n        this.net = this.getModel('net')\r\n    }\r\n\r\n    public initBaseData(data: any) {\r\n        this.day = data.day || 0 //天数\r\n        this.hp = data.hp\r\n        this.winCount = data.winCount || 0 //胜利次数\r\n    }\r\n\r\n    public init(data: any) {\r\n        this.roleId = data.roleId || 0 //角色id\r\n        this.day = data.day || 0 //天数\r\n        this.hour = data.hour || 0 //小时\r\n        this.hp = data.hp || 0 //血量\r\n        this.winCount = data.winCount || 0 //胜利次数\r\n        this.gold = data.gold || 0 //金币\r\n        this.earnings = data.earnings || 0 //收益\r\n        this.updateAreaHero(data) //刷新区域英雄信息\r\n    }\r\n\r\n    public initAreaData(data: any) {\r\n        const areas: { [key: number]: HeroObj } = {}\r\n        for (let key in data) {\r\n            areas[key] = new HeroObj().init(data[key]) //初始化武将数据\r\n        }\r\n        return areas\r\n    }\r\n\r\n    public clean() {\r\n        this.day = 0\r\n    }\r\n\r\n    public getDay() { return this.day }\r\n    public getBattleAreas() { return this.battleAreas }\r\n\r\n    public updateGold(val: number) {\r\n        this.gold = val\r\n    }\r\n\r\n    // 获取英雄列表\r\n    public getHeros() {\r\n        const heros: HeroObj[] = []\r\n        for (let k in this.battleAreas) {\r\n            const hero = this.battleAreas[k]\r\n            if (hero) {\r\n                heros.push(hero)\r\n            }\r\n        }\r\n        for (let k in this.prepareAreas) {\r\n            const hero = this.prepareAreas[k]\r\n            if (hero) {\r\n                heros.push(hero)\r\n            }\r\n        }\r\n        return heros\r\n    }\r\n\r\n    // 刷新区域英雄信息\r\n    public updateAreaHero(data: any) {\r\n        this.battleAreas = this.initAreaData(data.battleAreas || []) //战斗区域\r\n        this.prepareAreas = this.initAreaData(data.prepareAreas || []) //备战区域\r\n    }\r\n}"]}