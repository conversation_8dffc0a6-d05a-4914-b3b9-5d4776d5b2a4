System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, CLICK_SPACE, FSP_PLAY_MUL, MOBILE_TOUCH_HEIGHT, PC_TOUCH_HEIGHT;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5ac3eWCQj1BJZ4DQumqhnwQ", "Constant", undefined);

      // 点击间隔
      _export("CLICK_SPACE", CLICK_SPACE = 10); // 战斗播放倍数


      _export("FSP_PLAY_MUL", FSP_PLAY_MUL = [{
        val: 4,
        text: '0.25x'
      }, {
        val: 2,
        text: '0.5x'
      }, {
        val: 1,
        text: '1x'
      }, {
        val: 0.5,
        text: '2x'
      }, {
        val: 0.25,
        text: '4x'
      }]); // 移动端点击士兵之后抬起的高度


      _export("MOBILE_TOUCH_HEIGHT", MOBILE_TOUCH_HEIGHT = 40); // PC端点击士兵之后抬起的高度


      _export("PC_TOUCH_HEIGHT", PC_TOUCH_HEIGHT = 4);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3b69d3d23708cea265e6d9bc57b16bd409f6df96.js.map