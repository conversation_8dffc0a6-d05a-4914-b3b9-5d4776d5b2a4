/* cfb.js (C) 2013-present SheetJS -- http://sheetjs.com */
var Base64=function e(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return{encode:function(r){var t="";var i=0,n=0,f=0,a=0,s=0,h=0,l=0;for(var o=0;o<r.length;){i=r.charCodeAt(o++);a=i>>2;n=r.charCodeAt(o++);s=(i&3)<<4|n>>4;f=r.charCodeAt(o++);h=(n&15)<<2|f>>6;l=f&63;if(isNaN(n)){h=l=64}else if(isNaN(f)){l=64}t+=e.charAt(a)+e.charAt(s)+e.charAt(h)+e.charAt(l)}return t},decode:function r(t){var i="";var n=0,f=0,a=0,s=0,h=0,l=0,o=0;t=t.replace(/[^\w\+\/\=]/g,"");for(var u=0;u<t.length;){s=e.indexOf(t.charAt(u++));h=e.indexOf(t.charAt(u++));n=s<<2|h>>4;i+=String.fromCharCode(n);l=e.indexOf(t.charAt(u++));f=(h&15)<<4|l>>2;if(l!==64){i+=String.fromCharCode(f)}o=e.indexOf(t.charAt(u++));a=(l&3)<<6|o;if(o!==64){i+=String.fromCharCode(a)}}return i}}}();var has_buf=typeof Buffer!=="undefined"&&typeof process!=="undefined"&&typeof process.versions!=="undefined"&&process.versions.node;var Buffer_from=function(){};if(typeof Buffer!=="undefined"){var nbfs=!Buffer.from;if(!nbfs)try{Buffer.from("foo","utf8")}catch(e){nbfs=true}Buffer_from=nbfs?function(e,r){return r?new Buffer(e,r):new Buffer(e)}:Buffer.from.bind(Buffer);if(!Buffer.alloc)Buffer.alloc=function(e){return new Buffer(e)}}function new_raw_buf(e){return has_buf?Buffer.alloc(e):new Array(e)}var s2a=function r(e){if(has_buf)return Buffer.from(e,"binary");return e.split("").map(function(e){return e.charCodeAt(0)&255})};var chr0=/\u0000/g,chr1=/[\u0001-\u0006]/g;var __toBuffer=function(e){var r=[];for(var t=0;t<e[0].length;++t){r.push.apply(r,e[0][t])}return r};var ___toBuffer=__toBuffer;var __utf16le=function(e,r,t){var i=[];for(var n=r;n<t;n+=2)i.push(String.fromCharCode(__readUInt16LE(e,n)));return i.join("").replace(chr0,"")};var ___utf16le=__utf16le;var __hexlify=function(e,r,t){var i=[];for(var n=r;n<r+t;++n)i.push(("0"+e[n].toString(16)).slice(-2));return i.join("")};var ___hexlify=__hexlify;var __bconcat=function(e){if(Array.isArray(e[0]))return[].concat.apply([],e);var r=0,t=0;for(t=0;t<e.length;++t)r+=e[t].length;var i=new Uint8Array(r);for(t=0,r=0;t<e.length;r+=e[t].length,++t)i.set(e[t],r);return i};var bconcat=__bconcat;if(has_buf){__utf16le=function(e,r,t){if(!Buffer.isBuffer(e))return ___utf16le(e,r,t);return e.toString("utf16le",r,t).replace(chr0,"")};__hexlify=function(e,r,t){return Buffer.isBuffer(e)?e.toString("hex",r,r+t):___hexlify(e,r,t)};__toBuffer=function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0]):___toBuffer(e)};s2a=function(e){return Buffer_from(e,"binary")};bconcat=function(e){return Buffer.isBuffer(e[0])?Buffer.concat(e):__bconcat(e)}}var __readUInt8=function(e,r){return e[r]};var __readUInt16LE=function(e,r){return e[r+1]*(1<<8)+e[r]};var __readInt16LE=function(e,r){var t=e[r+1]*(1<<8)+e[r];return t<32768?t:(65535-t+1)*-1};var __readUInt32LE=function(e,r){return e[r+3]*(1<<24)+(e[r+2]<<16)+(e[r+1]<<8)+e[r]};var __readInt32LE=function(e,r){return(e[r+3]<<24)+(e[r+2]<<16)+(e[r+1]<<8)+e[r]};function ReadShift(e,r){var t,i,n=0;switch(e){case 1:t=__readUInt8(this,this.l);break;case 2:t=(r!=="i"?__readUInt16LE:__readInt16LE)(this,this.l);break;case 4:t=__readInt32LE(this,this.l);break;case 16:n=2;i=__hexlify(this,this.l,e);}this.l+=e;if(n===0)return t;return i}var __writeUInt32LE=function(e,r,t){e[t]=r&255;e[t+1]=r>>>8&255;e[t+2]=r>>>16&255;e[t+3]=r>>>24&255};var __writeInt32LE=function(e,r,t){e[t]=r&255;e[t+1]=r>>8&255;e[t+2]=r>>16&255;e[t+3]=r>>24&255};function WriteShift(e,r,t){var i=0,n=0;switch(t){case"hex":for(;n<e;++n){this[this.l++]=parseInt(r.slice(2*n,2*n+2),16)||0}return this;case"utf16le":var f=this.l+e;for(n=0;n<Math.min(r.length,e);++n){var a=r.charCodeAt(n);this[this.l++]=a&255;this[this.l++]=a>>8}while(this.l<f)this[this.l++]=0;return this;}switch(e){case 1:i=1;this[this.l]=r&255;break;case 2:i=2;this[this.l]=r&255;r>>>=8;this[this.l+1]=r&255;break;case 4:i=4;__writeUInt32LE(this,r,this.l);break;case-4:i=4;__writeInt32LE(this,r,this.l);break;}this.l+=i;return this}function CheckField(e,r){var t=__hexlify(this,this.l,e.length>>1);if(t!==e)throw new Error(r+"Expected "+e+" saw "+t);this.l+=e.length>>1}function prep_blob(e,r){e.l=r;e.read_shift=ReadShift;e.chk=CheckField;e.write_shift=WriteShift}function new_buf(e){var r=new_raw_buf(e);prep_blob(r,0);return r}var CFB=function t(){var e={};e.version="1.0.8";function r(e,r){var t=e.split("/"),i=r.split("/");for(var n=0,f=0,a=Math.min(t.length,i.length);n<a;++n){if(f=t[n].length-i[n].length)return f;if(t[n]!=i[n])return t[n]<i[n]?-1:1}return t.length-i.length}function t(e){if(e.charAt(e.length-1)=="/")return e.slice(0,-1).indexOf("/")===-1?e:t(e.slice(0,-1));var r=e.lastIndexOf("/");return r===-1?e:e.slice(0,r+1)}function i(e){if(e.charAt(e.length-1)=="/")return i(e.slice(0,-1));var r=e.lastIndexOf("/");return r===-1?e:e.slice(r+1)}var n;function f(){return n||(n=require("fs"))}function a(e,r){if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var t=3;var i=512;var n=0;var f=0;var a=0;var u=0;var _=0;var w=[];var p=e.slice(0,512);prep_blob(p,0);var F=s(p);t=F[0];switch(t){case 3:i=512;break;case 4:i=4096;break;default:throw new Error("Major Version: Expected 3 or 4 saw "+t);}if(i!==512){p=e.slice(0,i);prep_blob(p,28)}var g=e.slice(0,i);h(p,t);var I=p.read_shift(4,"i");if(t===3&&I!==0)throw new Error("# Directory Sectors: Expected 0 saw "+I);p.l+=4;a=p.read_shift(4,"i");p.l+=4;p.chk("00100000","Mini Stream Cutoff Size: ");u=p.read_shift(4,"i");n=p.read_shift(4,"i");_=p.read_shift(4,"i");f=p.read_shift(4,"i");for(var b=-1,x=0;x<109;++x){b=p.read_shift(4,"i");if(b<0)break;w[x]=b}var E=l(e,i);c(_,f,E,i,w);var C=d(E,a,w,i);C[a].name="!Directory";if(n>0&&u!==y)C[u].name="!MiniFAT";C[w[0]].name="!FAT";C.fat_addrs=w;C.ssz=i;var S={},B=[],m=[],A=[];v(a,C,E,B,n,S,m,u);o(m,A,B);B.shift();var L={FileIndex:m,FullPaths:A};if(r&&r.raw)L.raw={header:g,sectors:E};return L}function s(e){e.chk(S,"Header Signature: ");e.chk(m,"CLSID: ");var r=e.read_shift(2,"u");return[e.read_shift(2,"u"),r]}function h(e,r){var t=9;e.l+=2;switch(t=e.read_shift(2)){case 9:if(r!=3)throw new Error("Sector Shift: Expected 9 saw "+t);break;case 12:if(r!=4)throw new Error("Sector Shift: Expected 12 saw "+t);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+t);}e.chk("0600","Mini Sector Shift: ");e.chk("000000000000","Reserved: ")}function l(e,r){var t=Math.ceil(e.length/r)-1;var i=[];for(var n=1;n<t;++n)i[n-1]=e.slice(n*r,(n+1)*r);i[t-1]=e.slice(t*r);return i}function o(e,r,t){var i=0,n=0,f=0,a=0,s=0,h=t.length;var l=[],o=[];for(;i<h;++i){l[i]=o[i]=i;r[i]=t[i]}for(;s<o.length;++s){i=o[s];n=e[i].L;f=e[i].R;a=e[i].C;if(l[i]===i){if(n!==-1&&l[n]!==n)l[i]=l[n];if(f!==-1&&l[f]!==f)l[i]=l[f]}if(a!==-1)l[a]=i;if(n!==-1){l[n]=l[i];if(o.lastIndexOf(n)<s)o.push(n)}if(f!==-1){l[f]=l[i];if(o.lastIndexOf(f)<s)o.push(f)}}for(i=1;i<h;++i)if(l[i]===i){if(f!==-1&&l[f]!==f)l[i]=l[f];else if(n!==-1&&l[n]!==n)l[i]=l[n]}for(i=1;i<h;++i){if(e[i].type===0)continue;s=l[i];if(s===0)r[i]=r[0]+"/"+r[i];else while(s!==0&&s!==l[s]){r[i]=r[s]+"/"+r[i];s=l[s]}l[i]=0}r[0]+="/";for(i=1;i<h;++i){if(e[i].type!==2)r[i]+="/"}}function u(e,r,t){var i=e.start,n=e.size;var f=[];var a=i;while(t&&n>0&&a>=0){f.push(r.slice(a*C,a*C+C));n-=C;a=__readInt32LE(t,a*4)}if(f.length===0)return new_buf(0);return bconcat(f).slice(0,e.size)}function c(e,r,t,i,n){var f=y;if(e===y){if(r!==0)throw new Error("DIFAT chain shorter than expected")}else if(e!==-1){var a=t[e],s=(i>>>2)-1;if(!a)return;for(var h=0;h<s;++h){if((f=__readInt32LE(a,h*4))===y)break;n.push(f)}c(__readInt32LE(a,i-4),r-1,t,i,n)}}function _(e,r,t,i,n){var f=[],a=[];if(!n)n=[];var s=i-1,h=0,l=0;for(h=r;h>=0;){n[h]=true;f[f.length]=h;a.push(e[h]);var o=t[Math.floor(h*4/i)];l=h*4&s;if(i<4+l)throw new Error("FAT boundary crossed: "+h+" 4 "+i);if(!e[o])break;h=__readInt32LE(e[o],l)}return{nodes:f,data:__toBuffer([a])}}function d(e,r,t,i){var n=e.length,f=[];var a=[],s=[],h=[];var l=i-1,o=0,u=0,c=0,_=0;for(o=0;o<n;++o){s=[];c=o+r;if(c>=n)c-=n;if(a[c])continue;h=[];for(u=c;u>=0;){a[u]=true;s[s.length]=u;h.push(e[u]);var d=t[Math.floor(u*4/i)];_=u*4&l;if(i<4+_)throw new Error("FAT boundary crossed: "+u+" 4 "+i);if(!e[d])break;u=__readInt32LE(e[d],_)}f[c]={nodes:s,data:__toBuffer([h])}}return f}function v(e,r,t,i,n,f,a,s){var h=0,l=i.length?2:0;var o=r[e].data;var c=0,d=0,v;for(;c<o.length;c+=128){var p=o.slice(c,c+128);prep_blob(p,64);d=p.read_shift(2);v=__utf16le(p,0,d-l);i.push(v);var F={name:v,type:p.read_shift(1),color:p.read_shift(1),L:p.read_shift(4,"i"),R:p.read_shift(4,"i"),C:p.read_shift(4,"i"),clsid:p.read_shift(16),state:p.read_shift(4,"i"),start:0,size:0};var g=p.read_shift(2)+p.read_shift(2)+p.read_shift(2)+p.read_shift(2);if(g!==0)F.ct=w(p,p.l-8);var I=p.read_shift(2)+p.read_shift(2)+p.read_shift(2)+p.read_shift(2);if(I!==0)F.mt=w(p,p.l-8);F.start=p.read_shift(4,"i");F.size=p.read_shift(4,"i");if(F.size<0&&F.start<0){F.size=F.type=0;F.start=y;F.name=""}if(F.type===5){h=F.start;if(n>0&&h!==y)r[h].name="!StreamData"}else if(F.size>=4096){F.storage="fat";if(r[F.start]===undefined)r[F.start]=_(t,F.start,r.fat_addrs,r.ssz);r[F.start].name=F.name;F.content=r[F.start].data.slice(0,F.size)}else{F.storage="minifat";if(F.size<0)F.size=0;else if(h!==y&&F.start!==y&&r[h]){F.content=u(F,r[h].data,(r[s]||{}).data)}}if(F.content)prep_blob(F.content,0);f[v]=F;a.push(F)}}function w(e,r){return new Date((__readUInt32LE(e,r+4)/1e7*Math.pow(2,32)+__readUInt32LE(e,r)/1e7-11644473600)*1e3)}function p(e,r){f();return a(n.readFileSync(e),r)}function F(e,r){switch(r&&r.type||"base64"){case"file":return p(e,r);case"base64":return a(s2a(Base64.decode(e)),r);case"binary":return a(s2a(e),r);}return a(e,r)}function g(e,r){var t=r||{},i=t.root||"Root Entry";if(!e.FullPaths)e.FullPaths=[];if(!e.FileIndex)e.FileIndex=[];if(e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");if(e.FullPaths.length===0){e.FullPaths[0]=i+"/";e.FileIndex[0]={name:i,type:5}}if(t.CLSID)e.FileIndex[0].clsid=t.CLSID;I(e)}function I(e){var r="Sh33tJ5";if(CFB.find(e,"/"+r))return;var t=new_buf(4);t[0]=55;t[1]=t[3]=50;t[2]=54;e.FileIndex.push({name:r,type:2,content:t,size:4,L:69,R:69,C:69});e.FullPaths.push(e.FullPaths[0]+r);b(e)}function b(e,n){g(e);var f=false,a=false;for(var s=e.FullPaths.length-1;s>=0;--s){var h=e.FileIndex[s];switch(h.type){case 0:if(a)f=true;else{e.FileIndex.pop();e.FullPaths.pop()}break;case 1:;case 2:;case 5:a=true;if(isNaN(h.R*h.L*h.C))f=true;if(h.R>-1&&h.L>-1&&h.R==h.L)f=true;break;default:f=true;break;}}if(!f&&!n)return;var l=new Date(1987,1,19),o=0;var u=[];for(s=0;s<e.FullPaths.length;++s){if(e.FileIndex[s].type===0)continue;u.push([e.FullPaths[s],e.FileIndex[s]])}for(s=0;s<u.length;++s){var c=t(u[s][0]);a=false;for(o=0;o<u.length;++o)if(u[o][0]===c)a=true;if(!a)u.push([c,{name:i(c).replace("/",""),type:1,clsid:m,ct:l,mt:l,content:null}])}u.sort(function(e,t){return r(e[0],t[0])});e.FullPaths=[];e.FileIndex=[];for(s=0;s<u.length;++s){e.FullPaths[s]=u[s][0];e.FileIndex[s]=u[s][1]}for(s=0;s<u.length;++s){var _=e.FileIndex[s];var d=e.FullPaths[s];_.name=i(d).replace("/","");_.L=_.R=_.C=-(_.color=1);_.size=_.content?_.content.length:0;_.start=0;_.clsid=_.clsid||m;if(s===0){_.C=u.length>1?1:-1;_.size=0;_.type=5}else if(d.slice(-1)=="/"){for(o=s+1;o<u.length;++o)if(t(e.FullPaths[o])==d)break;_.C=o>=u.length?-1:o;for(o=s+1;o<u.length;++o)if(t(e.FullPaths[o])==t(d))break;_.R=o>=u.length?-1:o;_.type=1}else{if(t(e.FullPaths[s+1]||"")==t(d))_.R=s+1;_.type=2}}}function x(e,r){var t=r||{};b(e);var i=function(e){var r=0,t=0;for(var i=0;i<e.FileIndex.length;++i){var n=e.FileIndex[i];if(!n.content)continue;var f=n.content.length;if(f>0){if(f<4096)r+=f+63>>6;else t+=f+511>>9}}var a=e.FullPaths.length+3>>2;var s=r+7>>3;var h=r+127>>7;var l=s+t+a+h;var o=l+127>>7;var u=o<=109?0:Math.ceil((o-109)/127);while(l+o+u+127>>7>o)u=++o<=109?0:Math.ceil((o-109)/127);var c=[1,u,o,h,a,t,r,0];e.FileIndex[0].size=r<<6;c[7]=(e.FileIndex[0].start=c[0]+c[1]+c[2]+c[3]+c[4]+c[5])+(c[6]+7>>3);return c}(e);var n=new_buf(i[7]<<9);var f=0,a=0;{for(f=0;f<8;++f)n.write_shift(1,B[f]);for(f=0;f<8;++f)n.write_shift(2,0);n.write_shift(2,62);n.write_shift(2,3);n.write_shift(2,65534);n.write_shift(2,9);n.write_shift(2,6);for(f=0;f<3;++f)n.write_shift(2,0);n.write_shift(4,0);n.write_shift(4,i[2]);n.write_shift(4,i[0]+i[1]+i[2]+i[3]-1);n.write_shift(4,0);n.write_shift(4,1<<12);n.write_shift(4,i[3]?i[0]+i[1]+i[2]-1:y);n.write_shift(4,i[3]);n.write_shift(-4,i[1]?i[0]-1:y);n.write_shift(4,i[1]);for(f=0;f<109;++f)n.write_shift(-4,f<i[2]?i[1]+f:-1)}if(i[1]){for(a=0;a<i[1];++a){for(;f<236+a*127;++f)n.write_shift(-4,f<i[2]?i[1]+f:-1);n.write_shift(-4,a===i[1]-1?y:a+1)}}var s=function(e){for(a+=e;f<a-1;++f)n.write_shift(-4,f+1);if(e){++f;n.write_shift(-4,y)}};a=f=0;for(a+=i[1];f<a;++f)n.write_shift(-4,A.DIFSECT);for(a+=i[2];f<a;++f)n.write_shift(-4,A.FATSECT);s(i[3]);s(i[4]);var h=0,l=0;var o=e.FileIndex[0];for(;h<e.FileIndex.length;++h){o=e.FileIndex[h];if(!o.content)continue;l=o.content.length;if(l<4096)continue;o.start=a;s(l+511>>9)}s(i[6]+7>>3);while(n.l&511)n.write_shift(-4,A.ENDOFCHAIN);a=f=0;for(h=0;h<e.FileIndex.length;++h){o=e.FileIndex[h];if(!o.content)continue;l=o.content.length;if(!l||l>=4096)continue;o.start=a;s(l+63>>6)}while(n.l&511)n.write_shift(-4,A.ENDOFCHAIN);for(f=0;f<i[4]<<2;++f){var u=e.FullPaths[f];if(!u||u.length===0){for(h=0;h<17;++h)n.write_shift(4,0);for(h=0;h<3;++h)n.write_shift(4,-1);for(h=0;h<12;++h)n.write_shift(4,0);continue}o=e.FileIndex[f];if(f===0)o.start=o.size?o.start-1:y;var c=f===0&&t.root||o.name;l=2*(c.length+1);n.write_shift(64,c,"utf16le");n.write_shift(2,l);n.write_shift(1,o.type);n.write_shift(1,o.color);n.write_shift(-4,o.L);n.write_shift(-4,o.R);n.write_shift(-4,o.C);if(!o.clsid)for(h=0;h<4;++h)n.write_shift(4,0);else n.write_shift(16,o.clsid,"hex");n.write_shift(4,o.state||0);n.write_shift(4,0);n.write_shift(4,0);n.write_shift(4,0);n.write_shift(4,0);n.write_shift(4,o.start);n.write_shift(4,o.size);n.write_shift(4,0)}for(f=1;f<e.FileIndex.length;++f){o=e.FileIndex[f];if(o.size>=4096){n.l=o.start+1<<9;for(h=0;h<o.size;++h)n.write_shift(1,o.content[h]);for(;h&511;++h)n.write_shift(1,0)}}for(f=1;f<e.FileIndex.length;++f){o=e.FileIndex[f];if(o.size>0&&o.size<4096){for(h=0;h<o.size;++h)n.write_shift(1,o.content[h]);for(;h&63;++h)n.write_shift(1,0)}}while(n.l<n.length)n.write_shift(1,0);return n}function E(e,r){var t=e.FullPaths.map(function(e){return e.toUpperCase()});var i=t.map(function(e){var r=e.split("/");return r[r.length-(e.slice(-1)=="/"?2:1)]});var n=false;if(r.charCodeAt(0)===47){n=true;r=t[0].slice(0,-1)+r}else n=r.indexOf("/")!==-1;var f=r.toUpperCase();var a=n===true?t.indexOf(f):i.indexOf(f);if(a!==-1)return e.FileIndex[a];var s=!f.match(chr1);f=f.replace(chr0,"");if(s)f=f.replace(chr1,"!");for(a=0;a<t.length;++a){if((s?t[a].replace(chr1,"!"):t[a]).replace(chr0,"")==f)return e.FileIndex[a];if((s?i[a].replace(chr1,"!"):i[a]).replace(chr0,"")==f)return e.FileIndex[a]}return null}var C=64;var y=-2;var S="d0cf11e0a1b11ae1";var B=[208,207,17,224,161,177,26,225];var m="00000000000000000000000000000000";var A={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:y,FREESECT:-1,HEADER_SIGNATURE:S,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:m,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function L(e,r,t){f();var i=x(e,t);n.writeFileSync(r,i)}function k(e){var r=new Array(e.length);for(var t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function R(e,r){var t=x(e,r);switch(r&&r.type){case"file":f();n.writeFileSync(r.filename,t);return t;case"binary":return k(t);case"base64":return Base64.encode(k(t));}return t}function z(e){var r={};g(r,e);return r}function P(e,r,t,n){var f=n&&n.unsafe;if(!f)g(e);var a=!f&&CFB.find(e,r);if(!a){var s=e.FullPaths[0];if(r.slice(0,s.length)==s)s=r;else{if(s.slice(-1)!="/")s+="/";s=(s+r).replace("//","/")}a={name:i(r),type:2};e.FileIndex.push(a);e.FullPaths.push(s);if(!f)CFB.utils.cfb_gc(e)}a.content=t;a.size=t?t.length:0;if(n){if(n.CLSID)a.clsid=n.CLSID}return a}function D(e,r){g(e);var t=CFB.find(e,r);if(t)for(var i=0;i<e.FileIndex.length;++i)if(e.FileIndex[i]==t){e.FileIndex.splice(i,1);e.FullPaths.splice(i,1);return true}return false}function O(e,r,t){g(e);var n=CFB.find(e,r);if(n)for(var f=0;f<e.FileIndex.length;++f)if(e.FileIndex[f]==n){e.FileIndex[f].name=i(t);e.FullPaths[f]=t;return true}return false}function T(e){b(e,true)}e.find=E;e.read=F;e.parse=a;e.write=R;e.writeFile=L;e.utils={cfb_new:z,cfb_add:P,cfb_del:D,cfb_mov:O,cfb_gc:T,ReadShift:ReadShift,CheckField:CheckField,prep_blob:prep_blob,bconcat:bconcat,consts:A};return e}();if(typeof require!=="undefined"&&typeof module!=="undefined"&&typeof DO_NOT_EXPORT_CFB==="undefined"){module.exports=CFB}
