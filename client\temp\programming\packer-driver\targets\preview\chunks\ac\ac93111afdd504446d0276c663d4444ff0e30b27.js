System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, v2, v3, FrameAnimationCmpt, getHeroFrameAnimConf, GameModel, EventType, DragTouchType, HeroState, DragTouchCmpt, PC_TOUCH_HEIGHT, _class, _crd, ccclass, property, HeroCmpt;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfFrameAnimationCmpt(extras) {
    _reporterNs.report("FrameAnimationCmpt", "../cmpt/FrameAnimationCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfgetHeroFrameAnimConf(extras) {
    _reporterNs.report("getHeroFrameAnimConf", "../../common/config/HeroFrameAnimConf", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameModel(extras) {
    _reporterNs.report("GameModel", "../../model/game/GameModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventType(extras) {
    _reporterNs.report("EventType", "../../common/event/EventType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttrBarCmpt(extras) {
    _reporterNs.report("AttrBarCmpt", "./AttrBarCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHeroObj(extras) {
    _reporterNs.report("HeroObj", "../../model/game/HeroObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAreaType(extras) {
    _reporterNs.report("AreaType", "../../common/constant/Enums", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDragTouchType(extras) {
    _reporterNs.report("DragTouchType", "../../common/constant/Enums", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHeroState(extras) {
    _reporterNs.report("HeroState", "../../common/constant/Enums", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDragTouchCmpt(extras) {
    _reporterNs.report("DragTouchCmpt", "./DragTouchCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPC_TOUCH_HEIGHT(extras) {
    _reporterNs.report("PC_TOUCH_HEIGHT", "../../common/constant/Constant", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      v2 = _cc.v2;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      FrameAnimationCmpt = _unresolved_2.default;
    }, function (_unresolved_3) {
      getHeroFrameAnimConf = _unresolved_3.getHeroFrameAnimConf;
    }, function (_unresolved_4) {
      GameModel = _unresolved_4.default;
    }, function (_unresolved_5) {
      EventType = _unresolved_5.default;
    }, function (_unresolved_6) {
      DragTouchType = _unresolved_6.DragTouchType;
      HeroState = _unresolved_6.HeroState;
    }, function (_unresolved_7) {
      DragTouchCmpt = _unresolved_7.default;
    }, function (_unresolved_8) {
      PC_TOUCH_HEIGHT = _unresolved_8.PC_TOUCH_HEIGHT;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6339ekL6f5D5ZIP6RDrK34W", "HeroCmpt", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'Label', 'Node', 'v2', 'v3', 'Vec2', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator); // 一个英雄

      _export("default", HeroCmpt = ccclass(_class = class HeroCmpt extends Component {
        constructor() {
          super(...arguments);
          this.key = '';
          this.data = null;
          this.body = null;
          this.animNode = null;
          this.animCmpt = null;
          this.touchCmpt = null;
          this.currAnimName = '';
          this.attrBar = null;
          this.preStateUid = '';
          this.tempPosition = v3();
          this.originalPosition = v3();
          this.dragBeginLocation = v2();
          //拖动开始的坐标
          this._temp_vec2_1 = v2();
        }

        init(data, pos, key) {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this.data = data;
            _this.key = key;

            _this.originalPosition.set(pos);

            _this.node.setPosition(pos);

            _this.body = _this.FindChild('body');
            _this.animNode = _this.FindChild('body/anim');
            _this.animCmpt = _this.animNode.getComponent(_crd && FrameAnimationCmpt === void 0 ? (_reportPossibleCrUseOfFrameAnimationCmpt({
              error: Error()
            }), FrameAnimationCmpt) : FrameAnimationCmpt);
            yield _this.animCmpt.init((_crd && getHeroFrameAnimConf === void 0 ? (_reportPossibleCrUseOfgetHeroFrameAnimConf({
              error: Error()
            }), getHeroFrameAnimConf) : getHeroFrameAnimConf)(data.getViewId()), key);

            if (_this.isValid) {
              _this.animNode.setPosition(_this.animCmpt.getAnimConfPositionOffset()); //设置偏移


              _this.animCmpt.setUpdateModel((_crd && GameModel === void 0 ? (_reportPossibleCrUseOfGameModel({
                error: Error()
              }), GameModel) : GameModel).ins());

              _this.touchCmpt = _this.FindChild('touch').addComponent(_crd && DragTouchCmpt === void 0 ? (_reportPossibleCrUseOfDragTouchCmpt({
                error: Error()
              }), DragTouchCmpt) : DragTouchCmpt).init(_this).setCanDrag(data.isCanDrag());
              _this.Child('lv', Label).string = data.lv + '';

              _this.playAnimation('idle');

              _this.loadAttrBar();
            }

            return _this;
          })();
        }

        resync(data, pos) {
          this.data.init(data);
          this.originalPosition.set(pos);
          this.node.setPosition(pos);
          this.node.zIndex = 0;
          this.body.Child('shadow').active = true;
          this.playAnimation('idle');
          this.touchCmpt.setCanDrag(this.data.isCanDrag());
          this.loadAttrBar();
          return this;
        }

        clean(release) {
          var _this$data;

          this.animCmpt.clean();
          this.node.destroy();
          release && assetsMgr.releaseTempRes((_this$data = this.data) == null ? void 0 : _this$data.getPrefabUrl(), this.key);
          this.data = null;
        }

        loadAttrBar() {// const it = await nodePoolMgr.get('animal/ATTR_BAR', this.key)
          // if (!this.isValid || !this.data) {
          //     return nodePoolMgr.put(it)
          // }
          // it.parent = this.node
          // it.sortIndex = 10
          // it.active = true
          // this.attrBar = it.getComponent(AttrBarCmpt).init(this.data)

          return _asyncToGenerator(function* () {})();
        }

        get uid() {
          var _this$data2;

          return ((_this$data2 = this.data) == null ? void 0 : _this$data2.uid) || '';
        }

        get areaType() {
          var _this$data3;

          return (_this$data3 = this.data) == null ? void 0 : _this$data3.areaType;
        }

        getBody() {
          return this.body;
        }

        getTempPosition() {
          return this.node.getPosition(this.tempPosition);
        } // 触摸事件


        onTouchEvent(type, event) {
          // console.log('onTouchEvent', DragTouchType[type], event)
          if (type === (_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType).DRAG_BEGIN) {
            this.node.y = this.originalPosition.y + (_crd && PC_TOUCH_HEIGHT === void 0 ? (_reportPossibleCrUseOfPC_TOUCH_HEIGHT({
              error: Error()
            }), PC_TOUCH_HEIGHT) : PC_TOUCH_HEIGHT);
            event.getUILocation(this.dragBeginLocation);
            this.node.zIndex = 10;
            this.body.Child('shadow').active = false;
            this.playAnimation('caught');
            eventCenter.emit((_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).DRAG_HERO_BEGIN, this);
          } else if (type === (_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType).DRAG_MOVE) {
            var location = event.getUILocation(this._temp_vec2_1);
            var diff = location.subtract(this.dragBeginLocation).add(this.originalPosition.toVec2());
            this.node.setPosition(diff.x, diff.y + (_crd && PC_TOUCH_HEIGHT === void 0 ? (_reportPossibleCrUseOfPC_TOUCH_HEIGHT({
              error: Error()
            }), PC_TOUCH_HEIGHT) : PC_TOUCH_HEIGHT));
            eventCenter.emit((_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).DRAG_HERO_MOVE, this);
          } else if (type === (_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType).DRAG_END) {
            eventCenter.emit((_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).DRAG_HERO_END, this);
          } else if (type === (_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType).CLICK) {
            console.log('click!', this.data);
          }
        } // 还原位置


        restorePosition() {
          if (!this.isValid) {
            return;
          }

          this.node.zIndex = 0;
          this.node.setPosition(this.originalPosition);
          this.body.Child('shadow').active = true;
          this.playAnimation('idle');
        }

        update(dt) {
          if (!this.data) {
            return;
          }

          this.updateState();
        } // 播放动画


        playAnimation(name, cb, startTime) {
          var _this$animCmpt;

          this.currAnimName = name;
          (_this$animCmpt = this.animCmpt) == null || _this$animCmpt.play(name, cb, startTime);
        } // 同步状态信息


        updateState() {
          var _this$data4;

          if (!((_this$data4 = this.data) != null && _this$data4.state) || this.preStateUid === this.data.state.uid) {
            return;
          }

          this.preStateUid = this.data.state.uid;
          var state = this.data.state.type,
              data = this.data.state.data; // cc.log('updateState', this.uid, this.point.ID(), HeroState[state])
          // this.data.actioning = this.data.actioning || (state !== HeroState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动

          if (state === (_crd && HeroState === void 0 ? (_reportPossibleCrUseOfHeroState({
            error: Error()
          }), HeroState) : HeroState).STAND) {
            //待机
            this.doStand();
          } else if (state === (_crd && HeroState === void 0 ? (_reportPossibleCrUseOfHeroState({
            error: Error()
          }), HeroState) : HeroState).ATTACK) {
            //攻击
            this.doAttack(data);
          } else if (state === (_crd && HeroState === void 0 ? (_reportPossibleCrUseOfHeroState({
            error: Error()
          }), HeroState) : HeroState).HIT) {
            //受击
            this.doHit(data);
          } else {
            this.playAnimation('idle');
          }
        } // 待机


        doStand() {
          var _this$animCmpt2, _this$attrBar;

          var animName = (_this$animCmpt2 = this.animCmpt) == null ? void 0 : _this$animCmpt2.playAnimName;

          if (animName === 'move' || animName === 'move_pull') {
            //只有移动的时候才强行切换成idle
            this.playAnimation('idle');
          }

          (_this$attrBar = this.attrBar) == null || _this$attrBar.reset();
        } // 攻击


        doAttack(data) {
          var _data$currAttackTime;

          var currAttackTime = (_data$currAttackTime = data.currAttackTime) != null ? _data$currAttackTime : 0;
          var suffix = data.instabilityAttackIndex || '';
          this.playAnimation('attack' + suffix, () => this.isValid && this.playAnimation('idle'), currAttackTime);
        } // 受击


        doHit(data) {
          var _data$damage, _this$attrBar2;

          var damage = (_data$damage = data.damage) != null ? _data$damage : 0;
          var isDie = this.data.isDie();
          var sound = data.sound; //受击音效

          var uid = this.uid;
          (_this$attrBar2 = this.attrBar) == null || _this$attrBar2.play();

          if (damage === 0) {
            return this.playAnimation('idle');
          }

          var animName = 'hit';

          if (isDie) {
            animName = 'die'; // this.playSFXByKey('die_sound')
          } else if (sound) {// this.playSFX(sound)
          }

          this.playAnimation(animName, () => {
            if (isDie) {
              eventCenter.emit((_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
                error: Error()
              }), EventType) : EventType).REMOVE_HERO, uid, false);
            } else if (this.isValid) {
              this.playAnimation('idle');
            }
          });
        } // 直接死亡


        doDie(data) {}

      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ac93111afdd504446d0276c663d4444ff0e30b27.js.map