System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, EventType, FSPModel, EncounterObj, MapNodeObj, _dec, _class, _crd, GameModel;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfIFrameAnimation(extras) {
    _reporterNs.report("IFrameAnimation", "../../common/constant/interface", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventType(extras) {
    _reporterNs.report("EventType", "../../common/event/EventType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFSPModel(extras) {
    _reporterNs.report("FSPModel", "../battle/FSPModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfNetworkModel(extras) {
    _reporterNs.report("NetworkModel", "../common/NetworkModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEncounterObj(extras) {
    _reporterNs.report("EncounterObj", "./EncounterObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMapNodeObj(extras) {
    _reporterNs.report("MapNodeObj", "./MapNodeObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlayerModel(extras) {
    _reporterNs.report("PlayerModel", "./PlayerModel", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      EventType = _unresolved_2.default;
    }, function (_unresolved_3) {
      FSPModel = _unresolved_3.default;
    }, function (_unresolved_4) {
      EncounterObj = _unresolved_4.default;
    }, function (_unresolved_5) {
      MapNodeObj = _unresolved_5.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ce65360IrdCeov+v5oDCn0o", "GameModel", undefined);

      /**
       * 游戏模块
       */
      _export("default", GameModel = (_dec = mc.addmodel('game'), _dec(_class = class GameModel extends mc.BaseModel {
        constructor() {
          super(...arguments);
          this.runing = false;
          //是否运行中
          this.net = null;
          this.player = null;
          this.maps = [];
          //地图数据
          this.mapPaths = [];
          //已经走的路径
          this.encounter = new (_crd && EncounterObj === void 0 ? (_reportPossibleCrUseOfEncounterObj({
            error: Error()
          }), EncounterObj) : EncounterObj)();
          //遭遇信息
          this.frameAnimations = [];
          //动画组件 在这里统一每帧调用
          this.fspModel = null;
        }

        onCreate() {
          this.net = this.getModel('net');
          this.player = this.getModel('player');
        }

        init(data) {
          console.log('init game', data);
          var gameData = data.gameData;
          this.maps = data.mapData.maps.map((layer, day) => layer.nodes.map((n, i) => {
            n.children = n.children.map(id => (day + 1) * 10 + id);
            return new (_crd && MapNodeObj === void 0 ? (_reportPossibleCrUseOfMapNodeObj({
              error: Error()
            }), MapNodeObj) : MapNodeObj)().init(day * 10 + i, n);
          }));
          this.player.init(gameData.player);
          this.encounter.init(gameData.encounter);
          this.mapPaths = gameData.mapPaths;
        }

        enter() {
          this.runing = true;
        }

        leave() {
          this.runing = false;
          this.frameAnimations = [];
        }

        isRuning() {
          return this.runing;
        }

        getMaps() {
          return this.maps;
        }

        getEncounter() {
          return this.encounter;
        }

        addFrameAnimation(cmpt) {
          if (!this.frameAnimations.has('uuid', cmpt.uuid)) {
            this.frameAnimations.push(cmpt);
          }
        }

        removeFrameAnimation(uuid) {
          this.frameAnimations.remove('uuid', uuid);
        }

        update(dt) {
          if (!this.runing) {
            return;
          } else if (this.fspModel) {
            this.fspModel.update(dt);
          } else {
            this.updateAnimationFrame(dt * 1000);
          }
        } // 刷新动画帧 毫秒


        updateAnimationFrame(dt) {
          for (var i = this.frameAnimations.length - 1; i >= 0; i--) {
            var cmpt = this.frameAnimations[i];

            if (cmpt.isValid) {
              cmpt.updateFrame(dt);
            } else {
              this.frameAnimations.splice(i, 1);
            }
          }
        } // 选择地图节点


        selectMapNode(index) {
          var _this = this;

          return _asyncToGenerator(function* () {
            var {
              err,
              data
            } = yield _this.net.request('game/HD_SelectMapNode', {
              index
            }, true);

            if (!err) {
              var gameData = data.gameData;

              _this.player.init(gameData.player);

              _this.encounter.init(gameData.encounter);

              _this.emit((_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
                error: Error()
              }), EventType) : EventType).UPDATE_GAME_INFO);
            }

            return err;
          })();
        } // 购买英雄


        buyHero(heroUid, areaType, useIndex) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            var {
              err,
              data
            } = yield _this2.net.request('game/HD_BuyHero', {
              heroUid,
              areaType,
              useIndex
            }, true);

            if (!err) {
              var gameData = data.gameData;

              _this2.player.init(gameData.player);

              _this2.encounter.init(gameData.encounter);
            }

            return err;
          })();
        } // 出售英雄


        sellHero(heroUid) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            var {
              err,
              data
            } = yield _this3.net.request('game/HD_SellHero', {
              heroUid
            }, true);

            if (!err) {
              _this3.player.updateAreaHero(data);

              _this3.player.updateGold(data.gold);
            }

            return err;
          })();
        } // 移动英雄


        moveHero(heroUid, areaType, useIndex) {
          var _this4 = this;

          return _asyncToGenerator(function* () {
            var {
              err,
              data
            } = yield _this4.net.request('game/HD_MoveHero', {
              heroUid,
              areaType,
              useIndex
            }, true);

            if (!err) {
              _this4.player.updateAreaHero(data);
            }

            return err;
          })();
        } //////////////////////////////////////////////////////////[ 战斗相关 ]///////////////////////////////////////////////////////////////////


        getFspModel() {
          return this.fspModel;
        } // 战斗开始


        battleBegin() {
          var fighters = []; // fighters.pushArr(this.battleAnimals.map(m => m.toFighter()))
          // fighters.pushArr(this.enemyAnimals.map(m => m.toFighter()))

          this.fspModel = new (_crd && FSPModel === void 0 ? (_reportPossibleCrUseOfFSPModel({
            error: Error()
          }), FSPModel) : FSPModel)().init({
            randSeed: ut.random(10000, 99999),
            fighters: fighters
          });
        } // 战斗结束 本地


        battleEndByLocal() {
          this.fspModel.stop();
          this.fspModel = null;
        } // 根据阵营删除动物 来至战斗


        removeAnimalByBattle(camp, uid) {
          if (camp === 1) {// this.battleAnimals.remove('uid', uid)
          } else {// this.enemyAnimals.remove('uid', uid)
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b7ea4ae528ecb9ddfdc285a953baa6c8dc5e3541.js.map