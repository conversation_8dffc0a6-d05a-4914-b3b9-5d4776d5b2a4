System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _crd;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6d704z7i0xDspnIX9yjx1dl", "DataType", undefined);

      // 提示框参数选择
      __checkObsolete__(['Vec3']); // 对话框选择参数
      // 帧动画配置信息
      // 行为树节点配置信息


      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d19dfe25cbd1d65563d1aa77b53ee9010ad8c75d.js.map