{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/common/event/EventType.ts"], "names": ["EVENT_GAME_SHOW", "EVENT_GAME_HIDE", "CLIENT_VERSION_LOW", "BUTTON_LOGIN_DONE", "QUEUE_UP_DONE", "UPDATE_GAME_INFO", "UPDATE_BATTLE_AREA", "UPDATE_ENCOUNTER_AREA", "DRAG_HERO_BEGIN", "DRAG_HERO_MOVE", "DRAG_HERO_END", "REMOVE_HERO", "PLAY_FLUTTER_HP"], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;yBACe;AAEXA,QAAAA,eAAe,EAAE,iBAFN;AAEyB;AACpCC,QAAAA,eAAe,EAAE,iBAHN;AAGyB;AAEpCC,QAAAA,kBAAkB,EAAE,oBALT;AAK+B;AAC1CC,QAAAA,iBAAiB,EAAE,mBANR;AAM6B;AACxCC,QAAAA,aAAa,EAAE,eAPJ;AAOqB;AAEhCC,QAAAA,gBAAgB,EAAE,kBATP;AAS2B;AACtCC,QAAAA,kBAAkB,EAAE,oBAVT;AAU+B;AAC1CC,QAAAA,qBAAqB,EAAE,uBAXZ;AAWqC;AAChDC,QAAAA,eAAe,EAAE,iBAZN;AAYyB;AACpCC,QAAAA,cAAc,EAAE,gBAbL;AAauB;AAClCC,QAAAA,aAAa,EAAE,eAdJ;AAcqB;AAChCC,QAAAA,WAAW,EAAE,aAfF;AAeiB;AAC5BC,QAAAA,eAAe,EAAE,iBAhBN,CAgByB;;AAhBzB,O", "sourcesContent": ["/**\r\n * 全局事件（全大写单词间用下划线隔开）\r\n */\r\nexport default {\r\n\r\n    EVENT_GAME_SHOW: 'EVENT_GAME_SHOW', //游戏进入前台\r\n    EVENT_GAME_HIDE: 'EVENT_GAME_HIDE', //游戏进入后台\r\n\r\n    CLIENT_VERSION_LOW: 'CLIENT_VERSION_LOW', //客户端版本过低的提示\r\n    BUTTON_LOGIN_DONE: 'BUTTON_LOGIN_DONE', //点击按钮登录完成\r\n    QUEUE_UP_DONE: 'QUEUE_UP_DONE', //排队完成\r\n\r\n    UPDATE_GAME_INFO: 'UPDATE_GAME_INFO', //刷新游戏数据\r\n    UPDATE_BATTLE_AREA: 'UPDATE_BATTLE_AREA', //刷新战斗区域\r\n    UPDATE_ENCOUNTER_AREA: 'UPDATE_ENCOUNTER_AREA', //刷新遭遇区域\r\n    DRAG_HERO_BEGIN: 'DRAG_HERO_BEGIN', //拖动英雄开始\r\n    DRAG_HERO_MOVE: 'DRAG_HERO_MOVE', //拖动英雄移动\r\n    DRAG_HERO_END: 'DRAG_HERO_END', //拖动英雄结束\r\n    REMOVE_HERO: 'REMOVE_HERO', //删除英雄\r\n    PLAY_FLUTTER_HP: 'PLAY_FLUTTER_HP', //播放飘字\r\n}"]}