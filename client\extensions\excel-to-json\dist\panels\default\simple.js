"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const CfgUtil = require('../../core/CfgUtil');
const nodeXlsx = require('node-xlsx');
const jsonBeautifully = require('json-beautifully');
const Vue = require('vue/dist/vue.common.js');
module.exports = Editor.Panel.define({
    listeners: {
        show() { },
        hide() { },
    },
    template: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        app: '#app',
        logTextArea: '#logTextArea',
    },
    methods: {},
    ready() {
        const self = this;
        // 等待 DOM 加载完成后创建 Vue 实例
        setTimeout(() => {
            try {
                const appElement = self.$.app;
                if (!appElement) {
                    return;
                }
                window.plugin = new Vue({
                    el: appElement,
                    data: {
                        logView: "Excel转Json 面板已加载...\n",
                        excelRootPath: "",
                        jsonSavePath1: "",
                        jsonSavePath2: "",
                        isFormatJson: false,
                        isAutoTranslate: false,
                        isText: false,
                        isServer: false,
                        excelArray: [],
                        textConverterMap: {},
                    },
                    created() {
                        this.initPluginCfg();
                        this.initCfgSavePath();
                    },
                    methods: {
                        addLog(str) {
                            this.logView += str + "\n";
                            this.$nextTick(() => {
                                const logArea = document.getElementById('logTextArea');
                                if (logArea) {
                                    logArea.scrollTop = logArea.scrollHeight;
                                }
                            });
                        },
                        initPluginCfg() {
                            CfgUtil.initCfg((data) => {
                                this.setExcelRootPath(data.excelRootPath || "");
                                this.setJsonSavePath2(data.jsonSavePath2 || "");
                                this.isFormatJson = !!data.isFormatJson;
                                this.isAutoTranslate = !!data.isAutoTranslate;
                            });
                        },
                        initCfgSavePath() {
                            try {
                                // 获取项目路径，添加错误处理
                                let projectPath = '';
                                if (Editor && Editor.Project && Editor.Project.path) {
                                    projectPath = Editor.Project.path;
                                }
                                else {
                                    this.addLog('警告: 无法获取项目路径，跳过初始化配置保存路径');
                                    return;
                                }
                                let pluginResPath = projectPath + '/assets/resources/common';
                                if (!fs.existsSync(pluginResPath)) {
                                    fs.mkdirSync(pluginResPath, { recursive: true });
                                }
                                let jsonSavePath1 = path.join(pluginResPath, "json");
                                if (!fs.existsSync(jsonSavePath1)) {
                                    fs.mkdirSync(jsonSavePath1, { recursive: true });
                                }
                                this.jsonSavePath1 = jsonSavePath1;
                                this.addLog('初始化配置保存路径成功: ' + jsonSavePath1);
                            }
                            catch (error) {
                                this.addLog('错误: 初始化配置保存路径失败 - ' + error);
                            }
                        },
                        saveConfig() {
                            CfgUtil.saveCfgByData({
                                excelRootPath: this.excelRootPath,
                                jsonSavePath2: this.jsonSavePath2,
                                isFormatJson: this.isFormatJson,
                                isAutoTranslate: this.isAutoTranslate,
                            });
                        },
                        setExcelRootPath(pathStr) {
                            this.excelRootPath = pathStr;
                            if (fs.existsSync(this.excelRootPath)) {
                                this.onAnalyzeExcelDirPath(this.excelRootPath);
                            }
                            else {
                                this.addLog("Excel目录不存在：" + this.excelRootPath);
                                this.excelRootPath = "";
                            }
                            this.isText = this.excelRootPath.endsWith('文本');
                            this.isServer = this.excelRootPath.endsWith('server');
                        },
                        setJsonSavePath2(pathStr) {
                            this.jsonSavePath2 = pathStr;
                            if (pathStr && !fs.existsSync(pathStr)) {
                                this.jsonSavePath2 = '';
                            }
                        },
                        async onBtnClickSelectExcelRootPath() {
                            try {
                                let res = await Editor.Dialog.select({
                                    title: "选择Excel的根目录",
                                    type: 'directory',
                                });
                                if (res.filePaths && res.filePaths.length > 0) {
                                    let dir = res.filePaths[0];
                                    if (dir !== this.excelRootPath) {
                                        this.setExcelRootPath(dir);
                                        this.saveConfig();
                                    }
                                }
                            }
                            catch (error) {
                                this.addLog("选择目录失败: " + error);
                            }
                        },
                        async onBtnClickSelectJsonSavePath() {
                            try {
                                let res = await Editor.Dialog.select({
                                    title: "选择保存Json的根目录",
                                    type: 'directory',
                                });
                                if (res.filePaths && res.filePaths.length > 0) {
                                    let dir = res.filePaths[0];
                                    if (dir !== this.jsonSavePath2) {
                                        this.setJsonSavePath2(dir);
                                        this.saveConfig();
                                    }
                                }
                            }
                            catch (error) {
                                this.addLog("选择目录失败: " + error);
                            }
                        },
                        onBtnClickOpenJsonSavePath1() {
                            if (fs.existsSync(this.jsonSavePath1)) {
                                Editor.Utils.openInFolder(this.jsonSavePath1);
                            }
                            else {
                                this.addLog("目录不存在：" + this.jsonSavePath1);
                            }
                        },
                        onBtnClickOpenJsonSavePath2() {
                            if (fs.existsSync(this.jsonSavePath2)) {
                                Editor.Utils.openInFolder(this.jsonSavePath2);
                            }
                            else {
                                this.addLog("目录不存在：" + this.jsonSavePath2);
                            }
                        },
                        onBtnClickOpenExcelRootPath() {
                            if (fs.existsSync(this.excelRootPath)) {
                                Editor.Utils.openInFolder(this.excelRootPath);
                            }
                            else {
                                this.addLog("目录不存在：" + this.excelRootPath);
                            }
                        },
                        onBtnClickFormatJson() {
                            this.isFormatJson = !this.isFormatJson;
                            this.saveConfig();
                        },
                        onBtnClickAutoTranslate() {
                            this.isAutoTranslate = !this.isAutoTranslate;
                            this.saveConfig();
                        },
                        onBtnClickSelectSheet(event) {
                            let checked = event.target.checked;
                            this.excelArray.forEach((item) => item.isUse = checked);
                        },
                        onBtnClickUpdate() {
                            this.onAnalyzeExcelDirPath(this.excelRootPath);
                        },
                        onBtnClickGen() {
                            if (this.excelArray.length <= 0) {
                                return this.addLog("未发现要生成的配置!");
                            }
                            this.onBtnClickUpdate();
                            this.doGenerate();
                        },
                        onAnalyzeExcelDirPath(dir) {
                            if (!dir)
                                return;
                            this.logView = "";
                            this.excelArray = [];
                            try {
                                const now = Date.now();
                                let excelFileArr = this.readDirSync(dir);
                                for (let i = 0; i < excelFileArr.length; i++) {
                                    let itemFullPath = excelFileArr[i];
                                    try {
                                        let excelData = nodeXlsx.parse(itemFullPath);
                                        if (!excelData || excelData.length === 0) {
                                            this.addLog("[Error] 空Sheet: " + itemFullPath);
                                            continue;
                                        }
                                        let name = itemFullPath.substr(dir.length + 1);
                                        let it = excelData[0]; // 默认第一个sheet
                                        let itemData = {
                                            isUse: true,
                                            fullPath: itemFullPath,
                                            name: name,
                                            datas: it.data,
                                            sheet: it.name
                                        };
                                        this.excelArray.push(itemData);
                                    }
                                    catch (error) {
                                        this.addLog("[Error] 解析Excel失败: " + itemFullPath + " - " + error);
                                    }
                                }
                                this.addLog('读取文件完成，共' + this.excelArray.length + '个文件，耗时' + (Date.now() - now) + 'ms');
                            }
                            catch (error) {
                                this.addLog("分析Excel目录失败: " + error);
                            }
                        },
                        readDirSync(dirPath) {
                            let arr = [];
                            try {
                                let dirInfo = fs.readdirSync(dirPath);
                                for (let i = 0; i < dirInfo.length; i++) {
                                    let item = dirInfo[i];
                                    let itemFullPath = path.join(dirPath, item);
                                    let info = fs.statSync(itemFullPath);
                                    if (info.isDirectory()) {
                                        arr = arr.concat(this.readDirSync(itemFullPath));
                                    }
                                    else if (info.isFile()) {
                                        if (item.startsWith("~$")) {
                                            this.addLog("跳过临时文件: " + itemFullPath);
                                            continue;
                                        }
                                        let extName = path.extname(itemFullPath);
                                        if (extName === ".xlsx" || extName === ".xls") {
                                            arr.push(itemFullPath);
                                        }
                                    }
                                }
                            }
                            catch (error) {
                                this.addLog("读取目录失败: " + dirPath + " - " + error);
                            }
                            return arr;
                        },
                        doGenerate() {
                            this.logView = "";
                            let cnt = 0;
                            for (let i = 0; i < this.excelArray.length; i++) {
                                const item = this.excelArray[i];
                                if (!item.isUse)
                                    continue;
                                if (item.datas.length > 2) {
                                    try {
                                        let jsonSaveData = this.getJsonSaveData(item.datas);
                                        let saveStr = JSON.stringify(jsonSaveData);
                                        if (this.isFormatJson) {
                                            saveStr = jsonBeautifully(saveStr);
                                        }
                                        const itemName = item.name.split('.')[0];
                                        let fileName = itemName + '.json';
                                        // 保存到第一个目录
                                        if (this.jsonSavePath1) {
                                            let saveFileFullPath = path.join(this.jsonSavePath1, fileName);
                                            fs.writeFileSync(saveFileFullPath, saveStr);
                                            this.addLog("[Json]: " + saveFileFullPath);
                                        }
                                        // 保存到第二个目录
                                        if (this.jsonSavePath2) {
                                            let saveFileFullPath = path.join(this.jsonSavePath2, fileName);
                                            fs.writeFileSync(saveFileFullPath, saveStr);
                                            this.addLog("[Json]: " + saveFileFullPath);
                                        }
                                        cnt++;
                                    }
                                    catch (error) {
                                        this.addLog("[Error] 生成JSON失败: " + item.name + " - " + error);
                                    }
                                }
                                else {
                                    this.addLog("[Error] 行数不足: " + item.name);
                                }
                            }
                            if (cnt > 0) {
                                // Editor.Message.request('asset-db', 'refresh-asset', 'db://assets/resources/common/json').then(ok => {
                                //     if (ok) {
                                //         this.addLog("成功转换了[" + cnt + "]个文件!");
                                //     } else {
                                //         this.addLog("转换了[0]个文件!");
                                //     }
                                // })
                                this.addLog("成功转换了[" + cnt + "]个文件!");
                            }
                            else {
                                this.addLog("转换了[0]个文件!");
                            }
                        },
                        getJsonSaveData(excelData) {
                            const titles = excelData[1].filter((m) => !!m);
                            const titleCount = titles.length;
                            const datas = [];
                            for (let i = 2; i < excelData.length; i++) {
                                let lineData = excelData[i];
                                let saveLineData = {};
                                let hasData = false;
                                for (let j = 0; j < titleCount; j++) {
                                    let key = titles[j];
                                    if (!key)
                                        continue;
                                    let value = lineData[j];
                                    if (!value && j === 0 && value !== 0) {
                                        break; // 第一个默认为id，必须有值
                                    }
                                    else if (value === undefined) {
                                        value = '';
                                    }
                                    if (!!value) {
                                        hasData = true;
                                    }
                                    saveLineData[key] = value;
                                }
                                if (hasData) {
                                    datas.push(saveLineData);
                                }
                            }
                            return datas;
                        }
                    }
                });
            }
            catch (error) {
                // Vue 实例创建失败
            }
        }, 100);
    },
    beforeClose() { },
    close() { },
});
//# sourceMappingURL=data:application/json;base64,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