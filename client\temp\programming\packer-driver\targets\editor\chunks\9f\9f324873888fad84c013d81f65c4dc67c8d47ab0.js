System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Prefab, v2, v3, HeroCmpt, EventType, AnimPlayCmpt, RoleCmpt, RoleObj, vHelper, HeroObj, AreaType, MapNodeType, _class, _crd, ccclass, GameWindCtrl;

  function _reportPossibleCrUseOfHeroCmpt(extras) {
    _reporterNs.report("HeroCmpt", "./HeroCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameModel(extras) {
    _reporterNs.report("GameModel", "../../model/game/GameModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventType(extras) {
    _reporterNs.report("EventType", "../../common/event/EventType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAnimPlayCmpt(extras) {
    _reporterNs.report("AnimPlayCmpt", "./AnimPlayCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoleCmpt(extras) {
    _reporterNs.report("RoleCmpt", "./RoleCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoleObj(extras) {
    _reporterNs.report("RoleObj", "../../model/game/RoleObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfvHelper(extras) {
    _reporterNs.report("vHelper", "../../common/helper/ViewHelper", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHeroObj(extras) {
    _reporterNs.report("HeroObj", "../../model/game/HeroObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlayerModel(extras) {
    _reporterNs.report("PlayerModel", "../../model/game/PlayerModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAreaType(extras) {
    _reporterNs.report("AreaType", "../../common/constant/Enums", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMapNodeType(extras) {
    _reporterNs.report("MapNodeType", "../../common/constant/Enums", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Prefab = _cc.Prefab;
      v2 = _cc.v2;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      HeroCmpt = _unresolved_2.default;
    }, function (_unresolved_3) {
      EventType = _unresolved_3.default;
    }, function (_unresolved_4) {
      AnimPlayCmpt = _unresolved_4.default;
    }, function (_unresolved_5) {
      RoleCmpt = _unresolved_5.default;
    }, function (_unresolved_6) {
      RoleObj = _unresolved_6.default;
    }, function (_unresolved_7) {
      vHelper = _unresolved_7.vHelper;
    }, function (_unresolved_8) {
      HeroObj = _unresolved_8.default;
    }, function (_unresolved_9) {
      AreaType = _unresolved_9.AreaType;
      MapNodeType = _unresolved_9.MapNodeType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e0786caNBdOXb+Qh/0ZaZAG", "GameWindCtrl", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Prefab', 'EventTouch', 'Vec3', 'Vec2', 'v2', 'v3', 'Size', 'size']);

      ({
        ccclass
      } = _decorator);

      _export("default", GameWindCtrl = ccclass(_class = class GameWindCtrl extends mc.BaseWindCtrl {
        constructor(...args) {
          super(...args);
          //@autocode property begin
          this.chessboardNode_ = null;
          // path://chessboard_n
          this.itemsNode_ = null;
          // path://items_n
          this.flutterNode_ = null;
          // path://flutter_n
          //@end
          this.animPlay = null;
          this.encounterArea = null;
          this.root = null;
          this.recycleRoot = null;
          this.areaPositionMap = {};
          this.recyclePosition = v2();
          this.model = null;
          this.player = null;
          this.heros = [];
          this.heroMap = {};
          this.myRole = null;
          this._temp_vec2_1 = v2();
        }

        listenEventMaps() {
          return [{
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).UPDATE_GAME_INFO]: this.onUpdateGameInfo,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).UPDATE_BATTLE_AREA]: this.onUpdateBattleArea,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).UPDATE_ENCOUNTER_AREA]: this.onUpdateEncounterArea,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).DRAG_HERO_BEGIN]: this.onDragHeroBegin,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).DRAG_HERO_MOVE]: this.onDragHeroMove,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).DRAG_HERO_END]: this.onDragHeroEnd,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).REMOVE_HERO]: this.onRemoveHero,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).PLAY_FLUTTER_HP]: this.onPlayFlutterHp,
            enter: true
          }];
        }

        async onCreate() {
          this.encounterArea = this.chessboardNode_.FindChild('encounter');
          this.root = this.chessboardNode_.FindChild('root');
          this.recycleRoot = this.chessboardNode_.FindChild('recycle/root');
          this.animPlay = this.node.addComponent(_crd && AnimPlayCmpt === void 0 ? (_reportPossibleCrUseOfAnimPlayCmpt({
            error: Error()
          }), AnimPlayCmpt) : AnimPlayCmpt);
          this.areaPositionMap = {};
          this.chessboardNode_.FindChild('area').children.forEach(m => {
            const area = this.areaPositionMap[m.name] = {};
            m.FindChild('pos').children.forEach(n => area[n.name] = ut.convertToNodeAR(n, this.root).clone().toVec3());
          });
          ut.convertToNodeAR(this.recycleRoot, this.root, this.recyclePosition);
          this.model = this.getModel('game');
          this.player = this.getModel('player');
          this.myRole = await this.createRole(new (_crd && RoleObj === void 0 ? (_reportPossibleCrUseOfRoleObj({
            error: Error()
          }), RoleObj) : RoleObj)().init(110001), 0);
          await this.animPlay.init(this.key);
        }

        onEnter(data) {
          this.model.enter();
          this.myRole.playAnimation('idle');
          this.recycleRoot.active = false;
          this.initHeroArea();
          this.initEncounterArea();
        }

        onClean() {
          this.model.leave();
          this.animPlay.clean();
          this.animPlay = null;
          assetsMgr.releaseTempResByTag(this.key);
        } // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        // path://chessboard_n/goon_be


        onClickGoon(event, data) {
          this.myRole.playAnimation('attack', () => this.myRole.playAnimation('idle'));
          (_crd && vHelper === void 0 ? (_reportPossibleCrUseOfvHelper({
            error: Error()
          }), vHelper) : vHelper).showPnl('game/Map');
        } //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // 刷新游戏信息


        onUpdateGameInfo() {
          this.initHeroArea();
          this.initEncounterArea();
        } // 刷新战斗区域


        onUpdateBattleArea() {
          this.initHeroArea();
        } // 刷新遭遇区域


        onUpdateEncounterArea() {
          this.initEncounterArea();
        } // 拖动英雄开始


        onDragHeroBegin(hero) {
          this.recycleRoot.active = true;
        } // 拖动英雄移动


        onDragHeroMove(hero) {
          this.recycleRoot.Child('bg').opacity = this.checkInRecycleRange(hero.getTempPosition()) ? 255 : 100;
        } // 拖动英雄结束


        onDragHeroEnd(hero) {
          this.recycleRoot.active = false;
          const heroPosition = hero.getTempPosition();
          const data = hero.data,
                areaType = data.areaType;

          if (areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).BATTLE || areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).PREPARE) {
            //英雄区域
            if (this.checkInRecycleRange(heroPosition)) {
              return this.sellHero(hero); //卖出
            }

            const [areaType, index] = this.findMinDisAreaIndex(heroPosition);

            if (areaType === data.areaType && index === data.index) {
              return hero.restorePosition();
            }

            this.moveHero(hero, areaType, index);
          } else if (areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).SHOP) {
            //商店区域
            if (hero.node.x >= 0) {
              //没有超过 中线就不算购买
              return hero.restorePosition();
            }

            const [areaType, index] = this.findMinDisAreaIndex(heroPosition);

            if (areaType === data.areaType && index === data.index) {
              return hero.restorePosition();
            }

            this.buyHero(hero, areaType, index);
          }
        } // 删除英雄


        onRemoveHero(uid, release) {
          this.cleanHero(this.heros.remove('uid', uid), release);
        } // 播放飘字


        onPlayFlutterHp(data) {
          var _this$heros$find;

          const node = (_this$heros$find = this.heros.find(m => m.uid === data.uid)) == null ? void 0 : _this$heros$find.node;

          if (node) {
            this.animPlay.playFlutter(data, this.flutterNode_, ut.convertToNodeAR(node, this.flutterNode_));
          }
        } // ----------------------------------------- custom function ----------------------------------------------------


        update(dt) {
          var _this$model;

          (_this$model = this.model) == null || _this$model.update(dt);
        } // 创建一个角色


        async createRole(data, index) {
          if (!this.isValid || !data) {
            return null;
          }

          const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, this.key);

          if (!pfb || !this.isValid) {
            return null;
          }

          const pos = this.chessboardNode_.Child('role_pos/' + index).getPosition();
          const role = await mc.instantiate(pfb, this.itemsNode_).getComponent(_crd && RoleCmpt === void 0 ? (_reportPossibleCrUseOfRoleCmpt({
            error: Error()
          }), RoleCmpt) : RoleCmpt).init(data, pos, this.key);
          return role;
        } // 创建一个英雄


        async createHero(data, pos) {
          if (!this.isValid || !data) {
            return;
          }

          let hero = this.heros.find(m => m.uid === data.uid);

          if (!hero) {} else if (data.isDie()) {
            this.heros.remove('uid', data.uid);
            this.cleanHero(hero);
            return;
          } else {
            return hero.resync(data, pos);
          }

          const node = await nodePoolMgr.get('hero/HERO', this.key);

          if (!node || !node.isValid || !this.isValid) {
            return;
          } else if (this.heroMap[data.uid]) {
            return; //防止多次创建
          } else if (!data || data.isDie()) {
            return;
          }

          node.parent = this.root;
          hero = await node.Component(_crd && HeroCmpt === void 0 ? (_reportPossibleCrUseOfHeroCmpt({
            error: Error()
          }), HeroCmpt) : HeroCmpt).init(data, pos, this.key);

          if (this.isValid) {
            this.heros.push(hero);
            this.heroMap[data.uid] = hero;
          }
        }

        cleanHero(hero, release) {
          if (hero) {
            delete this.heroMap[hero.uid];
            hero.clean(release);
          }
        } // 运行战斗


        async runBattle() {// await Promise.all(this.model.getEnemyAnimals().map(m => this.createHero(m, 'enemy')))
          // this.model.battleBegin()
        } // 清理区域所有英雄


        cleanAreaAllHero() {
          this.heros.delete(m => m.areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).BATTLE || m.areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).PREPARE).forEach(m => this.cleanHero(m));
        } // 初始化英雄区域


        initHeroArea() {
          this.cleanAreaAllHero();
          this.player.getHeros().forEach(m => this.createHero(m, this.areaPositionMap[m.areaType][m.index]));
        } // 初始化遭遇区域


        initEncounterArea() {
          const encounter = this.model.getEncounter(),
                type = encounter.type,
                data = encounter.getData();
          const node = this.encounterArea.Swih(type)[0];

          if (type === (_crd && MapNodeType === void 0 ? (_reportPossibleCrUseOfMapNodeType({
            error: Error()
          }), MapNodeType) : MapNodeType).TAVERN) {
            var _data$heros;

            //酒馆
            if (data != null && (_data$heros = data.heros) != null && _data$heros.length) {
              data.heros.forEach(m => {
                const hero = new (_crd && HeroObj === void 0 ? (_reportPossibleCrUseOfHeroObj({
                  error: Error()
                }), HeroObj) : HeroObj)().init(m);
                this.createHero(hero, ut.convertToNodeAR(node.Child('pos/' + hero.index), this.root).clone().toVec3());
              });
            } else {
              //清理英雄
              this.heros.delete(m => m.areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
                error: Error()
              }), AreaType) : AreaType).SHOP).forEach(m => this.cleanHero(m));
            }
          } else if (type === (_crd && MapNodeType === void 0 ? (_reportPossibleCrUseOfMapNodeType({
            error: Error()
          }), MapNodeType) : MapNodeType).ENEMY_BATTLE) {//野怪
          }
        } // 检测是否在回收区域


        checkInRecycleRange(pos) {
          return pos.x > this.recyclePosition.x && pos.x < this.recycleRoot.Width && pos.y > this.recyclePosition.y && pos.y < this.recycleRoot.Height;
        } // 找出英雄最近的区域位置


        findMinDisAreaIndex(pos) {
          let temp = v3(),
              minMag = ut.MAX_VALUE,
              areaType = -1,
              index = -1;

          for (let _areaType in this.areaPositionMap) {
            const area = this.areaPositionMap[_areaType];

            for (let _index in area) {
              const p = temp.set(area[_index]);
              const mag = p.subtract(pos).length();

              if (mag < minMag) {
                areaType = Number(_areaType);
                index = Number(_index);
                minMag = mag;
              }
            }
          }

          return [areaType, index];
        } // 购买英雄


        async buyHero(hero, areaType, index) {
          const err = await this.model.buyHero(hero.uid, areaType, index);

          if (err) {
            hero.restorePosition();
            return (_crd && vHelper === void 0 ? (_reportPossibleCrUseOfvHelper({
              error: Error()
            }), vHelper) : vHelper).showAlert(err);
          }

          this.initHeroArea();
          this.initEncounterArea();
        } // 出售英雄


        async sellHero(hero) {
          const err = await this.model.sellHero(hero.uid);

          if (err) {
            hero.restorePosition();
            return (_crd && vHelper === void 0 ? (_reportPossibleCrUseOfvHelper({
              error: Error()
            }), vHelper) : vHelper).showAlert(err);
          }

          this.initHeroArea();
        } // 移动英雄


        async moveHero(hero, areaType, index) {
          const err = await this.model.moveHero(hero.uid, areaType, index);

          if (err) {
            hero.restorePosition();
            return (_crd && vHelper === void 0 ? (_reportPossibleCrUseOfvHelper({
              error: Error()
            }), vHelper) : vHelper).showAlert(err);
          }

          this.initHeroArea();
        }

      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9f324873888fad84c013d81f65c4dc67c8d47ab0.js.map