"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = toComputedKey;
var _index = require("../validators/generated/index.js");
var _index2 = require("../builders/generated/index.js");
function toComputedKey(node, key = node.key || node.property) {
  if (!node.computed && (0, _index.isIdentifier)(key)) key = (0, _index2.stringLiteral)(key.name);
  return key;
}

//# sourceMappingURL=toComputedKey.js.map
