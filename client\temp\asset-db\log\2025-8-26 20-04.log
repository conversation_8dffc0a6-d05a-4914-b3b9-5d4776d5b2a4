2025-8-26 20:04:44-log: Cannot access game frame or container.
2025-8-26 20:04:45-debug: asset-db:require-engine-code (1246ms)
2025-8-26 20:04:45-log: meshopt wasm decoder initialized
2025-8-26 20:04:45-log: [box2d]:box2d wasm lib loaded.
2025-8-26 20:04:45-log: [bullet]:bullet wasm lib loaded.
2025-8-26 20:04:45-log: Using legacy pipeline
2025-8-26 20:04:45-log: Cocos Creator v3.8.7
2025-8-26 20:04:45-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:33.49MB, end 79.85MB, increase: 46.36MB
2025-8-26 20:04:45-log: Forward render pipeline initialized.
2025-8-26 20:04:48-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.89MB, end 274.68MB, increase: 194.79MB
2025-8-26 20:04:48-debug: asset-db:worker-init: initPlugin (2809ms)
2025-8-26 20:04:48-debug: [Assets Memory track]: asset-db:worker-init start:33.49MB, end 274.80MB, increase: 241.31MB
2025-8-26 20:04:48-debug: Run asset db hook programming:beforePreStart ...
2025-8-26 20:04:48-debug: Run asset db hook programming:beforePreStart success!
2025-8-26 20:04:48-debug: Preimport db  success
2025-8-26 20:04:48-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-26 20:04:48-debug: Run asset db hook programming:afterPreStart ...
2025-8-26 20:04:48-debug: Preimport db  success
2025-8-26 20:04:48-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-26 20:04:48-debug: starting packer-driver...
2025-8-26 20:04:48-debug: asset-db:worker-init (4549ms)
2025-8-26 20:04:48-debug: asset-db-hook-programming-beforePreStart (119ms)
2025-8-26 20:04:48-debug: asset-db-hook-engine-extends-beforePreStart (119ms)
2025-8-26 20:04:49-debug: initialize scripting environment...
2025-8-26 20:04:49-debug: [[Executor]] prepare before lock
2025-8-26 20:04:49-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-26 20:04:49-debug: [[Executor]] prepare after unlock
2025-8-26 20:04:49-debug: Run asset db hook programming:afterPreStart success!
2025-8-26 20:04:49-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-26 20:04:49-debug: [Assets Memory track]: asset-db:worker-init: preStart start:274.82MB, end 279.57MB, increase: 4.75MB
2025-8-26 20:04:49-debug: Start up the 'internal' database...
2025-8-26 20:04:49-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-26 20:04:52-debug: asset-db-hook-programming-afterPreStart (3590ms)
2025-8-26 20:04:52-debug: asset-db:worker-effect-data-processing (3207ms)
2025-8-26 20:04:52-debug: asset-db-hook-engine-extends-afterPreStart (3207ms)
2025-8-26 20:04:53-debug: Run asset db hook engine-extends:afterStartDB ...
2025-8-26 20:04:54-debug: recompile effect.bin success
2025-8-26 20:04:54-debug: Run asset db hook engine-extends:afterStartDB success!
2025-8-26 20:04:54-debug: Start up the 'assets' database...
2025-8-26 20:04:59-debug: asset-db:worker-effect-data-processing (5605ms)
2025-8-26 20:04:59-debug: asset-db-hook-engine-extends-afterStartDB (5605ms)
2025-8-26 20:04:59-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\proto\msg.d.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:04:59-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\proto\msg.js
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:04:59-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\model\game\GameModel.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:04:59-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\model\game\PlayerModel.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:04:59-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:04:59-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\HeroCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:04:59-debug: Run asset db hook engine-extends:afterStartDB ...
2025-8-26 20:05:00-debug: recompile effect.bin success
2025-8-26 20:05:00-debug: Run asset db hook engine-extends:afterStartDB success!
2025-8-26 20:05:00-debug: lazy register asset handler directory
2025-8-26 20:05:00-debug: lazy register asset handler *
2025-8-26 20:05:00-debug: lazy register asset handler text
2025-8-26 20:05:00-debug: lazy register asset handler spine-data
2025-8-26 20:05:00-debug: lazy register asset handler dragonbones
2025-8-26 20:05:00-debug: lazy register asset handler dragonbones-atlas
2025-8-26 20:05:00-debug: lazy register asset handler json
2025-8-26 20:05:00-debug: lazy register asset handler javascript
2025-8-26 20:05:00-debug: lazy register asset handler typescript
2025-8-26 20:05:00-debug: lazy register asset handler prefab
2025-8-26 20:05:00-debug: lazy register asset handler terrain
2025-8-26 20:05:00-debug: lazy register asset handler sprite-frame
2025-8-26 20:05:00-debug: lazy register asset handler tiled-map
2025-8-26 20:05:00-debug: lazy register asset handler buffer
2025-8-26 20:05:00-debug: lazy register asset handler scene
2025-8-26 20:05:00-debug: lazy register asset handler sign-image
2025-8-26 20:05:00-debug: lazy register asset handler alpha-image
2025-8-26 20:05:00-debug: lazy register asset handler texture
2025-8-26 20:05:00-debug: lazy register asset handler texture-cube
2025-8-26 20:05:00-debug: lazy register asset handler erp-texture-cube
2025-8-26 20:05:00-debug: lazy register asset handler texture-cube-face
2025-8-26 20:05:00-debug: lazy register asset handler image
2025-8-26 20:05:00-debug: lazy register asset handler rt-sprite-frame
2025-8-26 20:05:00-debug: lazy register asset handler gltf-mesh
2025-8-26 20:05:00-debug: lazy register asset handler gltf-animation
2025-8-26 20:05:00-debug: lazy register asset handler gltf
2025-8-26 20:05:00-debug: lazy register asset handler gltf-skeleton
2025-8-26 20:05:00-debug: lazy register asset handler gltf-material
2025-8-26 20:05:00-debug: lazy register asset handler gltf-embeded-image
2025-8-26 20:05:00-debug: lazy register asset handler fbx
2025-8-26 20:05:00-debug: lazy register asset handler gltf-scene
2025-8-26 20:05:00-debug: lazy register asset handler effect
2025-8-26 20:05:00-debug: lazy register asset handler effect-header
2025-8-26 20:05:00-debug: lazy register asset handler render-texture
2025-8-26 20:05:00-debug: lazy register asset handler physics-material
2025-8-26 20:05:00-debug: lazy register asset handler audio-clip
2025-8-26 20:05:00-debug: lazy register asset handler animation-clip
2025-8-26 20:05:00-debug: lazy register asset handler animation-graph
2025-8-26 20:05:00-debug: lazy register asset handler animation-graph-variant
2025-8-26 20:05:00-debug: lazy register asset handler animation-mask
2025-8-26 20:05:00-debug: lazy register asset handler material
2025-8-26 20:05:00-debug: lazy register asset handler bitmap-font
2025-8-26 20:05:00-debug: lazy register asset handler sprite-atlas
2025-8-26 20:05:00-debug: lazy register asset handler auto-atlas
2025-8-26 20:05:00-debug: lazy register asset handler ttf-font
2025-8-26 20:05:00-debug: lazy register asset handler particle
2025-8-26 20:05:00-debug: lazy register asset handler render-pipeline
2025-8-26 20:05:00-debug: lazy register asset handler render-stage
2025-8-26 20:05:00-debug: lazy register asset handler label-atlas
2025-8-26 20:05:00-debug: lazy register asset handler instantiation-material
2025-8-26 20:05:00-debug: lazy register asset handler instantiation-skeleton
2025-8-26 20:05:00-debug: lazy register asset handler render-flow
2025-8-26 20:05:00-debug: lazy register asset handler instantiation-animation
2025-8-26 20:05:00-debug: lazy register asset handler video-clip
2025-8-26 20:05:00-debug: lazy register asset handler instantiation-mesh
2025-8-26 20:05:00-debug: asset-db:worker-effect-data-processing (327ms)
2025-8-26 20:05:00-debug: asset-db-hook-engine-extends-afterStartDB (327ms)
2025-8-26 20:05:00-debug: asset-db:start-database (11508ms)
2025-8-26 20:05:00-debug: init worker message success
2025-8-26 20:05:00-debug: asset-db:ready (20150ms)
2025-8-26 20:05:00-debug: programming:execute-script (11ms)
2025-8-26 20:05:00-debug: [Build Memory track]: builder:worker-init start:229.54MB, end 240.27MB, increase: 10.73MB
2025-8-26 20:05:00-debug: builder:worker-init (785ms)
2025-8-26 20:05:37-debug: refresh db internal success
2025-8-26 20:05:37-debug: refresh db assets success
2025-8-26 20:05:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:05:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:05:37-debug: asset-db:refresh-all-database (242ms)
2025-8-26 20:05:37-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 20:05:37-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 20:06:30-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_201001.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:06:30-debug: asset-db:reimport-asset743ec44b-0b9a-4426-9ded-ad83e06aa181 (11ms)
2025-8-26 20:07:57-debug: refresh db internal success
2025-8-26 20:07:57-debug: refresh db assets success
2025-8-26 20:07:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:07:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:07:57-debug: asset-db:refresh-all-database (231ms)
2025-8-26 20:07:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 20:07:57-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 20:08:04-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_201001.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:08:04-debug: asset-db:reimport-asset743ec44b-0b9a-4426-9ded-ad83e06aa181 (11ms)
2025-8-26 20:08:11-debug: Query all assets info in project
2025-8-26 20:08:11-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 20:08:11-debug: Skip compress image, progress: 0%
2025-8-26 20:08:11-debug: Init all bundles start..., progress: 0%
2025-8-26 20:08:11-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:08:11-debug: Num of bundles: 3..., progress: 0%
2025-8-26 20:08:11-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 20:08:11-debug: Init bundle root assets start..., progress: 0%
2025-8-26 20:08:11-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 20:08:11-debug:   Number of all scripts: 123
2025-8-26 20:08:11-debug:   Number of other assets: 2023
2025-8-26 20:08:11-debug: Init bundle root assets success..., progress: 0%
2025-8-26 20:08:11-debug:   Number of all scenes: 1
2025-8-26 20:08:11-debug: // ---- build task 查询 Asset Bundle ---- (33ms)
2025-8-26 20:08:11-debug: [Build Memory track]: 查询 Asset Bundle start:235.96MB, end 236.26MB, increase: 311.11KB
2025-8-26 20:08:11-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 20:08:11-log: run build task 查询 Asset Bundle success in 33 ms√, progress: 5%
2025-8-26 20:08:11-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:08:11-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-8-26 20:08:11-log: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-8-26 20:08:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:08:11-debug: [Build Memory track]: 查询 Asset Bundle start:236.30MB, end 236.74MB, increase: 445.30KB
2025-8-26 20:08:11-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 20:08:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-26 20:08:11-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-26 20:08:11-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 20:08:11-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:236.78MB, end 236.82MB, increase: 36.98KB
2025-8-26 20:08:11-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 20:08:11-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-26 20:08:11-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-26 20:08:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:08:11-debug: [Build Memory track]: 填充脚本数据到 settings.json start:236.86MB, end 236.89MB, increase: 34.48KB
2025-8-26 20:08:11-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 20:08:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (10ms)
2025-8-26 20:08:11-log: run build task 整理部分构建选项内数据到 settings.json success in 10 ms√, progress: 15%
2025-8-26 20:08:11-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:236.93MB, end 236.36MB, increase: -585.13KB
2025-8-26 20:12:41-debug: refresh db internal success
2025-8-26 20:12:41-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:12:41-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\HeroCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:12:41-debug: refresh db assets success
2025-8-26 20:12:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:12:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:12:41-debug: asset-db:refresh-all-database (235ms)
2025-8-26 20:12:41-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-26 20:12:41-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 20:12:43-debug: Query all assets info in project
2025-8-26 20:12:43-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 20:12:43-debug: Skip compress image, progress: 0%
2025-8-26 20:12:43-debug: Init all bundles start..., progress: 0%
2025-8-26 20:12:43-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:12:43-debug: Num of bundles: 3..., progress: 0%
2025-8-26 20:12:43-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 20:12:43-debug: Init bundle root assets start..., progress: 0%
2025-8-26 20:12:43-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 20:12:43-debug:   Number of all scripts: 123
2025-8-26 20:12:43-debug:   Number of other assets: 2023
2025-8-26 20:12:43-debug: Init bundle root assets success..., progress: 0%
2025-8-26 20:12:43-debug:   Number of all scenes: 1
2025-8-26 20:12:43-debug: // ---- build task 查询 Asset Bundle ---- (33ms)
2025-8-26 20:12:43-log: run build task 查询 Asset Bundle success in 33 ms√, progress: 5%
2025-8-26 20:12:43-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 20:12:43-debug: [Build Memory track]: 查询 Asset Bundle start:243.91MB, end 240.20MB, increase: -3799.11KB
2025-8-26 20:12:43-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:12:43-debug: // ---- build task 查询 Asset Bundle ---- (10ms)
2025-8-26 20:12:43-log: run build task 查询 Asset Bundle success in 10 ms√, progress: 10%
2025-8-26 20:12:43-debug: [Build Memory track]: 查询 Asset Bundle start:240.24MB, end 240.63MB, increase: 391.67KB
2025-8-26 20:12:43-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 20:12:43-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:12:43-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-26 20:12:43-log: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 12%
2025-8-26 20:12:43-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:240.68MB, end 240.72MB, increase: 48.32KB
2025-8-26 20:12:43-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 20:12:43-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 20:12:43-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-26 20:12:43-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-26 20:12:43-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:12:43-debug: [Build Memory track]: 填充脚本数据到 settings.json start:240.76MB, end 240.85MB, increase: 86.85KB
2025-8-26 20:12:43-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 20:12:43-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-26 20:12:43-log: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-26 20:12:43-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:240.89MB, end 241.02MB, increase: 135.51KB
2025-8-26 20:13:07-debug: refresh db internal success
2025-8-26 20:13:07-debug: refresh db assets success
2025-8-26 20:13:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:13:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:13:07-debug: asset-db:refresh-all-database (219ms)
2025-8-26 20:13:07-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-26 20:13:07-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 20:13:11-debug: start refresh asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202005.prefab...
2025-8-26 20:13:11-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202005.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:13:11-debug: refresh asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero success
2025-8-26 20:13:11-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:13:15-debug: refresh db internal success
2025-8-26 20:13:15-debug: refresh db assets success
2025-8-26 20:13:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:13:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:13:15-debug: asset-db:refresh-all-database (218ms)
2025-8-26 20:13:15-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 20:13:15-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 20:13:22-debug: start move asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202005.prefab -> D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO.prefab...
2025-8-26 20:13:22-debug: start refresh asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO.prefab...
2025-8-26 20:13:22-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:13:22-debug: refresh asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero success
2025-8-26 20:13:22-debug: start refresh asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero...
2025-8-26 20:13:22-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:13:22-debug: refresh asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab success
2025-8-26 20:13:22-debug: move asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202005.prefab -> D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO.prefab success
2025-8-26 20:14:14-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:14:14-debug: asset-db:reimport-asset315816da-989c-455c-9293-c5874e0fe0e2 (12ms)
2025-8-26 20:14:45-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:14:45-debug: asset-db:reimport-asset315816da-989c-455c-9293-c5874e0fe0e2 (11ms)
2025-8-26 20:15:47-debug: refresh db internal success
2025-8-26 20:15:47-debug: refresh db assets success
2025-8-26 20:15:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:15:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:15:47-debug: asset-db:refresh-all-database (255ms)
2025-8-26 20:15:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 20:15:47-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 20:17:37-debug: refresh db internal success
2025-8-26 20:17:37-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:17:38-debug: refresh db assets success
2025-8-26 20:17:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:17:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:17:38-debug: asset-db:refresh-all-database (233ms)
2025-8-26 20:17:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 20:17:38-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 20:17:39-debug: Query all assets info in project
2025-8-26 20:17:39-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 20:17:39-debug: Skip compress image, progress: 0%
2025-8-26 20:17:39-debug: Init all bundles start..., progress: 0%
2025-8-26 20:17:39-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:17:39-debug: Num of bundles: 3..., progress: 0%
2025-8-26 20:17:39-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 20:17:39-debug: Init bundle root assets start..., progress: 0%
2025-8-26 20:17:39-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 20:17:39-debug:   Number of all scripts: 123
2025-8-26 20:17:39-debug: Init bundle root assets success..., progress: 0%
2025-8-26 20:17:39-debug:   Number of other assets: 2024
2025-8-26 20:17:39-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-8-26 20:17:39-log: run build task 查询 Asset Bundle success in 31 ms√, progress: 5%
2025-8-26 20:17:39-debug: [Build Memory track]: 查询 Asset Bundle start:234.32MB, end 231.81MB, increase: -2570.29KB
2025-8-26 20:17:39-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:17:39-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 20:17:39-debug:   Number of all scenes: 1
2025-8-26 20:17:39-debug: // ---- build task 查询 Asset Bundle ---- (7ms)
2025-8-26 20:17:39-log: run build task 查询 Asset Bundle success in 7 ms√, progress: 10%
2025-8-26 20:17:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 20:17:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:17:39-debug: [Build Memory track]: 查询 Asset Bundle start:231.85MB, end 232.24MB, increase: 397.33KB
2025-8-26 20:17:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-26 20:17:39-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-26 20:17:39-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 20:17:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:232.28MB, end 232.31MB, increase: 34.48KB
2025-8-26 20:17:39-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 20:17:39-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-26 20:17:39-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-26 20:17:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:17:39-debug: [Build Memory track]: 填充脚本数据到 settings.json start:232.36MB, end 232.39MB, increase: 33.90KB
2025-8-26 20:17:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 20:17:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-26 20:17:39-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-26 20:17:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:232.43MB, end 232.57MB, increase: 138.41KB
2025-8-26 20:19:18-debug: refresh db internal success
2025-8-26 20:19:19-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:19:19-debug: refresh db assets success
2025-8-26 20:19:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:19:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:19:19-debug: asset-db:refresh-all-database (270ms)
2025-8-26 20:19:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 20:19:19-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 20:19:20-debug: Query all assets info in project
2025-8-26 20:19:20-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 20:19:20-debug: Skip compress image, progress: 0%
2025-8-26 20:19:20-debug: Init all bundles start..., progress: 0%
2025-8-26 20:19:20-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:19:20-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 20:19:20-debug: Num of bundles: 3..., progress: 0%
2025-8-26 20:19:20-debug: Init bundle root assets start..., progress: 0%
2025-8-26 20:19:20-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 20:19:20-debug:   Number of all scripts: 123
2025-8-26 20:19:20-debug:   Number of other assets: 2024
2025-8-26 20:19:20-debug: Init bundle root assets success..., progress: 0%
2025-8-26 20:19:20-debug:   Number of all scenes: 1
2025-8-26 20:19:20-debug: // ---- build task 查询 Asset Bundle ---- (33ms)
2025-8-26 20:19:20-log: run build task 查询 Asset Bundle success in 33 ms√, progress: 5%
2025-8-26 20:19:20-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 20:19:20-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:19:20-debug: [Build Memory track]: 查询 Asset Bundle start:238.09MB, end 235.40MB, increase: -2752.14KB
2025-8-26 20:19:20-debug: // ---- build task 查询 Asset Bundle ---- (8ms)
2025-8-26 20:19:20-log: run build task 查询 Asset Bundle success in 8 ms√, progress: 10%
2025-8-26 20:19:20-debug: [Build Memory track]: 查询 Asset Bundle start:235.44MB, end 235.82MB, increase: 383.09KB
2025-8-26 20:19:20-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 20:19:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:19:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-26 20:19:20-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-26 20:19:20-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:235.86MB, end 235.91MB, increase: 50.87KB
2025-8-26 20:19:20-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 20:19:20-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 20:19:20-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-26 20:19:20-debug: [Build Memory track]: 填充脚本数据到 settings.json start:235.95MB, end 236.02MB, increase: 65.30KB
2025-8-26 20:19:20-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 20:19:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:19:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-26 20:19:20-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-26 20:19:20-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:236.06MB, end 236.19MB, increase: 133.41KB
2025-8-26 20:22:50-debug: refresh db internal success
2025-8-26 20:22:50-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\common\config\HeroFrameAnimConf.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:22:50-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\common\constant\DataType.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:22:50-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\cmpt\FrameAnimationCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:22:50-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\HeroCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:22:50-debug: refresh db assets success
2025-8-26 20:22:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:22:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:22:50-debug: asset-db:refresh-all-database (228ms)
2025-8-26 20:22:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 20:22:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-26 20:22:52-debug: Query all assets info in project
2025-8-26 20:22:52-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 20:22:52-debug: Skip compress image, progress: 0%
2025-8-26 20:22:52-debug: Num of bundles: 3..., progress: 0%
2025-8-26 20:22:52-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 20:22:52-debug: Init all bundles start..., progress: 0%
2025-8-26 20:22:52-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:22:52-debug: Init bundle root assets start..., progress: 0%
2025-8-26 20:22:52-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 20:22:52-debug:   Number of all scenes: 1
2025-8-26 20:22:52-debug:   Number of other assets: 2024
2025-8-26 20:22:52-debug: Init bundle root assets success..., progress: 0%
2025-8-26 20:22:52-debug:   Number of all scripts: 123
2025-8-26 20:22:52-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-8-26 20:22:52-log: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-8-26 20:22:52-debug: [Build Memory track]: 查询 Asset Bundle start:242.01MB, end 239.30MB, increase: -2773.86KB
2025-8-26 20:22:52-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 20:22:52-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:22:52-debug: // ---- build task 查询 Asset Bundle ---- (15ms)
2025-8-26 20:22:52-log: run build task 查询 Asset Bundle success in 15 ms√, progress: 10%
2025-8-26 20:22:52-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 20:22:52-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:22:52-debug: [Build Memory track]: 查询 Asset Bundle start:239.35MB, end 239.73MB, increase: 388.77KB
2025-8-26 20:22:52-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-26 20:22:52-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 12%
2025-8-26 20:22:52-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:239.77MB, end 239.81MB, increase: 34.40KB
2025-8-26 20:22:52-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 20:22:52-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 20:22:52-debug: // ---- build task 填充脚本数据到 settings.json ---- (4ms)
2025-8-26 20:22:52-debug: [Build Memory track]: 填充脚本数据到 settings.json start:239.85MB, end 239.90MB, increase: 53.89KB
2025-8-26 20:22:52-log: run build task 填充脚本数据到 settings.json success in 4 ms√, progress: 13%
2025-8-26 20:22:52-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 20:22:52-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:22:52-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (14ms)
2025-8-26 20:22:52-log: run build task 整理部分构建选项内数据到 settings.json success in 14 ms√, progress: 15%
2025-8-26 20:22:52-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:239.94MB, end 240.07MB, increase: 126.31KB
2025-8-26 20:23:05-debug: refresh db internal success
2025-8-26 20:23:05-debug: refresh db assets success
2025-8-26 20:23:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:23:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:23:05-debug: asset-db:refresh-all-database (234ms)
2025-8-26 20:23:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 20:23:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 20:23:16-debug: refresh db internal success
2025-8-26 20:23:16-debug: refresh db assets success
2025-8-26 20:23:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:23:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:23:16-debug: asset-db:refresh-all-database (227ms)
2025-8-26 20:23:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 20:23:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-26 20:23:26-debug: refresh db internal success
2025-8-26 20:23:26-debug: refresh db assets success
2025-8-26 20:23:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:23:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:23:26-debug: asset-db:refresh-all-database (219ms)
2025-8-26 20:23:26-debug: asset-db:worker-effect-data-processing (4ms)
2025-8-26 20:23:26-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-8-26 20:23:35-debug: refresh db internal success
2025-8-26 20:23:36-debug: refresh db assets success
2025-8-26 20:23:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:23:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:23:36-debug: asset-db:refresh-all-database (250ms)
2025-8-26 20:23:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 20:23:36-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 20:24:39-debug: refresh db internal success
2025-8-26 20:24:39-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\common\config\HeroFrameAnimConf.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:24:39-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\common\constant\DataType.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:24:39-debug: refresh db assets success
2025-8-26 20:24:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:24:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:24:39-debug: asset-db:refresh-all-database (240ms)
2025-8-26 20:24:39-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-26 20:24:39-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 20:24:40-debug: Query all assets info in project
2025-8-26 20:24:40-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 20:24:40-debug: Skip compress image, progress: 0%
2025-8-26 20:24:40-debug: Init all bundles start..., progress: 0%
2025-8-26 20:24:40-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:24:40-debug: Init bundle root assets start..., progress: 0%
2025-8-26 20:24:40-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 20:24:40-debug: Num of bundles: 3..., progress: 0%
2025-8-26 20:24:40-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 20:24:40-debug:   Number of other assets: 2024
2025-8-26 20:24:40-debug: Init bundle root assets success..., progress: 0%
2025-8-26 20:24:40-debug:   Number of all scenes: 1
2025-8-26 20:24:40-debug:   Number of all scripts: 123
2025-8-26 20:24:40-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-8-26 20:24:40-debug: [Build Memory track]: 查询 Asset Bundle start:234.48MB, end 231.86MB, increase: -2684.63KB
2025-8-26 20:24:40-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 20:24:40-log: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-8-26 20:24:40-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:24:40-debug: // ---- build task 查询 Asset Bundle ---- (11ms)
2025-8-26 20:24:40-log: run build task 查询 Asset Bundle success in 11 ms√, progress: 10%
2025-8-26 20:24:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:24:40-debug: [Build Memory track]: 查询 Asset Bundle start:231.89MB, end 232.25MB, increase: 369.89KB
2025-8-26 20:24:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 20:24:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-26 20:24:40-log: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 12%
2025-8-26 20:24:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:232.29MB, end 232.32MB, increase: 27.66KB
2025-8-26 20:24:40-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 20:24:40-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 20:24:40-debug: // ---- build task 填充脚本数据到 settings.json ---- (5ms)
2025-8-26 20:24:40-log: run build task 填充脚本数据到 settings.json success in 5 ms√, progress: 13%
2025-8-26 20:24:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:24:40-debug: [Build Memory track]: 填充脚本数据到 settings.json start:232.35MB, end 232.38MB, increase: 26.98KB
2025-8-26 20:24:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 20:24:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (8ms)
2025-8-26 20:24:40-log: run build task 整理部分构建选项内数据到 settings.json success in 8 ms√, progress: 15%
2025-8-26 20:24:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:232.41MB, end 232.54MB, increase: 131.16KB
2025-8-26 20:26:05-debug: refresh db internal success
2025-8-26 20:26:05-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\HeroCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:26:05-debug: refresh db assets success
2025-8-26 20:26:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:26:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:26:05-debug: asset-db:refresh-all-database (228ms)
2025-8-26 20:26:05-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 20:26:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 20:26:06-debug: Query all assets info in project
2025-8-26 20:26:06-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 20:26:06-debug: Skip compress image, progress: 0%
2025-8-26 20:26:06-debug: Init all bundles start..., progress: 0%
2025-8-26 20:26:06-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:26:06-debug: Num of bundles: 3..., progress: 0%
2025-8-26 20:26:06-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 20:26:06-debug: Init bundle root assets start..., progress: 0%
2025-8-26 20:26:06-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 20:26:06-debug:   Number of all scenes: 1
2025-8-26 20:26:06-debug:   Number of all scripts: 123
2025-8-26 20:26:06-debug: Init bundle root assets success..., progress: 0%
2025-8-26 20:26:06-debug:   Number of other assets: 2024
2025-8-26 20:26:06-debug: // ---- build task 查询 Asset Bundle ---- (28ms)
2025-8-26 20:26:06-log: run build task 查询 Asset Bundle success in 28 ms√, progress: 5%
2025-8-26 20:26:06-debug: [Build Memory track]: 查询 Asset Bundle start:238.23MB, end 235.55MB, increase: -2747.87KB
2025-8-26 20:26:06-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 20:26:06-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:26:06-debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-8-26 20:26:06-log: run build task 查询 Asset Bundle success in 9 ms√, progress: 10%
2025-8-26 20:26:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:26:06-debug: [Build Memory track]: 查询 Asset Bundle start:235.58MB, end 235.96MB, increase: 388.64KB
2025-8-26 20:26:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 20:26:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-8-26 20:26:06-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 12%
2025-8-26 20:26:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:235.99MB, end 236.02MB, increase: 29.11KB
2025-8-26 20:26:06-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 20:26:06-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 20:26:06-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-26 20:26:06-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-26 20:26:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:26:06-debug: [Build Memory track]: 填充脚本数据到 settings.json start:236.05MB, end 236.07MB, increase: 26.91KB
2025-8-26 20:26:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 20:26:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (9ms)
2025-8-26 20:26:06-log: run build task 整理部分构建选项内数据到 settings.json success in 9 ms√, progress: 15%
2025-8-26 20:26:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:236.11MB, end 236.23MB, increase: 127.02KB
2025-8-26 20:35:04-debug: refresh db internal success
2025-8-26 20:35:05-debug: refresh db assets success
2025-8-26 20:35:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:35:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:35:05-debug: asset-db:refresh-all-database (225ms)
2025-8-26 20:35:05-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 20:35:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 20:35:07-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202004.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:35:07-debug: asset-db:reimport-assetcf299917-95b1-4f67-8904-45e20304eb51 (12ms)
2025-8-26 20:35:10-debug: Query all assets info in project
2025-8-26 20:35:10-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 20:35:10-debug: Skip compress image, progress: 0%
2025-8-26 20:35:10-debug: Init all bundles start..., progress: 0%
2025-8-26 20:35:10-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:35:10-debug: Num of bundles: 3..., progress: 0%
2025-8-26 20:35:10-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 20:35:10-debug: Init bundle root assets start..., progress: 0%
2025-8-26 20:35:10-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 20:35:10-debug:   Number of all scripts: 123
2025-8-26 20:35:10-debug: Init bundle root assets success..., progress: 0%
2025-8-26 20:35:10-log: run build task 查询 Asset Bundle success in 27 ms√, progress: 5%
2025-8-26 20:35:10-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-8-26 20:35:10-debug: [Build Memory track]: 查询 Asset Bundle start:240.19MB, end 239.15MB, increase: -1060.08KB
2025-8-26 20:35:10-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 20:35:10-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:35:10-debug:   Number of other assets: 2024
2025-8-26 20:35:10-debug:   Number of all scenes: 1
2025-8-26 20:35:10-debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-8-26 20:35:10-log: run build task 查询 Asset Bundle success in 9 ms√, progress: 10%
2025-8-26 20:35:10-debug: [Build Memory track]: 查询 Asset Bundle start:239.18MB, end 239.55MB, increase: 372.73KB
2025-8-26 20:35:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 20:35:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:35:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-26 20:35:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:239.58MB, end 239.61MB, increase: 27.39KB
2025-8-26 20:35:10-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 12%
2025-8-26 20:35:10-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 20:35:10-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 20:35:10-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-26 20:35:10-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-26 20:35:10-debug: [Build Memory track]: 填充脚本数据到 settings.json start:239.64MB, end 239.75MB, increase: 119.82KB
2025-8-26 20:35:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 20:35:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:35:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-26 20:35:10-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-26 20:35:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:239.80MB, end 239.93MB, increase: 129.52KB
2025-8-26 20:38:25-debug: refresh db internal success
2025-8-26 20:38:25-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\common\constant\Constant.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:38:25-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\DragTouchCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:38:25-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\HeroCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:38:25-debug: refresh db assets success
2025-8-26 20:38:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:38:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:38:25-debug: asset-db:refresh-all-database (236ms)
2025-8-26 20:38:25-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 20:38:25-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 20:38:27-debug: Query all assets info in project
2025-8-26 20:38:27-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 20:38:27-debug: Skip compress image, progress: 0%
2025-8-26 20:38:27-debug: Init all bundles start..., progress: 0%
2025-8-26 20:38:27-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:38:27-debug: Num of bundles: 3..., progress: 0%
2025-8-26 20:38:27-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 20:38:27-debug: Init bundle root assets start..., progress: 0%
2025-8-26 20:38:27-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 20:38:27-debug:   Number of all scripts: 123
2025-8-26 20:38:27-debug:   Number of other assets: 2024
2025-8-26 20:38:27-debug:   Number of all scenes: 1
2025-8-26 20:38:27-debug: Init bundle root assets success..., progress: 0%
2025-8-26 20:38:27-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-8-26 20:38:27-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-8-26 20:38:27-debug: [Build Memory track]: 查询 Asset Bundle start:245.63MB, end 243.02MB, increase: -2673.93KB
2025-8-26 20:38:27-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 20:38:27-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 20:38:27-debug: // ---- build task 查询 Asset Bundle ---- (12ms)
2025-8-26 20:38:27-log: run build task 查询 Asset Bundle success in 12 ms√, progress: 10%
2025-8-26 20:38:27-debug: [Build Memory track]: 查询 Asset Bundle start:243.05MB, end 243.42MB, increase: 379.32KB
2025-8-26 20:38:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 20:38:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:38:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-8-26 20:38:27-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 12%
2025-8-26 20:38:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:243.45MB, end 243.48MB, increase: 27.57KB
2025-8-26 20:38:27-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 20:38:27-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 20:38:27-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-26 20:38:27-debug: [Build Memory track]: 填充脚本数据到 settings.json start:243.51MB, end 243.54MB, increase: 27.11KB
2025-8-26 20:38:27-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-26 20:38:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 20:38:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 20:38:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-26 20:38:27-log: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-26 20:38:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:243.57MB, end 243.75MB, increase: 181.14KB
2025-8-26 20:42:59-debug: refresh db internal success
2025-8-26 20:42:59-debug: refresh db assets success
2025-8-26 20:42:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:42:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:42:59-debug: asset-db:refresh-all-database (219ms)
2025-8-26 20:42:59-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 20:42:59-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 20:43:10-debug: programming:execute-script (2ms)
2025-8-26 20:43:11-debug: start remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_201001.prefab...
2025-8-26 20:43:11-debug: start remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202001.prefab...
2025-8-26 20:43:11-debug: start remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202002.prefab...
2025-8-26 20:43:11-debug: start remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202004.prefab...
2025-8-26 20:43:11-debug: start remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202003.prefab...
2025-8-26 20:43:11-debug: refresh db internal success
2025-8-26 20:43:11-debug: %cDestroy%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202002.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-8-26 20:43:11-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:43:11-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-8-26 20:43:11-debug: refresh db assets success
2025-8-26 20:43:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:43:11-debug: start refresh asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202002.prefab...
2025-8-26 20:43:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:43:11-debug: asset-db:refresh-all-database (302ms)
2025-8-26 20:43:11-debug: asset-db:worker-effect-data-processing (5ms)
2025-8-26 20:43:11-debug: asset-db-hook-engine-extends-afterRefresh (6ms)
2025-8-26 20:43:11-debug: refresh asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero success
2025-8-26 20:43:11-debug: remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202002.prefab success
2025-8-26 20:43:11-debug: start refresh asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202004.prefab...
2025-8-26 20:43:11-debug: %cDestroy%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202004.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-8-26 20:43:11-debug: refresh asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero success
2025-8-26 20:43:11-debug: remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202004.prefab success
2025-8-26 20:43:11-debug: start refresh asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202003.prefab...
2025-8-26 20:43:11-debug: %cDestroy%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202003.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-8-26 20:43:11-debug: start refresh asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_201001.prefab...
2025-8-26 20:43:11-debug: %cDestroy%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_201001.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-8-26 20:43:11-debug: refresh asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero success
2025-8-26 20:43:11-debug: remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202003.prefab success
2025-8-26 20:43:11-debug: refresh asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero success
2025-8-26 20:43:11-debug: remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_201001.prefab success
2025-8-26 20:43:11-debug: start refresh asset from D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202001.prefab...
2025-8-26 20:43:11-debug: %cDestroy%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202001.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-8-26 20:43:11-debug: refresh asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero success
2025-8-26 20:43:11-debug: remove asset D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero\HERO_202001.prefab success
2025-8-26 20:43:11-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\tmp\prefab\hero
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:43:35-debug: refresh db internal success
2025-8-26 20:43:35-debug: refresh db assets success
2025-8-26 20:43:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:43:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:43:35-debug: asset-db:refresh-all-database (243ms)
2025-8-26 20:43:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 20:43:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-26 20:43:45-debug: refresh db internal success
2025-8-26 20:43:46-debug: refresh db assets success
2025-8-26 20:43:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:43:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:43:46-debug: asset-db:refresh-all-database (235ms)
2025-8-26 20:44:46-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\view\game\GameWind.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:44:46-debug: asset-db:reimport-assetc7a3e8ba-e61a-4454-9329-cef01e6896f6 (17ms)
2025-8-26 20:44:49-debug: refresh db internal success
2025-8-26 20:44:50-debug: refresh db assets success
2025-8-26 20:44:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:44:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:44:50-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 20:44:50-debug: asset-db:refresh-all-database (234ms)
2025-8-26 20:44:50-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 20:45:12-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\view\game\GameWind.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:45:12-debug: asset-db:reimport-assetc7a3e8ba-e61a-4454-9329-cef01e6896f6 (17ms)
2025-8-26 20:45:48-debug: refresh db internal success
2025-8-26 20:45:48-debug: refresh db assets success
2025-8-26 20:45:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:45:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:45:48-debug: asset-db:refresh-all-database (233ms)
2025-8-26 20:45:48-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-26 20:45:48-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 20:46:13-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\view\game\GameWind.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 20:46:13-debug: asset-db:reimport-assetc7a3e8ba-e61a-4454-9329-cef01e6896f6 (19ms)
2025-8-26 20:46:26-debug: refresh db internal success
2025-8-26 20:46:26-debug: refresh db assets success
2025-8-26 20:46:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 20:46:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 20:46:26-debug: asset-db:refresh-all-database (255ms)
2025-8-26 20:46:26-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 20:46:26-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-26 21:02:34-debug: refresh db internal success
2025-8-26 21:02:34-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\common\event\EventType.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 21:02:34-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\DragTouchCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 21:02:34-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 21:02:34-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\HeroCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 21:02:34-debug: refresh db assets success
2025-8-26 21:02:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:02:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:02:34-debug: asset-db:refresh-all-database (255ms)
2025-8-26 21:02:34-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-26 21:02:34-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-8-26 21:02:35-debug: Query all assets info in project
2025-8-26 21:02:35-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 21:02:35-debug: Skip compress image, progress: 0%
2025-8-26 21:02:35-debug: Init all bundles start..., progress: 0%
2025-8-26 21:02:35-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 21:02:35-debug: Num of bundles: 3..., progress: 0%
2025-8-26 21:02:35-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 21:02:35-debug: Init bundle root assets start..., progress: 0%
2025-8-26 21:02:35-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 21:02:35-debug:   Number of all scenes: 1
2025-8-26 21:02:35-debug:   Number of other assets: 2019
2025-8-26 21:02:35-debug: Init bundle root assets success..., progress: 0%
2025-8-26 21:02:35-debug:   Number of all scripts: 123
2025-8-26 21:02:35-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-8-26 21:02:35-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-8-26 21:02:35-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 21:02:35-debug: [Build Memory track]: 查询 Asset Bundle start:252.33MB, end 249.79MB, increase: -2607.42KB
2025-8-26 21:02:35-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 21:02:35-debug: // ---- build task 查询 Asset Bundle ---- (10ms)
2025-8-26 21:02:35-log: run build task 查询 Asset Bundle success in 10 ms√, progress: 10%
2025-8-26 21:02:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 21:02:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 21:02:35-debug: [Build Memory track]: 查询 Asset Bundle start:249.82MB, end 250.18MB, increase: 370.34KB
2025-8-26 21:02:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-26 21:02:35-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 12%
2025-8-26 21:02:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:250.21MB, end 250.24MB, increase: 27.51KB
2025-8-26 21:02:35-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 21:02:35-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 21:02:35-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-26 21:02:35-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-26 21:02:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 21:02:35-debug: [Build Memory track]: 填充脚本数据到 settings.json start:250.27MB, end 250.30MB, increase: 27.21KB
2025-8-26 21:02:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 21:02:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (8ms)
2025-8-26 21:02:35-log: run build task 整理部分构建选项内数据到 settings.json success in 8 ms√, progress: 15%
2025-8-26 21:02:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:250.33MB, end 250.46MB, increase: 136.29KB
2025-8-26 21:05:04-debug: refresh db internal success
2025-8-26 21:05:04-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\common\event\EventType.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 21:05:04-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 21:05:04-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\app\script\view\game\HeroCmpt.ts
background: #aaff85; color: #000;
color: #000;
2025-8-26 21:05:04-debug: refresh db assets success
2025-8-26 21:05:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:05:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:05:04-debug: asset-db:refresh-all-database (262ms)
2025-8-26 21:05:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 21:05:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-26 21:05:06-debug: Query all assets info in project
2025-8-26 21:05:06-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 21:05:06-debug: Skip compress image, progress: 0%
2025-8-26 21:05:06-debug: Init all bundles start..., progress: 0%
2025-8-26 21:05:06-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 21:05:06-debug: Init bundle root assets start..., progress: 0%
2025-8-26 21:05:06-debug: Num of bundles: 3..., progress: 0%
2025-8-26 21:05:06-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 21:05:06-debug:   Number of all scenes: 1
2025-8-26 21:05:06-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 21:05:06-debug:   Number of other assets: 2019
2025-8-26 21:05:06-debug: Init bundle root assets success..., progress: 0%
2025-8-26 21:05:06-debug:   Number of all scripts: 123
2025-8-26 21:05:06-debug: // ---- build task 查询 Asset Bundle ---- (28ms)
2025-8-26 21:05:06-debug: [Build Memory track]: 查询 Asset Bundle start:229.19MB, end 231.57MB, increase: 2.38MB
2025-8-26 21:05:06-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 21:05:06-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 21:05:06-log: run build task 查询 Asset Bundle success in 28 ms√, progress: 5%
2025-8-26 21:05:06-debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-8-26 21:05:06-log: run build task 查询 Asset Bundle success in 9 ms√, progress: 10%
2025-8-26 21:05:06-debug: [Build Memory track]: 查询 Asset Bundle start:231.60MB, end 231.96MB, increase: 371.45KB
2025-8-26 21:05:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 21:05:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 21:05:06-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 12%
2025-8-26 21:05:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:232.00MB, end 232.02MB, increase: 27.51KB
2025-8-26 21:05:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-8-26 21:05:06-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 21:05:06-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 21:05:06-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-26 21:05:06-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-26 21:05:06-debug: [Build Memory track]: 填充脚本数据到 settings.json start:232.05MB, end 232.08MB, increase: 27.13KB
2025-8-26 21:05:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 21:05:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 21:05:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-26 21:05:06-log: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-26 21:05:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:232.11MB, end 232.26MB, increase: 148.66KB
2025-8-26 21:05:40-debug: refresh db internal success
2025-8-26 21:05:40-debug: refresh db assets success
2025-8-26 21:05:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:05:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:05:40-debug: asset-db:refresh-all-database (228ms)
2025-8-26 21:05:40-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 21:05:40-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 21:05:57-debug: refresh db internal success
2025-8-26 21:05:57-debug: refresh db assets success
2025-8-26 21:05:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:05:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:05:57-debug: asset-db:refresh-all-database (216ms)
2025-8-26 21:06:02-debug: refresh db internal success
2025-8-26 21:06:02-debug: refresh db assets success
2025-8-26 21:06:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:06:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:06:02-debug: asset-db:refresh-all-database (222ms)
2025-8-26 21:06:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 21:06:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-26 21:06:10-debug: %cImport%c: D:\Projects\auto-chess-client\client\assets\resources\view\game\GameWind.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-26 21:06:10-debug: asset-db:reimport-assetc7a3e8ba-e61a-4454-9329-cef01e6896f6 (17ms)
2025-8-26 21:06:12-debug: Query all assets info in project
2025-8-26 21:06:12-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-26 21:06:12-debug: Skip compress image, progress: 0%
2025-8-26 21:06:12-debug: Init all bundles start..., progress: 0%
2025-8-26 21:06:12-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 21:06:12-debug: Num of bundles: 3..., progress: 0%
2025-8-26 21:06:12-debug: 查询 Asset Bundle start, progress: 0%
2025-8-26 21:06:12-debug: Init bundle root assets start..., progress: 0%
2025-8-26 21:06:12-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1,5c601d96-e4c7-4698-991b-7ee674b11079
2025-8-26 21:06:12-debug:   Number of all scenes: 1
2025-8-26 21:06:12-debug:   Number of other assets: 2019
2025-8-26 21:06:12-debug: Init bundle root assets success..., progress: 0%
2025-8-26 21:06:12-debug:   Number of all scripts: 123
2025-8-26 21:06:12-debug: // ---- build task 查询 Asset Bundle ---- (32ms)
2025-8-26 21:06:12-log: run build task 查询 Asset Bundle success in 32 ms√, progress: 5%
2025-8-26 21:06:12-debug: 查询 Asset Bundle start, progress: 5%
2025-8-26 21:06:12-debug: [Build Memory track]: 查询 Asset Bundle start:235.98MB, end 236.51MB, increase: 548.46KB
2025-8-26 21:06:12-debug: // ---- build task 查询 Asset Bundle ----
2025-8-26 21:06:12-debug: // ---- build task 查询 Asset Bundle ---- (12ms)
2025-8-26 21:06:12-log: run build task 查询 Asset Bundle success in 12 ms√, progress: 10%
2025-8-26 21:06:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-26 21:06:12-debug: [Build Memory track]: 查询 Asset Bundle start:236.54MB, end 236.90MB, increase: 369.20KB
2025-8-26 21:06:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 21:06:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-26 21:06:12-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 12%
2025-8-26 21:06:12-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:236.94MB, end 236.96MB, increase: 27.58KB
2025-8-26 21:06:12-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-26 21:06:12-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-26 21:06:12-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-26 21:06:12-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-26 21:06:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-26 21:06:12-debug: [Build Memory track]: 填充脚本数据到 settings.json start:237.00MB, end 237.03MB, increase: 27.05KB
2025-8-26 21:06:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-26 21:06:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (11ms)
2025-8-26 21:06:12-log: run build task 整理部分构建选项内数据到 settings.json success in 11 ms√, progress: 15%
2025-8-26 21:06:12-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:237.06MB, end 236.44MB, increase: -629.40KB
2025-8-26 21:06:33-debug: refresh db internal success
2025-8-26 21:06:33-debug: refresh db assets success
2025-8-26 21:06:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:06:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:06:33-debug: asset-db:refresh-all-database (215ms)
2025-8-26 21:06:33-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 21:06:33-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 21:06:52-debug: refresh db internal success
2025-8-26 21:06:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:06:52-debug: refresh db assets success
2025-8-26 21:06:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:06:52-debug: asset-db:refresh-all-database (218ms)
2025-8-26 21:06:52-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-26 21:06:52-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-26 21:07:02-debug: refresh db internal success
2025-8-26 21:07:02-debug: refresh db assets success
2025-8-26 21:07:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:07:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:07:02-debug: asset-db:refresh-all-database (213ms)
2025-8-26 21:07:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 21:07:02-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 21:07:04-debug: refresh db internal success
2025-8-26 21:07:04-debug: refresh db assets success
2025-8-26 21:07:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:07:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:07:04-debug: asset-db:refresh-all-database (209ms)
2025-8-26 21:07:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-26 21:07:04-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-26 21:07:39-debug: refresh db internal success
2025-8-26 21:07:39-debug: refresh db assets success
2025-8-26 21:07:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-26 21:07:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-26 21:07:39-debug: asset-db:refresh-all-database (223ms)
2025-8-26 21:07:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
