System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, error, v3, _crd, HERO_FRAME_ANIM_CONF;

  // 获取英雄的帧动画配置
  function getHeroFrameAnimConf(id) {
    const conf = HERO_FRAME_ANIM_CONF[id];

    if (!conf) {
      error('getHeroFrameAnimConf error. id: ' + id);
      return null;
    } else if (!conf.url) {
      conf.url = `hero/${id}/hero_${id}_`;
    }

    return conf;
  } // 英雄动画帧配置


  _export("getHeroFrameAnimConf", getHeroFrameAnimConf);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      error = _cc.error;
      v3 = _cc.v3;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9332aOWi0VHzaY2wXCvipWP", "HeroFrameAnimConf", undefined);

      __checkObsolete__(['error', 'v3']);

      HERO_FRAME_ANIM_CONF = {
        201001: {
          //张辽
          offset: v3(-4, -40),
          anims: [{
            name: 'idle',
            interval: 180,
            loop: true,
            frameIndexs: ['02', '03', '03']
          }, {
            name: 'caught',
            interval: 100,
            loop: true,
            frameIndexs: ['06', '07', '08', '09', '10']
          }, {
            name: 'attack',
            interval: 120,
            loop: false,
            frameIndexs: ['01', '13', '14', '15', '16']
          }, {
            name: 'hit',
            interval: 100,
            loop: false,
            frameIndexs: ['01', '27', '28', '29']
          }, {
            name: 'die',
            interval: 160,
            loop: false,
            frameIndexs: ['01', '29', '30', '31', '32', '33', '00', '33', '00', '33']
          }]
        },
        202001: {
          //刘备
          offset: v3(10, -68),
          anims: [{
            name: 'idle',
            interval: 180,
            loop: true,
            frameIndexs: ['02', '03', '03']
          }, {
            name: 'caught',
            interval: 100,
            loop: true,
            frameIndexs: ['06', '07', '08', '09', '10']
          }]
        },
        202002: {
          //关羽
          offset: v3(14, -68),
          anims: [{
            name: 'idle',
            interval: 180,
            loop: true,
            frameIndexs: ['02', '03', '03']
          }, {
            name: 'caught',
            interval: 100,
            loop: true,
            frameIndexs: ['06', '07', '08', '09', '10']
          }]
        },
        202003: {
          //张飞
          offset: v3(16, -148),
          anims: [{
            name: 'idle',
            interval: 180,
            loop: true,
            frameIndexs: ['02', '03', '03']
          }, {
            name: 'caught',
            interval: 100,
            loop: true,
            frameIndexs: ['06', '07', '08', '09', '10']
          }]
        },
        202004: {
          //赵云
          offset: v3(14, -76),
          anims: [{
            name: 'idle',
            interval: 180,
            loop: true,
            frameIndexs: ['02', '03', '03']
          }, {
            name: 'caught',
            interval: 100,
            loop: true,
            frameIndexs: ['06', '07', '08', '09', '10']
          }]
        }
      };

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=25c02ef38f2c47cefc0dd27e46bde6c03fa39df3.js.map