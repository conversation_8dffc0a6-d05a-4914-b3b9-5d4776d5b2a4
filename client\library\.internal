{"version": "1.0.1", "data": {"paths": ["chunks", "default-terrain", "default_cubemap", "default_file_content", "default_fonts", "default_materials", "default_prefab", "default_renderpipeline", "default_skybox", "default_ui", "dependencies", "effects", "gizmo", "physics", "tools", "chunks\\builtin", "chunks\\common", "chunks\\legacy", "chunks\\lighting-models", "chunks\\post-process", "chunks\\shading-entries", "chunks\\surfaces", "default_file_content\\animation-clip", "default_file_content\\animation-graph", "default_file_content\\animation-graph-variant", "default_file_content\\animation-mask", "default_file_content\\auto-atlas", "default_file_content\\effect", "default_file_content\\effect-header", "default_file_content\\label-atlas", "default_file_content\\material", "default_file_content\\physics-material", "default_file_content\\prefab", "default_file_content\\render-pipeline", "default_file_content\\render-texture", "default_file_content\\scene", "default_file_content\\terrain", "default_file_content\\typescript", "default_fonts\\builtin-bitmap", "default_fonts\\builtin-freetype", "default_prefab\\2d", "default_prefab\\3d", "default_prefab\\effects", "default_prefab\\light", "default_prefab\\ui", "dependencies\\textures", "effects\\advanced", "effects\\for2d", "effects\\internal", "effects\\legacy", "effects\\particles", "effects\\pipeline", "effects\\util", "chunks\\builtin\\functionalities", "chunks\\builtin\\internal", "chunks\\builtin\\uniforms", "chunks\\common\\color", "chunks\\common\\data", "chunks\\common\\debug", "chunks\\common\\effect", "chunks\\common\\graph-expression", "chunks\\common\\lighting", "chunks\\common\\math", "chunks\\common\\mesh", "chunks\\common\\shadow", "chunks\\common\\texture", "chunks\\legacy\\main-functions", "chunks\\lighting-models\\data-structures", "chunks\\lighting-models\\default-functions", "chunks\\lighting-models\\includes", "chunks\\lighting-models\\lighting-flow", "chunks\\lighting-models\\model-functions", "chunks\\shading-entries\\data-structures", "chunks\\shading-entries\\main-functions", "chunks\\surfaces\\data-structures", "chunks\\surfaces\\default-functions", "chunks\\surfaces\\effect-macros", "chunks\\surfaces\\includes", "chunks\\surfaces\\module-functions", "default_file_content\\animation-graph\\ts-animation-graph", "default_file_content\\effect-header\\chunk", "default_file_content\\render-pipeline\\ts-render-flow", "default_file_content\\render-pipeline\\ts-render-pipeline", "default_file_content\\render-pipeline\\ts-render-stage", "default_file_content\\typescript\\ts", "default_prefab\\2d\\ui", "dependencies\\textures\\lut", "effects\\internal\\editor", "effects\\pipeline\\post-process", "effects\\util\\dcc", "chunks\\shading-entries\\main-functions\\misc", "chunks\\shading-entries\\main-functions\\render-planar-shadow", "chunks\\shading-entries\\main-functions\\render-to-reflectmap", "chunks\\shading-entries\\main-functions\\render-to-scene", "chunks\\shading-entries\\main-functions\\render-to-shadowmap", "effects\\pipeline\\post-process\\chunks", "effects\\util\\dcc\\vat", "chunks\\shading-entries\\main-functions\\render-to-scene\\pipeline", "chunks\\deprecated.chunk", "chunks\\common\\common-define.chunk", "chunks\\legacy\\decode-base.chunk", "chunks\\legacy\\decode-standard.chunk", "chunks\\legacy\\decode.chunk", "chunks\\legacy\\fog-base.chunk", "chunks\\legacy\\fog-fs.chunk", "chunks\\legacy\\fog-vs.chunk", "chunks\\legacy\\input-standard.chunk", "chunks\\legacy\\input.chunk", "chunks\\legacy\\lighting.chunk", "chunks\\legacy\\lightingmap-fs.chunk", "chunks\\legacy\\lightingmap-vs.chunk", "chunks\\legacy\\local-batch.chunk", "chunks\\legacy\\morph.chunk", "chunks\\legacy\\output-standard.chunk", "chunks\\legacy\\output.chunk", "chunks\\legacy\\sh-fs.chunk", "chunks\\legacy\\sh-vs.chunk", "chunks\\legacy\\shading-cluster-additive.chunk", "chunks\\legacy\\shading-standard-additive.chunk", "chunks\\legacy\\shading-standard-base.chunk", "chunks\\legacy\\shading-standard.chunk", "chunks\\legacy\\shading-toon.chunk", "chunks\\legacy\\shadow-map-base.chunk", "chunks\\legacy\\shadow-map-fs.chunk", "chunks\\legacy\\shadow-map-vs.chunk", "chunks\\legacy\\skinning.chunk", "chunks\\legacy\\standard-surface-entry.chunk", "chunks\\post-process\\anti-aliasing.chunk", "chunks\\post-process\\fxaa-hq.chunk", "chunks\\post-process\\fxaa.chunk", "chunks\\post-process\\pipeline.chunk", "effects\\advanced\\common-functions.chunk", "effects\\advanced\\eye.chunk", "chunks\\builtin\\functionalities\\fog.chunk", "chunks\\builtin\\functionalities\\morph-animation.chunk", "chunks\\builtin\\functionalities\\probe.chunk", "chunks\\builtin\\functionalities\\sh.chunk", "chunks\\builtin\\functionalities\\shadow-map.chunk", "chunks\\builtin\\functionalities\\skinning-animation-dqs.chunk", "chunks\\builtin\\functionalities\\skinning-animation-lbs.chunk", "chunks\\builtin\\functionalities\\world-transform.chunk", "chunks\\builtin\\internal\\alpha-test.chunk", "chunks\\builtin\\internal\\embedded-alpha.chunk", "chunks\\builtin\\internal\\particle-common.chunk", "chunks\\builtin\\internal\\particle-trail.chunk", "chunks\\builtin\\internal\\particle-vs-gpu.chunk", "chunks\\builtin\\internal\\particle-vs-legacy.chunk", "chunks\\builtin\\internal\\sprite-common.chunk", "chunks\\builtin\\internal\\sprite-texture.chunk", "chunks\\builtin\\uniforms\\cc-csm.chunk", "chunks\\builtin\\uniforms\\cc-diffusemap.chunk", "chunks\\builtin\\uniforms\\cc-environment.chunk", "chunks\\builtin\\uniforms\\cc-forward-light.chunk", "chunks\\builtin\\uniforms\\cc-global.chunk", "chunks\\builtin\\uniforms\\cc-light-map.chunk", "chunks\\builtin\\uniforms\\cc-local-batched.chunk", "chunks\\builtin\\uniforms\\cc-local.chunk", "chunks\\builtin\\uniforms\\cc-morph.chunk", "chunks\\builtin\\uniforms\\cc-reflection-probe.chunk", "chunks\\builtin\\uniforms\\cc-sh.chunk", "chunks\\builtin\\uniforms\\cc-shadow-map.chunk", "chunks\\builtin\\uniforms\\cc-shadow.chunk", "chunks\\builtin\\uniforms\\cc-skinning.chunk", "chunks\\builtin\\uniforms\\cc-world-bound.chunk", "chunks\\common\\color\\aces.chunk", "chunks\\common\\color\\gamma.chunk", "chunks\\common\\color\\tone-mapping.chunk", "chunks\\common\\data\\packing.chunk", "chunks\\common\\data\\unpack.chunk", "chunks\\common\\debug\\debug-view-define.chunk", "chunks\\common\\effect\\fog.chunk", "chunks\\common\\effect\\special-effects.chunk", "chunks\\common\\graph-expression\\base.chunk", "chunks\\common\\lighting\\attenuation.chunk", "chunks\\common\\lighting\\brdf.chunk", "chunks\\common\\lighting\\bxdf.chunk", "chunks\\common\\lighting\\functions.chunk", "chunks\\common\\lighting\\light-map.chunk", "chunks\\common\\lighting\\rect-area-light.chunk", "chunks\\common\\math\\coordinates.chunk", "chunks\\common\\math\\number.chunk", "chunks\\common\\math\\octahedron-transform.chunk", "chunks\\common\\math\\transform.chunk", "chunks\\common\\mesh\\material.chunk", "chunks\\common\\mesh\\vat-animation.chunk", "chunks\\common\\shadow\\native-pcf.chunk", "chunks\\common\\texture\\cubemap.chunk", "chunks\\common\\texture\\texture-lod.chunk", "chunks\\common\\texture\\texture-misc.chunk", "chunks\\legacy\\main-functions\\general-vs.chunk", "chunks\\legacy\\main-functions\\outline-fs.chunk", "chunks\\legacy\\main-functions\\outline-vs.chunk", "chunks\\lighting-models\\data-structures\\lighting-intermediate-data.chunk", "chunks\\lighting-models\\data-structures\\lighting-misc-data.chunk", "chunks\\lighting-models\\data-structures\\lighting-result.chunk", "chunks\\lighting-models\\default-functions\\simple-skin.chunk", "chunks\\lighting-models\\default-functions\\skin.chunk", "chunks\\lighting-models\\default-functions\\standard.chunk", "chunks\\lighting-models\\default-functions\\toon.chunk", "chunks\\lighting-models\\includes\\common.chunk", "chunks\\lighting-models\\includes\\standard.chunk", "chunks\\lighting-models\\includes\\toon.chunk", "chunks\\lighting-models\\includes\\unlit.chunk", "chunks\\lighting-models\\lighting-flow\\common-flow.chunk", "chunks\\lighting-models\\lighting-flow\\unlit-flow.chunk", "chunks\\lighting-models\\model-functions\\standard-common.chunk", "chunks\\lighting-models\\model-functions\\standard.chunk", "chunks\\lighting-models\\model-functions\\toon.chunk", "chunks\\shading-entries\\data-structures\\fs-input.chunk", "chunks\\shading-entries\\data-structures\\vs-fs.chunk", "chunks\\shading-entries\\data-structures\\vs-input.chunk", "chunks\\shading-entries\\data-structures\\vs-intermediate.chunk", "chunks\\shading-entries\\data-structures\\vs-output.chunk", "chunks\\surfaces\\data-structures\\standard.chunk", "chunks\\surfaces\\data-structures\\toon.chunk", "chunks\\surfaces\\data-structures\\unlit.chunk", "chunks\\surfaces\\default-functions\\common-vs.chunk", "chunks\\surfaces\\default-functions\\skin.chunk", "chunks\\surfaces\\default-functions\\standard-fs.chunk", "chunks\\surfaces\\default-functions\\toon-fs.chunk", "chunks\\surfaces\\default-functions\\unlit-fs.chunk", "chunks\\surfaces\\effect-macros\\common-macros.chunk", "chunks\\surfaces\\effect-macros\\render-planar-shadow.chunk", "chunks\\surfaces\\effect-macros\\render-to-shadowmap.chunk", "chunks\\surfaces\\effect-macros\\silhouette-edge.chunk", "chunks\\surfaces\\effect-macros\\sky.chunk", "chunks\\surfaces\\effect-macros\\terrain.chunk", "chunks\\surfaces\\effect-macros\\unlit.chunk", "chunks\\surfaces\\includes\\common-fs.chunk", "chunks\\surfaces\\includes\\common-vs.chunk", "chunks\\surfaces\\includes\\standard-fs.chunk", "chunks\\surfaces\\includes\\standard-vs.chunk", "chunks\\surfaces\\includes\\toon-fs.chunk", "chunks\\surfaces\\includes\\toon-vs.chunk", "chunks\\surfaces\\includes\\unlit-fs.chunk", "chunks\\surfaces\\module-functions\\common-vs.chunk", "chunks\\surfaces\\module-functions\\debug-view.chunk", "chunks\\surfaces\\module-functions\\standard-fs.chunk", "chunks\\surfaces\\module-functions\\toon-fs.chunk", "chunks\\surfaces\\module-functions\\unlit-fs.chunk", "chunks\\shading-entries\\main-functions\\misc\\silhouette-edge-fs.chunk", "chunks\\shading-entries\\main-functions\\misc\\silhouette-edge-vs.chunk", "chunks\\shading-entries\\main-functions\\misc\\sky-fs.chunk", "chunks\\shading-entries\\main-functions\\misc\\sky-vs.chunk", "chunks\\shading-entries\\main-functions\\render-planar-shadow\\fs.chunk", "chunks\\shading-entries\\main-functions\\render-planar-shadow\\vs.chunk", "chunks\\shading-entries\\main-functions\\render-to-reflectmap\\fs.chunk", "chunks\\shading-entries\\main-functions\\render-to-scene\\fs.chunk", "chunks\\shading-entries\\main-functions\\render-to-scene\\vs.chunk", "chunks\\shading-entries\\main-functions\\render-to-shadowmap\\fs.chunk", "chunks\\shading-entries\\main-functions\\render-to-shadowmap\\vs.chunk", "effects\\pipeline\\post-process\\chunks\\depth.chunk", "effects\\pipeline\\post-process\\chunks\\fsr.chunk", "effects\\pipeline\\post-process\\chunks\\hbao.chunk", "effects\\pipeline\\post-process\\chunks\\vs.chunk", "effects\\pipeline\\post-process\\chunks\\vs1.chunk", "chunks\\shading-entries\\main-functions\\render-to-scene\\pipeline\\deferred-fs.chunk", "chunks\\shading-entries\\main-functions\\render-to-scene\\pipeline\\forward-fs.chunk", "effects\\builtin-reflection-probe-preview.effect", "effects\\builtin-standard.effect", "effects\\builtin-terrain.effect", "effects\\builtin-toon.effect", "effects\\builtin-unlit.effect", "default_file_content\\effect\\default.effect", "default_file_content\\effect\\effect-surface.effect", "effects\\advanced\\car-paint.effect", "effects\\advanced\\eye.effect", "effects\\advanced\\fabric.effect", "effects\\advanced\\glass.effect", "effects\\advanced\\hair.effect", "effects\\advanced\\leaf.effect", "effects\\advanced\\simple-skin.effect", "effects\\advanced\\skin.effect", "effects\\advanced\\sky.effect", "effects\\advanced\\water.effect", "effects\\for2d\\builtin-spine.effect", "effects\\for2d\\builtin-sprite-renderer.effect", "effects\\for2d\\builtin-sprite.effect", "effects\\internal\\builtin-camera-texture.effect", "effects\\internal\\builtin-clear-stencil.effect", "effects\\internal\\builtin-debug-renderer.effect", "effects\\internal\\builtin-geometry-renderer.effect", "effects\\internal\\builtin-graphics.effect", "effects\\internal\\builtin-occlusion-query.effect", "effects\\internal\\builtin-wireframe.effect", "effects\\legacy\\standard.effect", "effects\\legacy\\terrain.effect", "effects\\legacy\\toon.effect", "effects\\particles\\builtin-billboard.effect", "effects\\particles\\builtin-particle-gpu.effect", "effects\\particles\\builtin-particle-trail.effect", "effects\\particles\\builtin-particle-xr-trail.effect", "effects\\particles\\builtin-particle.effect", "effects\\pipeline\\cluster-build.effect", "effects\\pipeline\\cluster-culling.effect", "effects\\pipeline\\copy-pass.effect", "effects\\pipeline\\deferred-lighting.effect", "effects\\pipeline\\float-output-process.effect", "effects\\pipeline\\planar-shadow.effect", "effects\\pipeline\\post-process.effect", "effects\\pipeline\\skybox.effect", "effects\\pipeline\\smaa.effect", "effects\\pipeline\\ssss-blur.effect", "effects\\pipeline\\tonemap.effect", "effects\\util\\batched-unlit.effect", "effects\\util\\profiler.effect", "effects\\util\\sequence-anim.effect", "effects\\util\\splash-screen.effect", "effects\\internal\\editor\\box-height-light.effect", "effects\\internal\\editor\\gizmo.effect", "effects\\internal\\editor\\grid-2d.effect", "effects\\internal\\editor\\grid-stroke.effect", "effects\\internal\\editor\\grid.effect", "effects\\internal\\editor\\light-probe-visualization.effect", "effects\\internal\\editor\\light.effect", "effects\\internal\\editor\\terrain-circle-brush.effect", "effects\\internal\\editor\\terrain-image-brush.effect", "effects\\internal\\editor\\terrain-select-brush.effect", "effects\\pipeline\\post-process\\blit-screen.effect", "effects\\pipeline\\post-process\\bloom-kawase-dual.effect", "effects\\pipeline\\post-process\\bloom-mipmap-blur.effect", "effects\\pipeline\\post-process\\bloom.effect", "effects\\pipeline\\post-process\\color-grading.effect", "effects\\pipeline\\post-process\\color-grading1.effect", "effects\\pipeline\\post-process\\dof.effect", "effects\\pipeline\\post-process\\dof1.effect", "effects\\pipeline\\post-process\\fsr.effect", "effects\\pipeline\\post-process\\fsr1.effect", "effects\\pipeline\\post-process\\fxaa-hq.effect", "effects\\pipeline\\post-process\\fxaa-hq1.effect", "effects\\pipeline\\post-process\\hbao.effect", "effects\\pipeline\\post-process\\post-final.effect", "effects\\pipeline\\post-process\\taa.effect", "effects\\pipeline\\post-process\\tone-mapping.effect", "effects\\util\\dcc\\imported-metallic-roughness.effect", "effects\\util\\dcc\\imported-specular-glossiness.effect", "effects\\util\\dcc\\vat\\houdini-fluid-v3-liquid.effect", "effects\\util\\dcc\\vat\\houdini-rigidbody-v2.effect", "effects\\util\\dcc\\vat\\houdini-softbody-v3.effect", "effects\\util\\dcc\\vat\\zeno-fluid-liquid.effect", "Default-Particle.png", "default-video.mp4", "primitives.fbx", "default-terrain\\default-layer-texture.jpg", "default_cubemap\\back.jpg", "default_cubemap\\bottom.jpg", "default_cubemap\\front.jpg", "default_cubemap\\left.jpg", "default_cubemap\\right.jpg", "default_cubemap\\top.jpg", "default_materials\\default-billboard-material.mtl", "default_materials\\default-clear-stencil.mtl", "default_materials\\default-material-transparent.mtl", "default_materials\\default-material.mtl", "default_materials\\default-particle-gpu-material.mtl", "default_materials\\default-particle-material.mtl", "default_materials\\default-spine-material.mtl", "default_materials\\default-sprite-renderer-material.mtl", "default_materials\\default-trail-material.mtl", "default_materials\\missing-effect-material.mtl", "default_materials\\missing-material.mtl", "default_materials\\particle-add.mtl", "default_materials\\standard-material.mtl", "default_materials\\ui-alpha-test-material.mtl", "default_materials\\ui-base-material.mtl", "default_materials\\ui-graphics-material.mtl", "default_materials\\ui-sprite-alpha-sep-material.mtl", "default_materials\\ui-sprite-gray-alpha-sep-material.mtl", "default_materials\\ui-sprite-gray-material.mtl", "default_materials\\ui-sprite-material.mtl", "default_prefab\\Camera.prefab", "default_prefab\\Terrain.prefab", "default_renderpipeline\\builtin-color-grading.mtl", "default_renderpipeline\\builtin-deferred.rpp", "default_renderpipeline\\builtin-depth-of-field.mtl", "default_renderpipeline\\builtin-dof-pass.ts", "default_renderpipeline\\builtin-forward.rpp", "default_renderpipeline\\builtin-fsr.mtl", "default_renderpipeline\\builtin-fxaa.mtl", "default_renderpipeline\\builtin-kawase-bloom.mtl", "default_renderpipeline\\builtin-mipmap-bloom.mtl", "default_renderpipeline\\builtin-pipeline-pass.ts", "default_renderpipeline\\builtin-pipeline-settings.ts", "default_renderpipeline\\builtin-pipeline-types.ts", "default_renderpipeline\\builtin-pipeline.ts", "default_renderpipeline\\builtin-tone-mapping.mtl", "default_renderpipeline\\deferred-lighting.mtl", "default_renderpipeline\\post-process.mtl", "default_renderpipeline\\tonemap.mtl", "default_skybox\\default_skybox.hdr", "default_skybox\\default_skybox.png", "default_ui\\atom.plist", "default_ui\\atom.png", "default_ui\\atom_new.plist", "default_ui\\default_btn_disabled.png", "default_ui\\default_btn_normal.png", "default_ui\\default_btn_pressed.png", "default_ui\\default_editbox_bg.png", "default_ui\\default_panel.png", "default_ui\\default_progressbar.png", "default_ui\\default_progressbar_bg.png", "default_ui\\default_radio_button_off.png", "default_ui\\default_radio_button_on.png", "default_ui\\default_scrollbar.png", "default_ui\\default_scrollbar_bg.png", "default_ui\\default_scrollbar_vertical.png", "default_ui\\default_scrollbar_vertical_bg.png", "default_ui\\default_sprite.png", "default_ui\\default_sprite_splash.png", "default_ui\\default_toggle_checkmark.png", "default_ui\\default_toggle_disabled.png", "default_ui\\default_toggle_normal.png", "default_ui\\default_toggle_pressed.png", "gizmo\\camera.png", "gizmo\\directional-light.png", "gizmo\\light-probe.png", "gizmo\\particle-system.png", "gizmo\\reflection-probe.png", "gizmo\\scene-gizmo.mtl", "gizmo\\sphere-light.png", "gizmo\\spot-light.png", "gizmo\\webview.png", "gizmo\\x.png", "gizmo\\y.png", "gizmo\\z.png", "physics\\default-physics-material.pmtl", "tools\\debug-view-runtime-control.prefab", "tools\\debug-view-runtime-control.ts", "tools\\parsed-effect-info.json", "default_file_content\\animation-clip\\default.anim", "default_file_content\\animation-graph\\default.animgraph", "default_file_content\\animation-graph-variant\\default.animgraphvari", "default_file_content\\animation-mask\\default.animask", "default_file_content\\auto-atlas\\default.pac", "default_file_content\\label-atlas\\default.labelatlas", "default_file_content\\material\\default.mtl", "default_file_content\\physics-material\\default.pmtl", "default_file_content\\prefab\\default.prefab", "default_file_content\\render-pipeline\\default.rpp", "default_file_content\\render-pipeline\\forward-pipeline.rpp", "default_file_content\\render-texture\\default.rt", "default_file_content\\scene\\default.scene", "default_file_content\\scene\\scene-2d.scene", "default_file_content\\scene\\scene-quality.scene", "default_file_content\\terrain\\default.terrain", "default_fonts\\builtin-bitmap\\OpenSans-Bold.fnt", "default_fonts\\builtin-bitmap\\OpenSans-BoldItalic.fnt", "default_fonts\\builtin-bitmap\\OpenSans-BoldItalic_0.png", "default_fonts\\builtin-bitmap\\OpenSans-Bold_0.png", "default_fonts\\builtin-bitmap\\OpenSans-Italic.fnt", "default_fonts\\builtin-bitmap\\OpenSans-Italic_0.png", "default_fonts\\builtin-bitmap\\OpenSans-Regular.fnt", "default_fonts\\builtin-bitmap\\OpenSans-Regular_0.png", "default_fonts\\builtin-freetype\\OpenSans-Bold.ttf", "default_fonts\\builtin-freetype\\OpenSans-BoldItalic.ttf", "default_fonts\\builtin-freetype\\OpenSans-Italic.ttf", "default_fonts\\builtin-freetype\\OpenSans-Regular.ttf", "default_prefab\\2d\\Camera.prefab", "default_prefab\\3d\\Capsule.prefab", "default_prefab\\3d\\Cone.prefab", "default_prefab\\3d\\Cube.prefab", "default_prefab\\3d\\Cylinder.prefab", "default_prefab\\3d\\Plane.prefab", "default_prefab\\3d\\Quad.prefab", "default_prefab\\3d\\Sphere.prefab", "default_prefab\\3d\\Torus.prefab", "default_prefab\\effects\\Particle System.prefab", "default_prefab\\light\\Directional Light.prefab", "default_prefab\\light\\Light Probe Group.prefab", "default_prefab\\light\\Point Light.prefab", "default_prefab\\light\\Ranged Directional Light.prefab", "default_prefab\\light\\Reflection Probe.prefab", "default_prefab\\light\\Sphere Light.prefab", "default_prefab\\light\\Spot Light.prefab", "default_prefab\\ui\\Button.prefab", "default_prefab\\ui\\Canvas.prefab", "default_prefab\\ui\\EditBox.prefab", "default_prefab\\ui\\Graphics.prefab", "default_prefab\\ui\\Label.prefab", "default_prefab\\ui\\Layout.prefab", "default_prefab\\ui\\Mask.prefab", "default_prefab\\ui\\pageView.prefab", "default_prefab\\ui\\ParticleSystem2D.prefab", "default_prefab\\ui\\ProgressBar.prefab", "default_prefab\\ui\\RichText.prefab", "default_prefab\\ui\\ScrollView.prefab", "default_prefab\\ui\\Slider.prefab", "default_prefab\\ui\\Sprite.prefab", "default_prefab\\ui\\SpriteRenderer.prefab", "default_prefab\\ui\\SpriteSplash.prefab", "default_prefab\\ui\\TiledMap.prefab", "default_prefab\\ui\\Toggle.prefab", "default_prefab\\ui\\ToggleContainer.prefab", "default_prefab\\ui\\VideoPlayer.prefab", "default_prefab\\ui\\WebView.prefab", "default_prefab\\ui\\Widget.prefab", "dependencies\\textures\\preintegrated-skin.png", "default_prefab\\2d\\ui\\Canvas.prefab", "dependencies\\textures\\lut\\original-color-grading.png"]}}