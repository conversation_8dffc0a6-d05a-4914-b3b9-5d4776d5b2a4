{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/common/constant/Constant.ts"], "names": ["CLICK_SPACE", "FSP_PLAY_MUL", "val", "text", "MOBILE_TOUCH_HEIGHT", "PC_TOUCH_HEIGHT"], "mappings": ";;;;;;;;;;;;;;AACA;6BACMA,W,GAAc,E,GAEpB;;;8BACMC,Y,GAAe,CACjB;AAAEC,QAAAA,GAAG,EAAE,CAAP;AAAUC,QAAAA,IAAI,EAAE;AAAhB,OADiB,EAEjB;AAAED,QAAAA,GAAG,EAAE,CAAP;AAAUC,QAAAA,IAAI,EAAE;AAAhB,OAFiB,EAGjB;AAAED,QAAAA,GAAG,EAAE,CAAP;AAAUC,QAAAA,IAAI,EAAE;AAAhB,OAHiB,EAIjB;AAAED,QAAAA,GAAG,EAAE,GAAP;AAAYC,QAAAA,IAAI,EAAE;AAAlB,OAJiB,EAKjB;AAAED,QAAAA,GAAG,EAAE,IAAP;AAAaC,QAAAA,IAAI,EAAE;AAAnB,OALiB,C,GAQrB;;;qCACMC,mB,GAAsB,E,GAC5B;;;iCACMC,e,GAAkB,C", "sourcesContent": ["\r\n// 点击间隔\r\nconst CLICK_SPACE = 10\r\n\r\n// 战斗播放倍数\r\nconst FSP_PLAY_MUL = [\r\n    { val: 4, text: '0.25x' },\r\n    { val: 2, text: '0.5x' },\r\n    { val: 1, text: '1x' },\r\n    { val: 0.5, text: '2x' },\r\n    { val: 0.25, text: '4x' },\r\n]\r\n\r\n// 移动端点击士兵之后抬起的高度\r\nconst MOBILE_TOUCH_HEIGHT = 40\r\n// PC端点击士兵之后抬起的高度\r\nconst PC_TOUCH_HEIGHT = 4\r\n\r\nexport {\r\n    CLICK_SPACE,\r\n    FSP_PLAY_MUL,\r\n    MOBILE_TOUCH_HEIGHT,\r\n    PC_TOUCH_HEIGHT,\r\n}"]}