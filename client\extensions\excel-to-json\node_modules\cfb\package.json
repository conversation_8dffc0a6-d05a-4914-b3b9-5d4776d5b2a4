{"name": "cfb", "version": "1.0.8", "author": "sheetjs", "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "^2.14.1", "printj": "~1.1.2"}, "devDependencies": {"crc-32": "~1.2.0", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "@types/commander": "^2.9.0", "dtslint": "~0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}}