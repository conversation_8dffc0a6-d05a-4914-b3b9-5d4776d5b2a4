{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/proto/msg.mjs?cjs=&original=.js"], "names": ["req", "__cjsMetaURL", "loader", "d", "default", "throwInvalidWrapper", "url", "require"], "mappings": ";;;;;;;;;;;;;;;;;AACyBA,MAAAA,G,iBAAhBC,Y;;AACFC,MAAAA,M;;;;;;;;;;AAMaC,MAAAA,C,iBAAXC,O;;;mBART;;AAGA,UAAI;AAAA;AAAA,qBAAJ,EAAU;AACNF,QAAAA,MAAM,CAACG,mBAAP,CAA2B,UAA3B,EAAuC,cAAYC,GAAnD;AACH;;AACDJ,MAAAA,MAAM,CAACK,OAAP;AAAA;AAAA;;yBAGSJ,C", "sourcesContent": ["// I am the facade module who provides access to the CommonJS module './msg.js'~\nimport { __cjsMetaURL as req } from './msg.js';\nimport loader from 'cce:/internal/ml/cjs-loader.mjs';\nif (!req) {\n    loader.throwInvalidWrapper('./msg.js', import.meta.url);\n}\nloader.require(req);\nexport * from './msg.js';\nimport { default as d } from './msg.js'\nexport { d as default };"]}