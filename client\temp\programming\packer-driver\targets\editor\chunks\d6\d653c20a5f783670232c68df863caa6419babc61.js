System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, v2, CLICK_SPACE, gHelper, DragTouchType, _class, _crd, ccclass, property, DragTouchCmpt;

  function _reportPossibleCrUseOfCLICK_SPACE(extras) {
    _reporterNs.report("CLICK_SPACE", "../../common/constant/Constant", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIDragTarget(extras) {
    _reporterNs.report("IDragTarget", "../../common/constant/interface", _context.meta, extras);
  }

  function _reportPossibleCrUseOfgHelper(extras) {
    _reporterNs.report("gHelper", "../../common/helper/GameHelper", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDragTouchType(extras) {
    _reporterNs.report("DragTouchType", "../../common/constant/Enums", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      CLICK_SPACE = _unresolved_2.CLICK_SPACE;
    }, function (_unresolved_3) {
      gHelper = _unresolved_3.gHelper;
    }, function (_unresolved_4) {
      DragTouchType = _unresolved_4.DragTouchType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d977eliIm1Iwpi7gWgNBqSH", "DragTouchCmpt", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'Node', 'v2', 'Vec2']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 用于拖动的组建
       */

      _export("default", DragTouchCmpt = ccclass(_class = class DragTouchCmpt extends Component {
        constructor(...args) {
          super(...args);
          this.LONG_PRESS_INTERVAL = 0.5;
          this.interactable = true;
          this.target = null;
          this.isDownClick = false;
          //是否按下点击
          this.isFirstDrag = false;
          //是否首次拖动
          this.isCanDrag = false;
          //是否可以拖动
          this.touchEvent = null;
          this.clickTime = 0;
          this._temp_vec2_1 = v2();
          this._temp_vec2_2 = v2();
        }

        init(target) {
          this.target = target;
          this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
          this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
          this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
          this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
          this.node.SetSwallowTouches(false); //默认开启穿透

          return this;
        }

        clean() {
          this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
          this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
          this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
          this.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
          this.target = null;
        } // 设置是否可以拖动


        setCanDrag(val) {
          this.isCanDrag = val;
          return this;
        } // 触摸开始


        onTouchStart(event) {
          if (!this.interactable || (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
            error: Error()
          }), gHelper) : gHelper).clickTouchId !== -1) {
            return;
          }

          (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
            error: Error()
          }), gHelper) : gHelper).clickTouchId = event.getID();
          this.isDownClick = true;
          this.isFirstDrag = false;

          if (this.isCanDrag) {
            this.touchEvent = event;
            this.clickTime = this.LONG_PRESS_INTERVAL;
          } else {
            this.touchEvent = null;
            this.clickTime = 0;
          }
        }

        onTouchMove(event) {
          if (!this.interactable || (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
            error: Error()
          }), gHelper) : gHelper).clickTouchId !== event.getID()) {
            return;
          } else if (!this.isCanDrag) {
            return;
          }

          const startLocation = event.getStartLocation(this._temp_vec2_1);
          const location = event.getLocation(this._temp_vec2_2);
          const mag = startLocation.subtract(location).length();

          if (mag > 4) {
            if (!this.isFirstDrag) {
              this.onDragBegin(event);
            }

            this.target.onTouchEvent((_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
              error: Error()
            }), DragTouchType) : DragTouchType).DRAG_MOVE, event);
          }
        }

        onTouchEnd(event) {
          if ((_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
            error: Error()
          }), gHelper) : gHelper).clickTouchId !== event.getID() || !this.interactable) {
            return;
          }

          (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
            error: Error()
          }), gHelper) : gHelper).clickTouchId = -1;
          this.touchEvent = null;
          this.clickTime = 0;

          if (this.isCanDrag && this.isFirstDrag) {
            this.target.onTouchEvent((_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
              error: Error()
            }), DragTouchType) : DragTouchType).DRAG_END, event);
          } else if (this.isDownClick) {
            const startLocation = event.getStartLocation(this._temp_vec2_1);
            const location = event.getLocation(this._temp_vec2_2);
            const mag = startLocation.subtract(location).length();

            if (mag <= (_crd && CLICK_SPACE === void 0 ? (_reportPossibleCrUseOfCLICK_SPACE({
              error: Error()
            }), CLICK_SPACE) : CLICK_SPACE)) {
              return this.target.onTouchEvent((_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
                error: Error()
              }), DragTouchType) : DragTouchType).CLICK, event);
            }
          } else {
            console.log('onTouchEnd none!');
          }
        } // 拖动开始


        onDragBegin(event) {
          this.touchEvent = null;
          this.clickTime = 0;
          this.isFirstDrag = true;
          this.isDownClick = false;
          this.target.onTouchEvent((_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType).DRAG_BEGIN, event);
        }

        update(dt) {
          if (!this.isCanDrag || !this.touchEvent || this.isFirstDrag || this.clickTime <= 0) {
            return;
          }

          this.clickTime -= dt;

          if (this.clickTime <= 0) {
            this.onDragBegin(this.touchEvent);
          }
        }

      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d653c20a5f783670232c68df863caa6419babc61.js.map