{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/HeroCmpt.ts"], "names": ["_decorator", "Component", "Label", "v2", "v3", "FrameAnimationCmpt", "getHeroFrameAnimConf", "GameModel", "EventType", "DragTouchType", "HeroState", "DragTouchCmpt", "PC_TOUCH_HEIGHT", "ccclass", "property", "HeroCmpt", "key", "data", "body", "animNode", "animCmpt", "touchCmpt", "currAnimName", "attrBar", "preStateUid", "tempPosition", "originalPosition", "dragBeginLocation", "_temp_vec2_1", "init", "pos", "set", "node", "setPosition", "<PERSON><PERSON><PERSON><PERSON>", "getComponent", "getViewId", "<PERSON><PERSON><PERSON><PERSON>", "getAnimConfPositionOffset", "setUpdateModel", "ins", "addComponent", "setCanDrag", "isCanDrag", "Child", "string", "lv", "playAnimation", "loadAttrBar", "resync", "zIndex", "active", "clean", "release", "destroy", "assetsMgr", "releaseTempRes", "getPrefabUrl", "uid", "areaType", "getBody", "getTempPosition", "getPosition", "onTouchEvent", "type", "event", "DRAG_BEGIN", "y", "getUILocation", "eventCenter", "emit", "DRAG_HERO_BEGIN", "DRAG_MOVE", "location", "diff", "subtract", "add", "toVec2", "x", "DRAG_HERO_MOVE", "DRAG_END", "DRAG_HERO_END", "CLICK", "console", "log", "restorePosition", "update", "dt", "updateState", "name", "cb", "startTime", "play", "state", "STAND", "doStand", "ATTACK", "doAttack", "HIT", "doHit", "anim<PERSON><PERSON>", "playAnimName", "reset", "currAttackTime", "suffix", "instabilityAttackIndex", "damage", "isDie", "sound", "REMOVE_HERO", "doDie"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,E,OAAAA,E;;AACtDC,MAAAA,kB;;AACEC,MAAAA,oB,iBAAAA,oB;;AACFC,MAAAA,S;;AACAC,MAAAA,S;;AAGYC,MAAAA,a,iBAAAA,a;AAAeC,MAAAA,S,iBAAAA,S;;AAC3BC,MAAAA,a;;AACEC,MAAAA,e,iBAAAA,e;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U,GAE9B;;yBAEqBe,Q,GADpBF,O,UAAD,MACqBE,QADrB,SACsCd,SADtC,CACgD;AAAA;AAAA;AAAA,eAEpCe,GAFoC,GAEtB,EAFsB;AAAA,eAIrCC,IAJqC,GAIrB,IAJqB;AAAA,eAKpCC,IALoC,GAKvB,IALuB;AAAA,eAMpCC,QANoC,GAMnB,IANmB;AAAA,eAOpCC,QAPoC,GAOL,IAPK;AAAA,eAQpCC,SARoC,GAQT,IARS;AAAA,eASpCC,YAToC,GASb,EATa;AAAA,eAUpCC,OAVoC,GAUb,IAVa;AAAA,eAYpCC,WAZoC,GAYd,EAZc;AAAA,eAapCC,YAboC,GAafrB,EAAE,EAba;AAAA,eAcpCsB,gBAdoC,GAcXtB,EAAE,EAdS;AAAA,eAepCuB,iBAfoC,GAeVxB,EAAE,EAfQ;AAeL;AAfK,eAiBpCyB,YAjBoC,GAiBfzB,EAAE,EAjBa;AAAA;;AAmB/B0B,QAAAA,IAAI,CAACZ,IAAD,EAAgBa,GAAhB,EAA2Bd,GAA3B,EAAwC;AAAA;;AAAA;AACrD,YAAA,KAAI,CAACC,IAAL,GAAYA,IAAZ;AACA,YAAA,KAAI,CAACD,GAAL,GAAWA,GAAX;;AACA,YAAA,KAAI,CAACU,gBAAL,CAAsBK,GAAtB,CAA0BD,GAA1B;;AACA,YAAA,KAAI,CAACE,IAAL,CAAUC,WAAV,CAAsBH,GAAtB;;AACA,YAAA,KAAI,CAACZ,IAAL,GAAY,KAAI,CAACgB,SAAL,CAAe,MAAf,CAAZ;AACA,YAAA,KAAI,CAACf,QAAL,GAAgB,KAAI,CAACe,SAAL,CAAe,WAAf,CAAhB;AACA,YAAA,KAAI,CAACd,QAAL,GAAgB,KAAI,CAACD,QAAL,CAAcgB,YAAd;AAAA;AAAA,yDAAhB;AACA,kBAAM,KAAI,CAACf,QAAL,CAAcS,IAAd,CAAmB;AAAA;AAAA,8DAAqBZ,IAAI,CAACmB,SAAL,EAArB,CAAnB,EAA2DpB,GAA3D,CAAN;;AACA,gBAAI,KAAI,CAACqB,OAAT,EAAkB;AACd,cAAA,KAAI,CAAClB,QAAL,CAAcc,WAAd,CAA0B,KAAI,CAACb,QAAL,CAAckB,yBAAd,EAA1B,EADc,CACuD;;;AACrE,cAAA,KAAI,CAAClB,QAAL,CAAcmB,cAAd,CAA6B;AAAA;AAAA,0CAAUC,GAAV,EAA7B;;AACA,cAAA,KAAI,CAACnB,SAAL,GAAiB,KAAI,CAACa,SAAL,CAAe,OAAf,EAAwBO,YAAxB;AAAA;AAAA,kDAAoDZ,IAApD,CAAyD,KAAzD,EAA+Da,UAA/D,CAA0EzB,IAAI,CAAC0B,SAAL,EAA1E,CAAjB;AACA,cAAA,KAAI,CAACC,KAAL,CAAW,IAAX,EAAiB1C,KAAjB,EAAwB2C,MAAxB,GAAiC5B,IAAI,CAAC6B,EAAL,GAAU,EAA3C;;AACA,cAAA,KAAI,CAACC,aAAL,CAAmB,MAAnB;;AACA,cAAA,KAAI,CAACC,WAAL;AACH;;AACD,mBAAO,KAAP;AAjBqD;AAkBxD;;AAEMC,QAAAA,MAAM,CAAChC,IAAD,EAAYa,GAAZ,EAAuB;AAChC,eAAKb,IAAL,CAAUY,IAAV,CAAeZ,IAAf;AACA,eAAKS,gBAAL,CAAsBK,GAAtB,CAA0BD,GAA1B;AACA,eAAKE,IAAL,CAAUC,WAAV,CAAsBH,GAAtB;AACA,eAAKE,IAAL,CAAUkB,MAAV,GAAmB,CAAnB;AACA,eAAKhC,IAAL,CAAU0B,KAAV,CAAgB,QAAhB,EAA0BO,MAA1B,GAAmC,IAAnC;AACA,eAAKJ,aAAL,CAAmB,MAAnB;AACA,eAAK1B,SAAL,CAAeqB,UAAf,CAA0B,KAAKzB,IAAL,CAAU0B,SAAV,EAA1B;AACA,eAAKK,WAAL;AACA,iBAAO,IAAP;AACH;;AAEMI,QAAAA,KAAK,CAACC,OAAD,EAAoB;AAAA;;AAC5B,eAAKjC,QAAL,CAAcgC,KAAd;AACA,eAAKpB,IAAL,CAAUsB,OAAV;AACAD,UAAAA,OAAO,IAAIE,SAAS,CAACC,cAAV,eAAyB,KAAKvC,IAA9B,qBAAyB,WAAWwC,YAAX,EAAzB,EAAoD,KAAKzC,GAAzD,CAAX;AACA,eAAKC,IAAL,GAAY,IAAZ;AACH;;AAEa+B,QAAAA,WAAW,GAAG,CACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AARwB;AAS3B;;AAEa,YAAHU,GAAG,GAAW;AAAA;;AAAE,iBAAO,qBAAKzC,IAAL,iCAAWyC,GAAX,KAAkB,EAAzB;AAA6B;;AACrC,YAARC,QAAQ,GAAa;AAAA;;AAAE,gCAAO,KAAK1C,IAAZ,qBAAO,YAAW0C,QAAlB;AAA4B;;AAEvDC,QAAAA,OAAO,GAAG;AACb,iBAAO,KAAK1C,IAAZ;AACH;;AAEM2C,QAAAA,eAAe,GAAG;AACrB,iBAAO,KAAK7B,IAAL,CAAU8B,WAAV,CAAsB,KAAKrC,YAA3B,CAAP;AACH,SA9E2C,CAgF5C;;;AACOsC,QAAAA,YAAY,CAACC,IAAD,EAAsBC,KAAtB,EAAyC;AACxD;AACA,cAAID,IAAI,KAAK;AAAA;AAAA,8CAAcE,UAA3B,EAAuC;AACnC,iBAAKlC,IAAL,CAAUmC,CAAV,GAAc,KAAKzC,gBAAL,CAAsByC,CAAtB;AAAA;AAAA,mDAAd;AACAF,YAAAA,KAAK,CAACG,aAAN,CAAoB,KAAKzC,iBAAzB;AACA,iBAAKK,IAAL,CAAUkB,MAAV,GAAmB,EAAnB;AACA,iBAAKhC,IAAL,CAAU0B,KAAV,CAAgB,QAAhB,EAA0BO,MAA1B,GAAmC,KAAnC;AACA,iBAAKJ,aAAL,CAAmB,QAAnB;AACAsB,YAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,wCAAUC,eAA3B,EAA4C,IAA5C;AACH,WAPD,MAOO,IAAIP,IAAI,KAAK;AAAA;AAAA,8CAAcQ,SAA3B,EAAsC;AACzC,gBAAMC,QAAQ,GAAGR,KAAK,CAACG,aAAN,CAAoB,KAAKxC,YAAzB,CAAjB;AACA,gBAAM8C,IAAI,GAAGD,QAAQ,CAACE,QAAT,CAAkB,KAAKhD,iBAAvB,EAA0CiD,GAA1C,CAA8C,KAAKlD,gBAAL,CAAsBmD,MAAtB,EAA9C,CAAb;AACA,iBAAK7C,IAAL,CAAUC,WAAV,CAAsByC,IAAI,CAACI,CAA3B,EAA8BJ,IAAI,CAACP,CAAL;AAAA;AAAA,mDAA9B;AACAE,YAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,wCAAUS,cAA3B,EAA2C,IAA3C;AACH,WALM,MAKA,IAAIf,IAAI,KAAK;AAAA;AAAA,8CAAcgB,QAA3B,EAAqC;AACxCX,YAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,wCAAUW,aAA3B,EAA0C,IAA1C;AACH,WAFM,MAEA,IAAIjB,IAAI,KAAK;AAAA;AAAA,8CAAckB,KAA3B,EAAkC;AACrCC,YAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EAAsB,KAAKnE,IAA3B;AACH;AACJ,SApG2C,CAsG5C;;;AACOoE,QAAAA,eAAe,GAAG;AACrB,cAAI,CAAC,KAAKhD,OAAV,EAAmB;AACf;AACH;;AACD,eAAKL,IAAL,CAAUkB,MAAV,GAAmB,CAAnB;AACA,eAAKlB,IAAL,CAAUC,WAAV,CAAsB,KAAKP,gBAA3B;AACA,eAAKR,IAAL,CAAU0B,KAAV,CAAgB,QAAhB,EAA0BO,MAA1B,GAAmC,IAAnC;AACA,eAAKJ,aAAL,CAAmB,MAAnB;AACH;;AAEDuC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,cAAI,CAAC,KAAKtE,IAAV,EAAgB;AACZ;AACH;;AACD,eAAKuE,WAAL;AACH,SAtH2C,CAwH5C;;;AACOzC,QAAAA,aAAa,CAAC0C,IAAD,EAAeC,EAAf,EAA8BC,SAA9B,EAAkD;AAAA;;AAClE,eAAKrE,YAAL,GAAoBmE,IAApB;AACA,iCAAKrE,QAAL,4BAAewE,IAAf,CAAoBH,IAApB,EAA0BC,EAA1B,EAA8BC,SAA9B;AACH,SA5H2C,CA8H5C;;;AACQH,QAAAA,WAAW,GAAG;AAAA;;AAClB,cAAI,iBAAC,KAAKvE,IAAN,aAAC,YAAW4E,KAAZ,KAAqB,KAAKrE,WAAL,KAAqB,KAAKP,IAAL,CAAU4E,KAAV,CAAgBnC,GAA9D,EAAmE;AAC/D;AACH;;AACD,eAAKlC,WAAL,GAAmB,KAAKP,IAAL,CAAU4E,KAAV,CAAgBnC,GAAnC;AACA,cAAMmC,KAAK,GAAG,KAAK5E,IAAL,CAAU4E,KAAV,CAAgB7B,IAA9B;AAAA,cAAoC/C,IAAI,GAAG,KAAKA,IAAL,CAAU4E,KAAV,CAAgB5E,IAA3D,CALkB,CAMlB;AACA;;AACA,cAAI4E,KAAK,KAAK;AAAA;AAAA,sCAAUC,KAAxB,EAA+B;AAAE;AAC7B,iBAAKC,OAAL;AACH,WAFD,MAEO,IAAIF,KAAK,KAAK;AAAA;AAAA,sCAAUG,MAAxB,EAAgC;AAAE;AACrC,iBAAKC,QAAL,CAAchF,IAAd;AACH,WAFM,MAEA,IAAI4E,KAAK,KAAK;AAAA;AAAA,sCAAUK,GAAxB,EAA6B;AAAE;AAClC,iBAAKC,KAAL,CAAWlF,IAAX;AACH,WAFM,MAEA;AACH,iBAAK8B,aAAL,CAAmB,MAAnB;AACH;AACJ,SAhJ2C,CAkJ5C;;;AACQgD,QAAAA,OAAO,GAAG;AAAA;;AACd,cAAMK,QAAQ,sBAAG,KAAKhF,QAAR,qBAAG,gBAAeiF,YAAhC;;AACA,cAAID,QAAQ,KAAK,MAAb,IAAuBA,QAAQ,KAAK,WAAxC,EAAqD;AAAE;AACnD,iBAAKrD,aAAL,CAAmB,MAAnB;AACH;;AACD,gCAAKxB,OAAL,2BAAc+E,KAAd;AACH,SAzJ2C,CA2J5C;;;AACQL,QAAAA,QAAQ,CAAChF,IAAD,EAAY;AAAA;;AACxB,cAAMsF,cAAc,2BAAGtF,IAAI,CAACsF,cAAR,mCAA0B,CAA9C;AACA,cAAMC,MAAM,GAAGvF,IAAI,CAACwF,sBAAL,IAA+B,EAA9C;AACA,eAAK1D,aAAL,CAAmB,WAAWyD,MAA9B,EAAsC,MAAM,KAAKnE,OAAL,IAAgB,KAAKU,aAAL,CAAmB,MAAnB,CAA5D,EAAwFwD,cAAxF;AACH,SAhK2C,CAkK5C;;;AACQJ,QAAAA,KAAK,CAAClF,IAAD,EAAY;AAAA;;AACrB,cAAIyF,MAAM,mBAAGzF,IAAI,CAACyF,MAAR,2BAAkB,CAA5B;AACA,cAAMC,KAAK,GAAG,KAAK1F,IAAL,CAAU0F,KAAV,EAAd;AACA,cAAMC,KAAK,GAAG3F,IAAI,CAAC2F,KAAnB,CAHqB,CAGI;;AACzB,cAAMlD,GAAG,GAAG,KAAKA,GAAjB;AACA,iCAAKnC,OAAL,4BAAcqE,IAAd;;AACA,cAAIc,MAAM,KAAK,CAAf,EAAkB;AACd,mBAAO,KAAK3D,aAAL,CAAmB,MAAnB,CAAP;AACH;;AACD,cAAIqD,QAAQ,GAAG,KAAf;;AACA,cAAIO,KAAJ,EAAW;AACPP,YAAAA,QAAQ,GAAG,KAAX,CADO,CAEP;AACH,WAHD,MAGO,IAAIQ,KAAJ,EAAW,CACd;AACH;;AACD,eAAK7D,aAAL,CAAmBqD,QAAnB,EAA6B,MAAM;AAC/B,gBAAIO,KAAJ,EAAW;AACPtC,cAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,0CAAUuC,WAA3B,EAAwCnD,GAAxC,EAA6C,KAA7C;AACH,aAFD,MAEO,IAAI,KAAKrB,OAAT,EAAkB;AACrB,mBAAKU,aAAL,CAAmB,MAAnB;AACH;AACJ,WAND;AAOH,SA1L2C,CA4L5C;;;AACQ+D,QAAAA,KAAK,CAAC7F,IAAD,EAAY,CAExB;;AA/L2C,O", "sourcesContent": ["import { _decorator, Component, EventTouch, Label, Node, v2, v3, Vec2, Vec3 } from \"cc\";\r\nimport FrameAnimationCmpt from \"../cmpt/FrameAnimationCmpt\";\r\nimport { getHeroFrameAnimConf } from \"../../common/config/HeroFrameAnimConf\";\r\nimport GameModel from \"../../model/game/GameModel\";\r\nimport EventType from \"../../common/event/EventType\";\r\nimport AttrBarCmpt from \"./AttrBarCmpt\";\r\nimport HeroObj from \"../../model/game/HeroObj\";\r\nimport { AreaType, DragTouchType, HeroState } from \"../../common/constant/Enums\";\r\nimport DragTouchCmpt from \"./DragTouchCmpt\";\r\nimport { PC_TOUCH_HEIGHT } from \"../../common/constant/Constant\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n// 一个英雄\r\n@ccclass\r\nexport default class HeroCmpt extends Component {\r\n\r\n    private key: string = ''\r\n\r\n    public data: HeroObj = null\r\n    private body: Node = null\r\n    private animNode: Node = null\r\n    private animCmpt: FrameAnimationCmpt = null\r\n    private touchCmpt: DragTouchCmpt = null\r\n    private currAnimName: string = ''\r\n    private attrBar: AttrBarCmpt = null\r\n\r\n    private preStateUid: string = ''\r\n    private tempPosition: Vec3 = v3()\r\n    private originalPosition: Vec3 = v3()\r\n    private dragBeginLocation: Vec2 = v2() //拖动开始的坐标\r\n\r\n    private _temp_vec2_1: Vec2 = v2()\r\n\r\n    public async init(data: HeroObj, pos: Vec3, key: string) {\r\n        this.data = data\r\n        this.key = key\r\n        this.originalPosition.set(pos)\r\n        this.node.setPosition(pos)\r\n        this.body = this.FindChild('body')\r\n        this.animNode = this.FindChild('body/anim')\r\n        this.animCmpt = this.animNode.getComponent(FrameAnimationCmpt)\r\n        await this.animCmpt.init(getHeroFrameAnimConf(data.getViewId()), key)\r\n        if (this.isValid) {\r\n            this.animNode.setPosition(this.animCmpt.getAnimConfPositionOffset()) //设置偏移\r\n            this.animCmpt.setUpdateModel(GameModel.ins())\r\n            this.touchCmpt = this.FindChild('touch').addComponent(DragTouchCmpt).init(this).setCanDrag(data.isCanDrag())\r\n            this.Child('lv', Label).string = data.lv + ''\r\n            this.playAnimation('idle')\r\n            this.loadAttrBar()\r\n        }\r\n        return this\r\n    }\r\n\r\n    public resync(data: any, pos: Vec3) {\r\n        this.data.init(data)\r\n        this.originalPosition.set(pos)\r\n        this.node.setPosition(pos)\r\n        this.node.zIndex = 0\r\n        this.body.Child('shadow').active = true\r\n        this.playAnimation('idle')\r\n        this.touchCmpt.setCanDrag(this.data.isCanDrag())\r\n        this.loadAttrBar()\r\n        return this\r\n    }\r\n\r\n    public clean(release?: boolean) {\r\n        this.animCmpt.clean()\r\n        this.node.destroy()\r\n        release && assetsMgr.releaseTempRes(this.data?.getPrefabUrl(), this.key)\r\n        this.data = null\r\n    }\r\n\r\n    private async loadAttrBar() {\r\n        // const it = await nodePoolMgr.get('animal/ATTR_BAR', this.key)\r\n        // if (!this.isValid || !this.data) {\r\n        //     return nodePoolMgr.put(it)\r\n        // }\r\n        // it.parent = this.node\r\n        // it.sortIndex = 10\r\n        // it.active = true\r\n        // this.attrBar = it.getComponent(AttrBarCmpt).init(this.data)\r\n    }\r\n\r\n    public get uid(): string { return this.data?.uid || '' }\r\n    public get areaType(): AreaType { return this.data?.areaType }\r\n\r\n    public getBody() {\r\n        return this.body\r\n    }\r\n\r\n    public getTempPosition() {\r\n        return this.node.getPosition(this.tempPosition)\r\n    }\r\n\r\n    // 触摸事件\r\n    public onTouchEvent(type: DragTouchType, event: EventTouch) {\r\n        // console.log('onTouchEvent', DragTouchType[type], event)\r\n        if (type === DragTouchType.DRAG_BEGIN) {\r\n            this.node.y = this.originalPosition.y + PC_TOUCH_HEIGHT\r\n            event.getUILocation(this.dragBeginLocation)\r\n            this.node.zIndex = 10\r\n            this.body.Child('shadow').active = false\r\n            this.playAnimation('caught')\r\n            eventCenter.emit(EventType.DRAG_HERO_BEGIN, this)\r\n        } else if (type === DragTouchType.DRAG_MOVE) {\r\n            const location = event.getUILocation(this._temp_vec2_1)\r\n            const diff = location.subtract(this.dragBeginLocation).add(this.originalPosition.toVec2())\r\n            this.node.setPosition(diff.x, diff.y + PC_TOUCH_HEIGHT)\r\n            eventCenter.emit(EventType.DRAG_HERO_MOVE, this)\r\n        } else if (type === DragTouchType.DRAG_END) {\r\n            eventCenter.emit(EventType.DRAG_HERO_END, this)\r\n        } else if (type === DragTouchType.CLICK) {\r\n            console.log('click!', this.data)\r\n        }\r\n    }\r\n\r\n    // 还原位置\r\n    public restorePosition() {\r\n        if (!this.isValid) {\r\n            return\r\n        }\r\n        this.node.zIndex = 0\r\n        this.node.setPosition(this.originalPosition)\r\n        this.body.Child('shadow').active = true\r\n        this.playAnimation('idle')\r\n    }\r\n\r\n    update(dt: number) {\r\n        if (!this.data) {\r\n            return\r\n        }\r\n        this.updateState()\r\n    }\r\n\r\n    // 播放动画\r\n    public playAnimation(name: string, cb?: Function, startTime?: number) {\r\n        this.currAnimName = name\r\n        this.animCmpt?.play(name, cb, startTime)\r\n    }\r\n\r\n    // 同步状态信息\r\n    private updateState() {\r\n        if (!this.data?.state || this.preStateUid === this.data.state.uid) {\r\n            return\r\n        }\r\n        this.preStateUid = this.data.state.uid\r\n        const state = this.data.state.type, data = this.data.state.data\r\n        // cc.log('updateState', this.uid, this.point.ID(), HeroState[state])\r\n        // this.data.actioning = this.data.actioning || (state !== HeroState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动\r\n        if (state === HeroState.STAND) { //待机\r\n            this.doStand()\r\n        } else if (state === HeroState.ATTACK) { //攻击\r\n            this.doAttack(data)\r\n        } else if (state === HeroState.HIT) { //受击\r\n            this.doHit(data)\r\n        } else {\r\n            this.playAnimation('idle')\r\n        }\r\n    }\r\n\r\n    // 待机\r\n    private doStand() {\r\n        const animName = this.animCmpt?.playAnimName\r\n        if (animName === 'move' || animName === 'move_pull') { //只有移动的时候才强行切换成idle\r\n            this.playAnimation('idle')\r\n        }\r\n        this.attrBar?.reset()\r\n    }\r\n\r\n    // 攻击\r\n    private doAttack(data: any) {\r\n        const currAttackTime = data.currAttackTime ?? 0\r\n        const suffix = data.instabilityAttackIndex || ''\r\n        this.playAnimation('attack' + suffix, () => this.isValid && this.playAnimation('idle'), currAttackTime)\r\n    }\r\n\r\n    // 受击\r\n    private doHit(data: any) {\r\n        let damage = data.damage ?? 0\r\n        const isDie = this.data.isDie()\r\n        const sound = data.sound //受击音效\r\n        const uid = this.uid\r\n        this.attrBar?.play()\r\n        if (damage === 0) {\r\n            return this.playAnimation('idle')\r\n        }\r\n        let animName = 'hit'\r\n        if (isDie) {\r\n            animName = 'die'\r\n            // this.playSFXByKey('die_sound')\r\n        } else if (sound) {\r\n            // this.playSFX(sound)\r\n        }\r\n        this.playAnimation(animName, () => {\r\n            if (isDie) {\r\n                eventCenter.emit(EventType.REMOVE_HERO, uid, false)\r\n            } else if (this.isValid) {\r\n                this.playAnimation('idle')\r\n            }\r\n        })\r\n    }\r\n\r\n    // 直接死亡\r\n    private doDie(data: any) {\r\n\r\n    }\r\n}"]}