System.register(["__unresolved_0"], function (_export, _context) {
  "use strict";

  var _cjsLoader, _cjsExports, __cjsMetaURL;

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _cjsLoader = _unresolved_.default;
    }],
    execute: function () {
      _export("__cjsMetaURL", __cjsMetaURL = _context.meta.url);

      _cjsLoader.define(__cjsMetaURL, function (exports, require, module, __filename, __dirname) {
        // #region ORIGINAL CODE

        /*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
        (window || global).proto = function ($protobuf) {
          "use strict"; // Common aliases

          var $Reader = $protobuf.Reader,
              $Writer = $protobuf.Writer,
              $util = $protobuf.util; // Exported root namespace

          var $root = $protobuf.roots["default"] || ($protobuf.roots["default"] = {});

          $root.proto = function () {
            /**
             * Namespace proto.
             * @exports proto
             * @namespace
             */
            var proto = {};

            proto.Vec2 = function () {
              /**
               * Properties of a Vec2.
               * @memberof proto
               * @interface IVec2
               * @property {number|null} [x] Vec2 x
               * @property {number|null} [y] Vec2 y
               */

              /**
               * Constructs a new Vec2.
               * @memberof proto
               * @classdesc Represents a Vec2.
               * @implements IVec2
               * @constructor
               * @param {proto.IVec2=} [properties] Properties to set
               */
              function Vec2(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * Vec2 x.
               * @member {number} x
               * @memberof proto.Vec2
               * @instance
               */


              Vec2.prototype.x = 0;
              /**
               * Vec2 y.
               * @member {number} y
               * @memberof proto.Vec2
               * @instance
               */

              Vec2.prototype.y = 0;
              /**
               * Creates a new Vec2 instance using the specified properties.
               * @function create
               * @memberof proto.Vec2
               * @static
               * @param {proto.IVec2=} [properties] Properties to set
               * @returns {proto.Vec2} Vec2 instance
               */

              Vec2.create = function create(properties) {
                return new Vec2(properties);
              };
              /**
               * Encodes the specified Vec2 message. Does not implicitly {@link proto.Vec2.verify|verify} messages.
               * @function encode
               * @memberof proto.Vec2
               * @static
               * @param {proto.IVec2} message Vec2 message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              Vec2.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.x != null && message.hasOwnProperty("x")) writer.uint32(
                /* id 1, wireType 0 =*/
                8).int32(message.x);
                if (message.y != null && message.hasOwnProperty("y")) writer.uint32(
                /* id 2, wireType 0 =*/
                16).int32(message.y);
                return writer;
              };
              /**
               * Encodes the specified Vec2 message, length delimited. Does not implicitly {@link proto.Vec2.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.Vec2
               * @static
               * @param {proto.IVec2} message Vec2 message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              Vec2.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a Vec2 message from the specified reader or buffer.
               * @function decode
               * @memberof proto.Vec2
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.Vec2} Vec2
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              Vec2.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.Vec2();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.x = reader.int32();
                      break;

                    case 2:
                      message.y = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a Vec2 message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.Vec2
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.Vec2} Vec2
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              Vec2.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a Vec2 message.
               * @function verify
               * @memberof proto.Vec2
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              Vec2.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.x != null && message.hasOwnProperty("x")) if (!$util.isInteger(message.x)) return "x: integer expected";
                if (message.y != null && message.hasOwnProperty("y")) if (!$util.isInteger(message.y)) return "y: integer expected";
                return null;
              };
              /**
               * Creates a Vec2 message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.Vec2
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.Vec2} Vec2
               */


              Vec2.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.Vec2) return object;
                var message = new $root.proto.Vec2();
                if (object.x != null) message.x = object.x | 0;
                if (object.y != null) message.y = object.y | 0;
                return message;
              };
              /**
               * Creates a plain object from a Vec2 message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.Vec2
               * @static
               * @param {proto.Vec2} message Vec2
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              Vec2.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.x = 0;
                  object.y = 0;
                }

                if (message.x != null && message.hasOwnProperty("x")) object.x = message.x;
                if (message.y != null && message.hasOwnProperty("y")) object.y = message.y;
                return object;
              };
              /**
               * Converts this Vec2 to JSON.
               * @function toJSON
               * @memberof proto.Vec2
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              Vec2.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return Vec2;
            }();

            proto.Int32ArrayInfo = function () {
              /**
               * Properties of an Int32ArrayInfo.
               * @memberof proto
               * @interface IInt32ArrayInfo
               * @property {Array.<number>|null} [arr] Int32ArrayInfo arr
               */

              /**
               * Constructs a new Int32ArrayInfo.
               * @memberof proto
               * @classdesc Represents an Int32ArrayInfo.
               * @implements IInt32ArrayInfo
               * @constructor
               * @param {proto.IInt32ArrayInfo=} [properties] Properties to set
               */
              function Int32ArrayInfo(properties) {
                this.arr = [];
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * Int32ArrayInfo arr.
               * @member {Array.<number>} arr
               * @memberof proto.Int32ArrayInfo
               * @instance
               */


              Int32ArrayInfo.prototype.arr = $util.emptyArray;
              /**
               * Creates a new Int32ArrayInfo instance using the specified properties.
               * @function create
               * @memberof proto.Int32ArrayInfo
               * @static
               * @param {proto.IInt32ArrayInfo=} [properties] Properties to set
               * @returns {proto.Int32ArrayInfo} Int32ArrayInfo instance
               */

              Int32ArrayInfo.create = function create(properties) {
                return new Int32ArrayInfo(properties);
              };
              /**
               * Encodes the specified Int32ArrayInfo message. Does not implicitly {@link proto.Int32ArrayInfo.verify|verify} messages.
               * @function encode
               * @memberof proto.Int32ArrayInfo
               * @static
               * @param {proto.IInt32ArrayInfo} message Int32ArrayInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              Int32ArrayInfo.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();

                if (message.arr != null && message.arr.length) {
                  writer.uint32(
                  /* id 1, wireType 2 =*/
                  10).fork();

                  for (var i = 0; i < message.arr.length; ++i) writer.int32(message.arr[i]);

                  writer.ldelim();
                }

                return writer;
              };
              /**
               * Encodes the specified Int32ArrayInfo message, length delimited. Does not implicitly {@link proto.Int32ArrayInfo.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.Int32ArrayInfo
               * @static
               * @param {proto.IInt32ArrayInfo} message Int32ArrayInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              Int32ArrayInfo.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes an Int32ArrayInfo message from the specified reader or buffer.
               * @function decode
               * @memberof proto.Int32ArrayInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.Int32ArrayInfo} Int32ArrayInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              Int32ArrayInfo.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.Int32ArrayInfo();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      if (!(message.arr && message.arr.length)) message.arr = [];

                      if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;

                        while (reader.pos < end2) message.arr.push(reader.int32());
                      } else message.arr.push(reader.int32());

                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes an Int32ArrayInfo message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.Int32ArrayInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.Int32ArrayInfo} Int32ArrayInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              Int32ArrayInfo.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies an Int32ArrayInfo message.
               * @function verify
               * @memberof proto.Int32ArrayInfo
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              Int32ArrayInfo.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.arr != null && message.hasOwnProperty("arr")) {
                  if (!Array.isArray(message.arr)) return "arr: array expected";

                  for (var i = 0; i < message.arr.length; ++i) if (!$util.isInteger(message.arr[i])) return "arr: integer[] expected";
                }

                return null;
              };
              /**
               * Creates an Int32ArrayInfo message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.Int32ArrayInfo
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.Int32ArrayInfo} Int32ArrayInfo
               */


              Int32ArrayInfo.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.Int32ArrayInfo) return object;
                var message = new $root.proto.Int32ArrayInfo();

                if (object.arr) {
                  if (!Array.isArray(object.arr)) throw TypeError(".proto.Int32ArrayInfo.arr: array expected");
                  message.arr = [];

                  for (var i = 0; i < object.arr.length; ++i) message.arr[i] = object.arr[i] | 0;
                }

                return message;
              };
              /**
               * Creates a plain object from an Int32ArrayInfo message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.Int32ArrayInfo
               * @static
               * @param {proto.Int32ArrayInfo} message Int32ArrayInfo
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              Int32ArrayInfo.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.arrays || options.defaults) object.arr = [];

                if (message.arr && message.arr.length) {
                  object.arr = [];

                  for (var j = 0; j < message.arr.length; ++j) object.arr[j] = message.arr[j];
                }

                return object;
              };
              /**
               * Converts this Int32ArrayInfo to JSON.
               * @function toJSON
               * @memberof proto.Int32ArrayInfo
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              Int32ArrayInfo.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return Int32ArrayInfo;
            }();

            proto.UserInfo = function () {
              /**
               * Properties of a UserInfo.
               * @memberof proto
               * @interface IUserInfo
               * @property {string|null} [uid] UserInfo uid
               * @property {string|null} [loginType] UserInfo loginType
               * @property {Array.<number>|null} [totalGameCount] UserInfo totalGameCount
               * @property {number|null} [loginDayCount] UserInfo loginDayCount
               * @property {string|null} [nickname] UserInfo nickname
               * @property {number|Long|null} [createTime] UserInfo createTime
               * @property {number|Long|null} [sumOnlineTime] UserInfo sumOnlineTime
               * @property {string|null} [sessionId] UserInfo sessionId
               * @property {number|null} [roleId] UserInfo roleId
               */

              /**
               * Constructs a new UserInfo.
               * @memberof proto
               * @classdesc Represents a UserInfo.
               * @implements IUserInfo
               * @constructor
               * @param {proto.IUserInfo=} [properties] Properties to set
               */
              function UserInfo(properties) {
                this.totalGameCount = [];
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * UserInfo uid.
               * @member {string} uid
               * @memberof proto.UserInfo
               * @instance
               */


              UserInfo.prototype.uid = "";
              /**
               * UserInfo loginType.
               * @member {string} loginType
               * @memberof proto.UserInfo
               * @instance
               */

              UserInfo.prototype.loginType = "";
              /**
               * UserInfo totalGameCount.
               * @member {Array.<number>} totalGameCount
               * @memberof proto.UserInfo
               * @instance
               */

              UserInfo.prototype.totalGameCount = $util.emptyArray;
              /**
               * UserInfo loginDayCount.
               * @member {number} loginDayCount
               * @memberof proto.UserInfo
               * @instance
               */

              UserInfo.prototype.loginDayCount = 0;
              /**
               * UserInfo nickname.
               * @member {string} nickname
               * @memberof proto.UserInfo
               * @instance
               */

              UserInfo.prototype.nickname = "";
              /**
               * UserInfo createTime.
               * @member {number|Long} createTime
               * @memberof proto.UserInfo
               * @instance
               */

              UserInfo.prototype.createTime = $util.Long ? $util.Long.fromBits(0, 0, false) : 0;
              /**
               * UserInfo sumOnlineTime.
               * @member {number|Long} sumOnlineTime
               * @memberof proto.UserInfo
               * @instance
               */

              UserInfo.prototype.sumOnlineTime = $util.Long ? $util.Long.fromBits(0, 0, false) : 0;
              /**
               * UserInfo sessionId.
               * @member {string} sessionId
               * @memberof proto.UserInfo
               * @instance
               */

              UserInfo.prototype.sessionId = "";
              /**
               * UserInfo roleId.
               * @member {number} roleId
               * @memberof proto.UserInfo
               * @instance
               */

              UserInfo.prototype.roleId = 0;
              /**
               * Creates a new UserInfo instance using the specified properties.
               * @function create
               * @memberof proto.UserInfo
               * @static
               * @param {proto.IUserInfo=} [properties] Properties to set
               * @returns {proto.UserInfo} UserInfo instance
               */

              UserInfo.create = function create(properties) {
                return new UserInfo(properties);
              };
              /**
               * Encodes the specified UserInfo message. Does not implicitly {@link proto.UserInfo.verify|verify} messages.
               * @function encode
               * @memberof proto.UserInfo
               * @static
               * @param {proto.IUserInfo} message UserInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              UserInfo.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.uid != null && message.hasOwnProperty("uid")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.uid);
                if (message.loginType != null && message.hasOwnProperty("loginType")) writer.uint32(
                /* id 2, wireType 2 =*/
                18).string(message.loginType);

                if (message.totalGameCount != null && message.totalGameCount.length) {
                  writer.uint32(
                  /* id 3, wireType 2 =*/
                  26).fork();

                  for (var i = 0; i < message.totalGameCount.length; ++i) writer.int32(message.totalGameCount[i]);

                  writer.ldelim();
                }

                if (message.loginDayCount != null && message.hasOwnProperty("loginDayCount")) writer.uint32(
                /* id 4, wireType 0 =*/
                32).int32(message.loginDayCount);
                if (message.nickname != null && message.hasOwnProperty("nickname")) writer.uint32(
                /* id 5, wireType 2 =*/
                42).string(message.nickname);
                if (message.createTime != null && message.hasOwnProperty("createTime")) writer.uint32(
                /* id 6, wireType 0 =*/
                48).int64(message.createTime);
                if (message.sumOnlineTime != null && message.hasOwnProperty("sumOnlineTime")) writer.uint32(
                /* id 7, wireType 0 =*/
                56).int64(message.sumOnlineTime);
                if (message.sessionId != null && message.hasOwnProperty("sessionId")) writer.uint32(
                /* id 8, wireType 2 =*/
                66).string(message.sessionId);
                if (message.roleId != null && message.hasOwnProperty("roleId")) writer.uint32(
                /* id 9, wireType 0 =*/
                72).int32(message.roleId);
                return writer;
              };
              /**
               * Encodes the specified UserInfo message, length delimited. Does not implicitly {@link proto.UserInfo.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.UserInfo
               * @static
               * @param {proto.IUserInfo} message UserInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              UserInfo.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a UserInfo message from the specified reader or buffer.
               * @function decode
               * @memberof proto.UserInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.UserInfo} UserInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              UserInfo.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.UserInfo();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.uid = reader.string();
                      break;

                    case 2:
                      message.loginType = reader.string();
                      break;

                    case 3:
                      if (!(message.totalGameCount && message.totalGameCount.length)) message.totalGameCount = [];

                      if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;

                        while (reader.pos < end2) message.totalGameCount.push(reader.int32());
                      } else message.totalGameCount.push(reader.int32());

                      break;

                    case 4:
                      message.loginDayCount = reader.int32();
                      break;

                    case 5:
                      message.nickname = reader.string();
                      break;

                    case 6:
                      message.createTime = reader.int64();
                      break;

                    case 7:
                      message.sumOnlineTime = reader.int64();
                      break;

                    case 8:
                      message.sessionId = reader.string();
                      break;

                    case 9:
                      message.roleId = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a UserInfo message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.UserInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.UserInfo} UserInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              UserInfo.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a UserInfo message.
               * @function verify
               * @memberof proto.UserInfo
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              UserInfo.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.uid != null && message.hasOwnProperty("uid")) if (!$util.isString(message.uid)) return "uid: string expected";
                if (message.loginType != null && message.hasOwnProperty("loginType")) if (!$util.isString(message.loginType)) return "loginType: string expected";

                if (message.totalGameCount != null && message.hasOwnProperty("totalGameCount")) {
                  if (!Array.isArray(message.totalGameCount)) return "totalGameCount: array expected";

                  for (var i = 0; i < message.totalGameCount.length; ++i) if (!$util.isInteger(message.totalGameCount[i])) return "totalGameCount: integer[] expected";
                }

                if (message.loginDayCount != null && message.hasOwnProperty("loginDayCount")) if (!$util.isInteger(message.loginDayCount)) return "loginDayCount: integer expected";
                if (message.nickname != null && message.hasOwnProperty("nickname")) if (!$util.isString(message.nickname)) return "nickname: string expected";
                if (message.createTime != null && message.hasOwnProperty("createTime")) if (!$util.isInteger(message.createTime) && !(message.createTime && $util.isInteger(message.createTime.low) && $util.isInteger(message.createTime.high))) return "createTime: integer|Long expected";
                if (message.sumOnlineTime != null && message.hasOwnProperty("sumOnlineTime")) if (!$util.isInteger(message.sumOnlineTime) && !(message.sumOnlineTime && $util.isInteger(message.sumOnlineTime.low) && $util.isInteger(message.sumOnlineTime.high))) return "sumOnlineTime: integer|Long expected";
                if (message.sessionId != null && message.hasOwnProperty("sessionId")) if (!$util.isString(message.sessionId)) return "sessionId: string expected";
                if (message.roleId != null && message.hasOwnProperty("roleId")) if (!$util.isInteger(message.roleId)) return "roleId: integer expected";
                return null;
              };
              /**
               * Creates a UserInfo message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.UserInfo
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.UserInfo} UserInfo
               */


              UserInfo.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.UserInfo) return object;
                var message = new $root.proto.UserInfo();
                if (object.uid != null) message.uid = String(object.uid);
                if (object.loginType != null) message.loginType = String(object.loginType);

                if (object.totalGameCount) {
                  if (!Array.isArray(object.totalGameCount)) throw TypeError(".proto.UserInfo.totalGameCount: array expected");
                  message.totalGameCount = [];

                  for (var i = 0; i < object.totalGameCount.length; ++i) message.totalGameCount[i] = object.totalGameCount[i] | 0;
                }

                if (object.loginDayCount != null) message.loginDayCount = object.loginDayCount | 0;
                if (object.nickname != null) message.nickname = String(object.nickname);
                if (object.createTime != null) if ($util.Long) (message.createTime = $util.Long.fromValue(object.createTime)).unsigned = false;else if (typeof object.createTime === "string") message.createTime = parseInt(object.createTime, 10);else if (typeof object.createTime === "number") message.createTime = object.createTime;else if (typeof object.createTime === "object") message.createTime = new $util.LongBits(object.createTime.low >>> 0, object.createTime.high >>> 0).toNumber();
                if (object.sumOnlineTime != null) if ($util.Long) (message.sumOnlineTime = $util.Long.fromValue(object.sumOnlineTime)).unsigned = false;else if (typeof object.sumOnlineTime === "string") message.sumOnlineTime = parseInt(object.sumOnlineTime, 10);else if (typeof object.sumOnlineTime === "number") message.sumOnlineTime = object.sumOnlineTime;else if (typeof object.sumOnlineTime === "object") message.sumOnlineTime = new $util.LongBits(object.sumOnlineTime.low >>> 0, object.sumOnlineTime.high >>> 0).toNumber();
                if (object.sessionId != null) message.sessionId = String(object.sessionId);
                if (object.roleId != null) message.roleId = object.roleId | 0;
                return message;
              };
              /**
               * Creates a plain object from a UserInfo message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.UserInfo
               * @static
               * @param {proto.UserInfo} message UserInfo
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              UserInfo.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.arrays || options.defaults) object.totalGameCount = [];

                if (options.defaults) {
                  object.uid = "";
                  object.loginType = "";
                  object.loginDayCount = 0;
                  object.nickname = "";

                  if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.createTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                  } else object.createTime = options.longs === String ? "0" : 0;

                  if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.sumOnlineTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                  } else object.sumOnlineTime = options.longs === String ? "0" : 0;

                  object.sessionId = "";
                  object.roleId = 0;
                }

                if (message.uid != null && message.hasOwnProperty("uid")) object.uid = message.uid;
                if (message.loginType != null && message.hasOwnProperty("loginType")) object.loginType = message.loginType;

                if (message.totalGameCount && message.totalGameCount.length) {
                  object.totalGameCount = [];

                  for (var j = 0; j < message.totalGameCount.length; ++j) object.totalGameCount[j] = message.totalGameCount[j];
                }

                if (message.loginDayCount != null && message.hasOwnProperty("loginDayCount")) object.loginDayCount = message.loginDayCount;
                if (message.nickname != null && message.hasOwnProperty("nickname")) object.nickname = message.nickname;
                if (message.createTime != null && message.hasOwnProperty("createTime")) if (typeof message.createTime === "number") object.createTime = options.longs === String ? String(message.createTime) : message.createTime;else object.createTime = options.longs === String ? $util.Long.prototype.toString.call(message.createTime) : options.longs === Number ? new $util.LongBits(message.createTime.low >>> 0, message.createTime.high >>> 0).toNumber() : message.createTime;
                if (message.sumOnlineTime != null && message.hasOwnProperty("sumOnlineTime")) if (typeof message.sumOnlineTime === "number") object.sumOnlineTime = options.longs === String ? String(message.sumOnlineTime) : message.sumOnlineTime;else object.sumOnlineTime = options.longs === String ? $util.Long.prototype.toString.call(message.sumOnlineTime) : options.longs === Number ? new $util.LongBits(message.sumOnlineTime.low >>> 0, message.sumOnlineTime.high >>> 0).toNumber() : message.sumOnlineTime;
                if (message.sessionId != null && message.hasOwnProperty("sessionId")) object.sessionId = message.sessionId;
                if (message.roleId != null && message.hasOwnProperty("roleId")) object.roleId = message.roleId;
                return object;
              };
              /**
               * Converts this UserInfo to JSON.
               * @function toJSON
               * @memberof proto.UserInfo
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              UserInfo.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return UserInfo;
            }();

            proto.Hero = function () {
              /**
               * Properties of a Hero.
               * @memberof proto
               * @interface IHero
               * @property {string|null} [uid] Hero uid
               * @property {number|null} [id] Hero id
               * @property {number|null} [lv] Hero lv
               * @property {Array.<proto.IInt32ArrayInfo>|null} [attrs] Hero attrs
               * @property {number|null} [cost] Hero cost
               * @property {number|null} [areaType] Hero areaType
               * @property {number|null} [index] Hero index
               */

              /**
               * Constructs a new Hero.
               * @memberof proto
               * @classdesc Represents a Hero.
               * @implements IHero
               * @constructor
               * @param {proto.IHero=} [properties] Properties to set
               */
              function Hero(properties) {
                this.attrs = [];
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * Hero uid.
               * @member {string} uid
               * @memberof proto.Hero
               * @instance
               */


              Hero.prototype.uid = "";
              /**
               * Hero id.
               * @member {number} id
               * @memberof proto.Hero
               * @instance
               */

              Hero.prototype.id = 0;
              /**
               * Hero lv.
               * @member {number} lv
               * @memberof proto.Hero
               * @instance
               */

              Hero.prototype.lv = 0;
              /**
               * Hero attrs.
               * @member {Array.<proto.IInt32ArrayInfo>} attrs
               * @memberof proto.Hero
               * @instance
               */

              Hero.prototype.attrs = $util.emptyArray;
              /**
               * Hero cost.
               * @member {number} cost
               * @memberof proto.Hero
               * @instance
               */

              Hero.prototype.cost = 0;
              /**
               * Hero areaType.
               * @member {number} areaType
               * @memberof proto.Hero
               * @instance
               */

              Hero.prototype.areaType = 0;
              /**
               * Hero index.
               * @member {number} index
               * @memberof proto.Hero
               * @instance
               */

              Hero.prototype.index = 0;
              /**
               * Creates a new Hero instance using the specified properties.
               * @function create
               * @memberof proto.Hero
               * @static
               * @param {proto.IHero=} [properties] Properties to set
               * @returns {proto.Hero} Hero instance
               */

              Hero.create = function create(properties) {
                return new Hero(properties);
              };
              /**
               * Encodes the specified Hero message. Does not implicitly {@link proto.Hero.verify|verify} messages.
               * @function encode
               * @memberof proto.Hero
               * @static
               * @param {proto.IHero} message Hero message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              Hero.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.uid != null && message.hasOwnProperty("uid")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.uid);
                if (message.id != null && message.hasOwnProperty("id")) writer.uint32(
                /* id 2, wireType 0 =*/
                16).int32(message.id);
                if (message.lv != null && message.hasOwnProperty("lv")) writer.uint32(
                /* id 3, wireType 0 =*/
                24).int32(message.lv);
                if (message.attrs != null && message.attrs.length) for (var i = 0; i < message.attrs.length; ++i) $root.proto.Int32ArrayInfo.encode(message.attrs[i], writer.uint32(
                /* id 4, wireType 2 =*/
                34).fork()).ldelim();
                if (message.cost != null && message.hasOwnProperty("cost")) writer.uint32(
                /* id 5, wireType 0 =*/
                40).int32(message.cost);
                if (message.areaType != null && message.hasOwnProperty("areaType")) writer.uint32(
                /* id 6, wireType 0 =*/
                48).int32(message.areaType);
                if (message.index != null && message.hasOwnProperty("index")) writer.uint32(
                /* id 7, wireType 0 =*/
                56).int32(message.index);
                return writer;
              };
              /**
               * Encodes the specified Hero message, length delimited. Does not implicitly {@link proto.Hero.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.Hero
               * @static
               * @param {proto.IHero} message Hero message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              Hero.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a Hero message from the specified reader or buffer.
               * @function decode
               * @memberof proto.Hero
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.Hero} Hero
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              Hero.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.Hero();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.uid = reader.string();
                      break;

                    case 2:
                      message.id = reader.int32();
                      break;

                    case 3:
                      message.lv = reader.int32();
                      break;

                    case 4:
                      if (!(message.attrs && message.attrs.length)) message.attrs = [];
                      message.attrs.push($root.proto.Int32ArrayInfo.decode(reader, reader.uint32()));
                      break;

                    case 5:
                      message.cost = reader.int32();
                      break;

                    case 6:
                      message.areaType = reader.int32();
                      break;

                    case 7:
                      message.index = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a Hero message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.Hero
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.Hero} Hero
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              Hero.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a Hero message.
               * @function verify
               * @memberof proto.Hero
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              Hero.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.uid != null && message.hasOwnProperty("uid")) if (!$util.isString(message.uid)) return "uid: string expected";
                if (message.id != null && message.hasOwnProperty("id")) if (!$util.isInteger(message.id)) return "id: integer expected";
                if (message.lv != null && message.hasOwnProperty("lv")) if (!$util.isInteger(message.lv)) return "lv: integer expected";

                if (message.attrs != null && message.hasOwnProperty("attrs")) {
                  if (!Array.isArray(message.attrs)) return "attrs: array expected";

                  for (var i = 0; i < message.attrs.length; ++i) {
                    var error = $root.proto.Int32ArrayInfo.verify(message.attrs[i]);
                    if (error) return "attrs." + error;
                  }
                }

                if (message.cost != null && message.hasOwnProperty("cost")) if (!$util.isInteger(message.cost)) return "cost: integer expected";
                if (message.areaType != null && message.hasOwnProperty("areaType")) if (!$util.isInteger(message.areaType)) return "areaType: integer expected";
                if (message.index != null && message.hasOwnProperty("index")) if (!$util.isInteger(message.index)) return "index: integer expected";
                return null;
              };
              /**
               * Creates a Hero message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.Hero
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.Hero} Hero
               */


              Hero.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.Hero) return object;
                var message = new $root.proto.Hero();
                if (object.uid != null) message.uid = String(object.uid);
                if (object.id != null) message.id = object.id | 0;
                if (object.lv != null) message.lv = object.lv | 0;

                if (object.attrs) {
                  if (!Array.isArray(object.attrs)) throw TypeError(".proto.Hero.attrs: array expected");
                  message.attrs = [];

                  for (var i = 0; i < object.attrs.length; ++i) {
                    if (typeof object.attrs[i] !== "object") throw TypeError(".proto.Hero.attrs: object expected");
                    message.attrs[i] = $root.proto.Int32ArrayInfo.fromObject(object.attrs[i]);
                  }
                }

                if (object.cost != null) message.cost = object.cost | 0;
                if (object.areaType != null) message.areaType = object.areaType | 0;
                if (object.index != null) message.index = object.index | 0;
                return message;
              };
              /**
               * Creates a plain object from a Hero message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.Hero
               * @static
               * @param {proto.Hero} message Hero
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              Hero.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.arrays || options.defaults) object.attrs = [];

                if (options.defaults) {
                  object.uid = "";
                  object.id = 0;
                  object.lv = 0;
                  object.cost = 0;
                  object.areaType = 0;
                  object.index = 0;
                }

                if (message.uid != null && message.hasOwnProperty("uid")) object.uid = message.uid;
                if (message.id != null && message.hasOwnProperty("id")) object.id = message.id;
                if (message.lv != null && message.hasOwnProperty("lv")) object.lv = message.lv;

                if (message.attrs && message.attrs.length) {
                  object.attrs = [];

                  for (var j = 0; j < message.attrs.length; ++j) object.attrs[j] = $root.proto.Int32ArrayInfo.toObject(message.attrs[j], options);
                }

                if (message.cost != null && message.hasOwnProperty("cost")) object.cost = message.cost;
                if (message.areaType != null && message.hasOwnProperty("areaType")) object.areaType = message.areaType;
                if (message.index != null && message.hasOwnProperty("index")) object.index = message.index;
                return object;
              };
              /**
               * Converts this Hero to JSON.
               * @function toJSON
               * @memberof proto.Hero
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              Hero.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return Hero;
            }();

            proto.TavernInfo = function () {
              /**
               * Properties of a TavernInfo.
               * @memberof proto
               * @interface ITavernInfo
               * @property {number|null} [Id] TavernInfo Id
               * @property {Array.<proto.IHero>|null} [heros] TavernInfo heros
               */

              /**
               * Constructs a new TavernInfo.
               * @memberof proto
               * @classdesc Represents a TavernInfo.
               * @implements ITavernInfo
               * @constructor
               * @param {proto.ITavernInfo=} [properties] Properties to set
               */
              function TavernInfo(properties) {
                this.heros = [];
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * TavernInfo Id.
               * @member {number} Id
               * @memberof proto.TavernInfo
               * @instance
               */


              TavernInfo.prototype.Id = 0;
              /**
               * TavernInfo heros.
               * @member {Array.<proto.IHero>} heros
               * @memberof proto.TavernInfo
               * @instance
               */

              TavernInfo.prototype.heros = $util.emptyArray;
              /**
               * Creates a new TavernInfo instance using the specified properties.
               * @function create
               * @memberof proto.TavernInfo
               * @static
               * @param {proto.ITavernInfo=} [properties] Properties to set
               * @returns {proto.TavernInfo} TavernInfo instance
               */

              TavernInfo.create = function create(properties) {
                return new TavernInfo(properties);
              };
              /**
               * Encodes the specified TavernInfo message. Does not implicitly {@link proto.TavernInfo.verify|verify} messages.
               * @function encode
               * @memberof proto.TavernInfo
               * @static
               * @param {proto.ITavernInfo} message TavernInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              TavernInfo.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.Id != null && message.hasOwnProperty("Id")) writer.uint32(
                /* id 1, wireType 0 =*/
                8).int32(message.Id);
                if (message.heros != null && message.heros.length) for (var i = 0; i < message.heros.length; ++i) $root.proto.Hero.encode(message.heros[i], writer.uint32(
                /* id 2, wireType 2 =*/
                18).fork()).ldelim();
                return writer;
              };
              /**
               * Encodes the specified TavernInfo message, length delimited. Does not implicitly {@link proto.TavernInfo.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.TavernInfo
               * @static
               * @param {proto.ITavernInfo} message TavernInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              TavernInfo.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a TavernInfo message from the specified reader or buffer.
               * @function decode
               * @memberof proto.TavernInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.TavernInfo} TavernInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              TavernInfo.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.TavernInfo();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.Id = reader.int32();
                      break;

                    case 2:
                      if (!(message.heros && message.heros.length)) message.heros = [];
                      message.heros.push($root.proto.Hero.decode(reader, reader.uint32()));
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a TavernInfo message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.TavernInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.TavernInfo} TavernInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              TavernInfo.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a TavernInfo message.
               * @function verify
               * @memberof proto.TavernInfo
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              TavernInfo.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.Id != null && message.hasOwnProperty("Id")) if (!$util.isInteger(message.Id)) return "Id: integer expected";

                if (message.heros != null && message.hasOwnProperty("heros")) {
                  if (!Array.isArray(message.heros)) return "heros: array expected";

                  for (var i = 0; i < message.heros.length; ++i) {
                    var error = $root.proto.Hero.verify(message.heros[i]);
                    if (error) return "heros." + error;
                  }
                }

                return null;
              };
              /**
               * Creates a TavernInfo message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.TavernInfo
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.TavernInfo} TavernInfo
               */


              TavernInfo.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.TavernInfo) return object;
                var message = new $root.proto.TavernInfo();
                if (object.Id != null) message.Id = object.Id | 0;

                if (object.heros) {
                  if (!Array.isArray(object.heros)) throw TypeError(".proto.TavernInfo.heros: array expected");
                  message.heros = [];

                  for (var i = 0; i < object.heros.length; ++i) {
                    if (typeof object.heros[i] !== "object") throw TypeError(".proto.TavernInfo.heros: object expected");
                    message.heros[i] = $root.proto.Hero.fromObject(object.heros[i]);
                  }
                }

                return message;
              };
              /**
               * Creates a plain object from a TavernInfo message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.TavernInfo
               * @static
               * @param {proto.TavernInfo} message TavernInfo
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              TavernInfo.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.arrays || options.defaults) object.heros = [];
                if (options.defaults) object.Id = 0;
                if (message.Id != null && message.hasOwnProperty("Id")) object.Id = message.Id;

                if (message.heros && message.heros.length) {
                  object.heros = [];

                  for (var j = 0; j < message.heros.length; ++j) object.heros[j] = $root.proto.Hero.toObject(message.heros[j], options);
                }

                return object;
              };
              /**
               * Converts this TavernInfo to JSON.
               * @function toJSON
               * @memberof proto.TavernInfo
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              TavernInfo.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return TavernInfo;
            }();

            proto.EncounterInfo = function () {
              /**
               * Properties of an EncounterInfo.
               * @memberof proto
               * @interface IEncounterInfo
               * @property {number|null} [type] EncounterInfo type
               * @property {number|null} [updateCost] EncounterInfo updateCost
               * @property {proto.IPlayerInfo|null} [data_1] EncounterInfo data_1
               * @property {proto.ITavernInfo|null} [data_3] EncounterInfo data_3
               */

              /**
               * Constructs a new EncounterInfo.
               * @memberof proto
               * @classdesc Represents an EncounterInfo.
               * @implements IEncounterInfo
               * @constructor
               * @param {proto.IEncounterInfo=} [properties] Properties to set
               */
              function EncounterInfo(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * EncounterInfo type.
               * @member {number} type
               * @memberof proto.EncounterInfo
               * @instance
               */


              EncounterInfo.prototype.type = 0;
              /**
               * EncounterInfo updateCost.
               * @member {number} updateCost
               * @memberof proto.EncounterInfo
               * @instance
               */

              EncounterInfo.prototype.updateCost = 0;
              /**
               * EncounterInfo data_1.
               * @member {proto.IPlayerInfo|null|undefined} data_1
               * @memberof proto.EncounterInfo
               * @instance
               */

              EncounterInfo.prototype.data_1 = null;
              /**
               * EncounterInfo data_3.
               * @member {proto.ITavernInfo|null|undefined} data_3
               * @memberof proto.EncounterInfo
               * @instance
               */

              EncounterInfo.prototype.data_3 = null;
              /**
               * Creates a new EncounterInfo instance using the specified properties.
               * @function create
               * @memberof proto.EncounterInfo
               * @static
               * @param {proto.IEncounterInfo=} [properties] Properties to set
               * @returns {proto.EncounterInfo} EncounterInfo instance
               */

              EncounterInfo.create = function create(properties) {
                return new EncounterInfo(properties);
              };
              /**
               * Encodes the specified EncounterInfo message. Does not implicitly {@link proto.EncounterInfo.verify|verify} messages.
               * @function encode
               * @memberof proto.EncounterInfo
               * @static
               * @param {proto.IEncounterInfo} message EncounterInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              EncounterInfo.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.type != null && message.hasOwnProperty("type")) writer.uint32(
                /* id 1, wireType 0 =*/
                8).int32(message.type);
                if (message.updateCost != null && message.hasOwnProperty("updateCost")) writer.uint32(
                /* id 2, wireType 0 =*/
                16).int32(message.updateCost);
                if (message.data_1 != null && message.hasOwnProperty("data_1")) $root.proto.PlayerInfo.encode(message.data_1, writer.uint32(
                /* id 3, wireType 2 =*/
                26).fork()).ldelim();
                if (message.data_3 != null && message.hasOwnProperty("data_3")) $root.proto.TavernInfo.encode(message.data_3, writer.uint32(
                /* id 4, wireType 2 =*/
                34).fork()).ldelim();
                return writer;
              };
              /**
               * Encodes the specified EncounterInfo message, length delimited. Does not implicitly {@link proto.EncounterInfo.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.EncounterInfo
               * @static
               * @param {proto.IEncounterInfo} message EncounterInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              EncounterInfo.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes an EncounterInfo message from the specified reader or buffer.
               * @function decode
               * @memberof proto.EncounterInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.EncounterInfo} EncounterInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              EncounterInfo.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.EncounterInfo();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.type = reader.int32();
                      break;

                    case 2:
                      message.updateCost = reader.int32();
                      break;

                    case 3:
                      message.data_1 = $root.proto.PlayerInfo.decode(reader, reader.uint32());
                      break;

                    case 4:
                      message.data_3 = $root.proto.TavernInfo.decode(reader, reader.uint32());
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes an EncounterInfo message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.EncounterInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.EncounterInfo} EncounterInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              EncounterInfo.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies an EncounterInfo message.
               * @function verify
               * @memberof proto.EncounterInfo
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              EncounterInfo.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.type != null && message.hasOwnProperty("type")) if (!$util.isInteger(message.type)) return "type: integer expected";
                if (message.updateCost != null && message.hasOwnProperty("updateCost")) if (!$util.isInteger(message.updateCost)) return "updateCost: integer expected";

                if (message.data_1 != null && message.hasOwnProperty("data_1")) {
                  var error = $root.proto.PlayerInfo.verify(message.data_1);
                  if (error) return "data_1." + error;
                }

                if (message.data_3 != null && message.hasOwnProperty("data_3")) {
                  var error = $root.proto.TavernInfo.verify(message.data_3);
                  if (error) return "data_3." + error;
                }

                return null;
              };
              /**
               * Creates an EncounterInfo message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.EncounterInfo
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.EncounterInfo} EncounterInfo
               */


              EncounterInfo.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.EncounterInfo) return object;
                var message = new $root.proto.EncounterInfo();
                if (object.type != null) message.type = object.type | 0;
                if (object.updateCost != null) message.updateCost = object.updateCost | 0;

                if (object.data_1 != null) {
                  if (typeof object.data_1 !== "object") throw TypeError(".proto.EncounterInfo.data_1: object expected");
                  message.data_1 = $root.proto.PlayerInfo.fromObject(object.data_1);
                }

                if (object.data_3 != null) {
                  if (typeof object.data_3 !== "object") throw TypeError(".proto.EncounterInfo.data_3: object expected");
                  message.data_3 = $root.proto.TavernInfo.fromObject(object.data_3);
                }

                return message;
              };
              /**
               * Creates a plain object from an EncounterInfo message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.EncounterInfo
               * @static
               * @param {proto.EncounterInfo} message EncounterInfo
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              EncounterInfo.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.type = 0;
                  object.updateCost = 0;
                  object.data_1 = null;
                  object.data_3 = null;
                }

                if (message.type != null && message.hasOwnProperty("type")) object.type = message.type;
                if (message.updateCost != null && message.hasOwnProperty("updateCost")) object.updateCost = message.updateCost;
                if (message.data_1 != null && message.hasOwnProperty("data_1")) object.data_1 = $root.proto.PlayerInfo.toObject(message.data_1, options);
                if (message.data_3 != null && message.hasOwnProperty("data_3")) object.data_3 = $root.proto.TavernInfo.toObject(message.data_3, options);
                return object;
              };
              /**
               * Converts this EncounterInfo to JSON.
               * @function toJSON
               * @memberof proto.EncounterInfo
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              EncounterInfo.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return EncounterInfo;
            }();

            proto.PlayerInfo = function () {
              /**
               * Properties of a PlayerInfo.
               * @memberof proto
               * @interface IPlayerInfo
               * @property {string|null} [uid] PlayerInfo uid
               * @property {string|null} [nickname] PlayerInfo nickname
               * @property {Object.<string,proto.IHero>|null} [battleAreas] PlayerInfo battleAreas
               * @property {Object.<string,proto.IHero>|null} [prepareAreas] PlayerInfo prepareAreas
               * @property {number|null} [roleId] PlayerInfo roleId
               * @property {number|null} [day] PlayerInfo day
               * @property {number|null} [hour] PlayerInfo hour
               * @property {number|null} [hp] PlayerInfo hp
               * @property {number|null} [winCount] PlayerInfo winCount
               * @property {number|null} [gold] PlayerInfo gold
               * @property {number|null} [earnings] PlayerInfo earnings
               */

              /**
               * Constructs a new PlayerInfo.
               * @memberof proto
               * @classdesc Represents a PlayerInfo.
               * @implements IPlayerInfo
               * @constructor
               * @param {proto.IPlayerInfo=} [properties] Properties to set
               */
              function PlayerInfo(properties) {
                this.battleAreas = {};
                this.prepareAreas = {};
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * PlayerInfo uid.
               * @member {string} uid
               * @memberof proto.PlayerInfo
               * @instance
               */


              PlayerInfo.prototype.uid = "";
              /**
               * PlayerInfo nickname.
               * @member {string} nickname
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.nickname = "";
              /**
               * PlayerInfo battleAreas.
               * @member {Object.<string,proto.IHero>} battleAreas
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.battleAreas = $util.emptyObject;
              /**
               * PlayerInfo prepareAreas.
               * @member {Object.<string,proto.IHero>} prepareAreas
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.prepareAreas = $util.emptyObject;
              /**
               * PlayerInfo roleId.
               * @member {number} roleId
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.roleId = 0;
              /**
               * PlayerInfo day.
               * @member {number} day
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.day = 0;
              /**
               * PlayerInfo hour.
               * @member {number} hour
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.hour = 0;
              /**
               * PlayerInfo hp.
               * @member {number} hp
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.hp = 0;
              /**
               * PlayerInfo winCount.
               * @member {number} winCount
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.winCount = 0;
              /**
               * PlayerInfo gold.
               * @member {number} gold
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.gold = 0;
              /**
               * PlayerInfo earnings.
               * @member {number} earnings
               * @memberof proto.PlayerInfo
               * @instance
               */

              PlayerInfo.prototype.earnings = 0;
              /**
               * Creates a new PlayerInfo instance using the specified properties.
               * @function create
               * @memberof proto.PlayerInfo
               * @static
               * @param {proto.IPlayerInfo=} [properties] Properties to set
               * @returns {proto.PlayerInfo} PlayerInfo instance
               */

              PlayerInfo.create = function create(properties) {
                return new PlayerInfo(properties);
              };
              /**
               * Encodes the specified PlayerInfo message. Does not implicitly {@link proto.PlayerInfo.verify|verify} messages.
               * @function encode
               * @memberof proto.PlayerInfo
               * @static
               * @param {proto.IPlayerInfo} message PlayerInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              PlayerInfo.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.uid != null && message.hasOwnProperty("uid")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.uid);
                if (message.nickname != null && message.hasOwnProperty("nickname")) writer.uint32(
                /* id 2, wireType 2 =*/
                18).string(message.nickname);
                if (message.battleAreas != null && message.hasOwnProperty("battleAreas")) for (var keys = Object.keys(message.battleAreas), i = 0; i < keys.length; ++i) {
                  writer.uint32(
                  /* id 3, wireType 2 =*/
                  26).fork().uint32(
                  /* id 1, wireType 0 =*/
                  8).int32(keys[i]);
                  $root.proto.Hero.encode(message.battleAreas[keys[i]], writer.uint32(
                  /* id 2, wireType 2 =*/
                  18).fork()).ldelim().ldelim();
                }
                if (message.prepareAreas != null && message.hasOwnProperty("prepareAreas")) for (var keys = Object.keys(message.prepareAreas), i = 0; i < keys.length; ++i) {
                  writer.uint32(
                  /* id 4, wireType 2 =*/
                  34).fork().uint32(
                  /* id 1, wireType 0 =*/
                  8).int32(keys[i]);
                  $root.proto.Hero.encode(message.prepareAreas[keys[i]], writer.uint32(
                  /* id 2, wireType 2 =*/
                  18).fork()).ldelim().ldelim();
                }
                if (message.roleId != null && message.hasOwnProperty("roleId")) writer.uint32(
                /* id 5, wireType 0 =*/
                40).int32(message.roleId);
                if (message.day != null && message.hasOwnProperty("day")) writer.uint32(
                /* id 6, wireType 0 =*/
                48).int32(message.day);
                if (message.hour != null && message.hasOwnProperty("hour")) writer.uint32(
                /* id 7, wireType 0 =*/
                56).int32(message.hour);
                if (message.hp != null && message.hasOwnProperty("hp")) writer.uint32(
                /* id 8, wireType 0 =*/
                64).int32(message.hp);
                if (message.winCount != null && message.hasOwnProperty("winCount")) writer.uint32(
                /* id 9, wireType 0 =*/
                72).int32(message.winCount);
                if (message.gold != null && message.hasOwnProperty("gold")) writer.uint32(
                /* id 10, wireType 0 =*/
                80).int32(message.gold);
                if (message.earnings != null && message.hasOwnProperty("earnings")) writer.uint32(
                /* id 11, wireType 0 =*/
                88).int32(message.earnings);
                return writer;
              };
              /**
               * Encodes the specified PlayerInfo message, length delimited. Does not implicitly {@link proto.PlayerInfo.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.PlayerInfo
               * @static
               * @param {proto.IPlayerInfo} message PlayerInfo message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              PlayerInfo.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a PlayerInfo message from the specified reader or buffer.
               * @function decode
               * @memberof proto.PlayerInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.PlayerInfo} PlayerInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              PlayerInfo.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.PlayerInfo(),
                    key;

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.uid = reader.string();
                      break;

                    case 2:
                      message.nickname = reader.string();
                      break;

                    case 3:
                      reader.skip().pos++;
                      if (message.battleAreas === $util.emptyObject) message.battleAreas = {};
                      key = reader.int32();
                      reader.pos++;
                      message.battleAreas[key] = $root.proto.Hero.decode(reader, reader.uint32());
                      break;

                    case 4:
                      reader.skip().pos++;
                      if (message.prepareAreas === $util.emptyObject) message.prepareAreas = {};
                      key = reader.int32();
                      reader.pos++;
                      message.prepareAreas[key] = $root.proto.Hero.decode(reader, reader.uint32());
                      break;

                    case 5:
                      message.roleId = reader.int32();
                      break;

                    case 6:
                      message.day = reader.int32();
                      break;

                    case 7:
                      message.hour = reader.int32();
                      break;

                    case 8:
                      message.hp = reader.int32();
                      break;

                    case 9:
                      message.winCount = reader.int32();
                      break;

                    case 10:
                      message.gold = reader.int32();
                      break;

                    case 11:
                      message.earnings = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a PlayerInfo message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.PlayerInfo
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.PlayerInfo} PlayerInfo
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              PlayerInfo.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a PlayerInfo message.
               * @function verify
               * @memberof proto.PlayerInfo
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              PlayerInfo.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.uid != null && message.hasOwnProperty("uid")) if (!$util.isString(message.uid)) return "uid: string expected";
                if (message.nickname != null && message.hasOwnProperty("nickname")) if (!$util.isString(message.nickname)) return "nickname: string expected";

                if (message.battleAreas != null && message.hasOwnProperty("battleAreas")) {
                  if (!$util.isObject(message.battleAreas)) return "battleAreas: object expected";
                  var key = Object.keys(message.battleAreas);

                  for (var i = 0; i < key.length; ++i) {
                    if (!$util.key32Re.test(key[i])) return "battleAreas: integer key{k:int32} expected";
                    {
                      var error = $root.proto.Hero.verify(message.battleAreas[key[i]]);
                      if (error) return "battleAreas." + error;
                    }
                  }
                }

                if (message.prepareAreas != null && message.hasOwnProperty("prepareAreas")) {
                  if (!$util.isObject(message.prepareAreas)) return "prepareAreas: object expected";
                  var key = Object.keys(message.prepareAreas);

                  for (var i = 0; i < key.length; ++i) {
                    if (!$util.key32Re.test(key[i])) return "prepareAreas: integer key{k:int32} expected";
                    {
                      var error = $root.proto.Hero.verify(message.prepareAreas[key[i]]);
                      if (error) return "prepareAreas." + error;
                    }
                  }
                }

                if (message.roleId != null && message.hasOwnProperty("roleId")) if (!$util.isInteger(message.roleId)) return "roleId: integer expected";
                if (message.day != null && message.hasOwnProperty("day")) if (!$util.isInteger(message.day)) return "day: integer expected";
                if (message.hour != null && message.hasOwnProperty("hour")) if (!$util.isInteger(message.hour)) return "hour: integer expected";
                if (message.hp != null && message.hasOwnProperty("hp")) if (!$util.isInteger(message.hp)) return "hp: integer expected";
                if (message.winCount != null && message.hasOwnProperty("winCount")) if (!$util.isInteger(message.winCount)) return "winCount: integer expected";
                if (message.gold != null && message.hasOwnProperty("gold")) if (!$util.isInteger(message.gold)) return "gold: integer expected";
                if (message.earnings != null && message.hasOwnProperty("earnings")) if (!$util.isInteger(message.earnings)) return "earnings: integer expected";
                return null;
              };
              /**
               * Creates a PlayerInfo message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.PlayerInfo
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.PlayerInfo} PlayerInfo
               */


              PlayerInfo.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.PlayerInfo) return object;
                var message = new $root.proto.PlayerInfo();
                if (object.uid != null) message.uid = String(object.uid);
                if (object.nickname != null) message.nickname = String(object.nickname);

                if (object.battleAreas) {
                  if (typeof object.battleAreas !== "object") throw TypeError(".proto.PlayerInfo.battleAreas: object expected");
                  message.battleAreas = {};

                  for (var keys = Object.keys(object.battleAreas), i = 0; i < keys.length; ++i) {
                    if (typeof object.battleAreas[keys[i]] !== "object") throw TypeError(".proto.PlayerInfo.battleAreas: object expected");
                    message.battleAreas[keys[i]] = $root.proto.Hero.fromObject(object.battleAreas[keys[i]]);
                  }
                }

                if (object.prepareAreas) {
                  if (typeof object.prepareAreas !== "object") throw TypeError(".proto.PlayerInfo.prepareAreas: object expected");
                  message.prepareAreas = {};

                  for (var keys = Object.keys(object.prepareAreas), i = 0; i < keys.length; ++i) {
                    if (typeof object.prepareAreas[keys[i]] !== "object") throw TypeError(".proto.PlayerInfo.prepareAreas: object expected");
                    message.prepareAreas[keys[i]] = $root.proto.Hero.fromObject(object.prepareAreas[keys[i]]);
                  }
                }

                if (object.roleId != null) message.roleId = object.roleId | 0;
                if (object.day != null) message.day = object.day | 0;
                if (object.hour != null) message.hour = object.hour | 0;
                if (object.hp != null) message.hp = object.hp | 0;
                if (object.winCount != null) message.winCount = object.winCount | 0;
                if (object.gold != null) message.gold = object.gold | 0;
                if (object.earnings != null) message.earnings = object.earnings | 0;
                return message;
              };
              /**
               * Creates a plain object from a PlayerInfo message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.PlayerInfo
               * @static
               * @param {proto.PlayerInfo} message PlayerInfo
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              PlayerInfo.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.objects || options.defaults) {
                  object.battleAreas = {};
                  object.prepareAreas = {};
                }

                if (options.defaults) {
                  object.uid = "";
                  object.nickname = "";
                  object.roleId = 0;
                  object.day = 0;
                  object.hour = 0;
                  object.hp = 0;
                  object.winCount = 0;
                  object.gold = 0;
                  object.earnings = 0;
                }

                if (message.uid != null && message.hasOwnProperty("uid")) object.uid = message.uid;
                if (message.nickname != null && message.hasOwnProperty("nickname")) object.nickname = message.nickname;
                var keys2;

                if (message.battleAreas && (keys2 = Object.keys(message.battleAreas)).length) {
                  object.battleAreas = {};

                  for (var j = 0; j < keys2.length; ++j) object.battleAreas[keys2[j]] = $root.proto.Hero.toObject(message.battleAreas[keys2[j]], options);
                }

                if (message.prepareAreas && (keys2 = Object.keys(message.prepareAreas)).length) {
                  object.prepareAreas = {};

                  for (var j = 0; j < keys2.length; ++j) object.prepareAreas[keys2[j]] = $root.proto.Hero.toObject(message.prepareAreas[keys2[j]], options);
                }

                if (message.roleId != null && message.hasOwnProperty("roleId")) object.roleId = message.roleId;
                if (message.day != null && message.hasOwnProperty("day")) object.day = message.day;
                if (message.hour != null && message.hasOwnProperty("hour")) object.hour = message.hour;
                if (message.hp != null && message.hasOwnProperty("hp")) object.hp = message.hp;
                if (message.winCount != null && message.hasOwnProperty("winCount")) object.winCount = message.winCount;
                if (message.gold != null && message.hasOwnProperty("gold")) object.gold = message.gold;
                if (message.earnings != null && message.hasOwnProperty("earnings")) object.earnings = message.earnings;
                return object;
              };
              /**
               * Converts this PlayerInfo to JSON.
               * @function toJSON
               * @memberof proto.PlayerInfo
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              PlayerInfo.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return PlayerInfo;
            }();

            proto.GameData = function () {
              /**
               * Properties of a GameData.
               * @memberof proto
               * @interface IGameData
               * @property {proto.IPlayerInfo|null} [player] GameData player
               * @property {proto.IEncounterInfo|null} [encounter] GameData encounter
               * @property {Array.<number>|null} [mapPaths] GameData mapPaths
               */

              /**
               * Constructs a new GameData.
               * @memberof proto
               * @classdesc Represents a GameData.
               * @implements IGameData
               * @constructor
               * @param {proto.IGameData=} [properties] Properties to set
               */
              function GameData(properties) {
                this.mapPaths = [];
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GameData player.
               * @member {proto.IPlayerInfo|null|undefined} player
               * @memberof proto.GameData
               * @instance
               */


              GameData.prototype.player = null;
              /**
               * GameData encounter.
               * @member {proto.IEncounterInfo|null|undefined} encounter
               * @memberof proto.GameData
               * @instance
               */

              GameData.prototype.encounter = null;
              /**
               * GameData mapPaths.
               * @member {Array.<number>} mapPaths
               * @memberof proto.GameData
               * @instance
               */

              GameData.prototype.mapPaths = $util.emptyArray;
              /**
               * Creates a new GameData instance using the specified properties.
               * @function create
               * @memberof proto.GameData
               * @static
               * @param {proto.IGameData=} [properties] Properties to set
               * @returns {proto.GameData} GameData instance
               */

              GameData.create = function create(properties) {
                return new GameData(properties);
              };
              /**
               * Encodes the specified GameData message. Does not implicitly {@link proto.GameData.verify|verify} messages.
               * @function encode
               * @memberof proto.GameData
               * @static
               * @param {proto.IGameData} message GameData message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GameData.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.player != null && message.hasOwnProperty("player")) $root.proto.PlayerInfo.encode(message.player, writer.uint32(
                /* id 1, wireType 2 =*/
                10).fork()).ldelim();
                if (message.encounter != null && message.hasOwnProperty("encounter")) $root.proto.EncounterInfo.encode(message.encounter, writer.uint32(
                /* id 2, wireType 2 =*/
                18).fork()).ldelim();

                if (message.mapPaths != null && message.mapPaths.length) {
                  writer.uint32(
                  /* id 3, wireType 2 =*/
                  26).fork();

                  for (var i = 0; i < message.mapPaths.length; ++i) writer.int32(message.mapPaths[i]);

                  writer.ldelim();
                }

                return writer;
              };
              /**
               * Encodes the specified GameData message, length delimited. Does not implicitly {@link proto.GameData.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GameData
               * @static
               * @param {proto.IGameData} message GameData message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GameData.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GameData message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GameData
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GameData} GameData
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GameData.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GameData();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.player = $root.proto.PlayerInfo.decode(reader, reader.uint32());
                      break;

                    case 2:
                      message.encounter = $root.proto.EncounterInfo.decode(reader, reader.uint32());
                      break;

                    case 3:
                      if (!(message.mapPaths && message.mapPaths.length)) message.mapPaths = [];

                      if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;

                        while (reader.pos < end2) message.mapPaths.push(reader.int32());
                      } else message.mapPaths.push(reader.int32());

                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GameData message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GameData
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GameData} GameData
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GameData.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GameData message.
               * @function verify
               * @memberof proto.GameData
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GameData.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.player != null && message.hasOwnProperty("player")) {
                  var error = $root.proto.PlayerInfo.verify(message.player);
                  if (error) return "player." + error;
                }

                if (message.encounter != null && message.hasOwnProperty("encounter")) {
                  var error = $root.proto.EncounterInfo.verify(message.encounter);
                  if (error) return "encounter." + error;
                }

                if (message.mapPaths != null && message.hasOwnProperty("mapPaths")) {
                  if (!Array.isArray(message.mapPaths)) return "mapPaths: array expected";

                  for (var i = 0; i < message.mapPaths.length; ++i) if (!$util.isInteger(message.mapPaths[i])) return "mapPaths: integer[] expected";
                }

                return null;
              };
              /**
               * Creates a GameData message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GameData
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GameData} GameData
               */


              GameData.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GameData) return object;
                var message = new $root.proto.GameData();

                if (object.player != null) {
                  if (typeof object.player !== "object") throw TypeError(".proto.GameData.player: object expected");
                  message.player = $root.proto.PlayerInfo.fromObject(object.player);
                }

                if (object.encounter != null) {
                  if (typeof object.encounter !== "object") throw TypeError(".proto.GameData.encounter: object expected");
                  message.encounter = $root.proto.EncounterInfo.fromObject(object.encounter);
                }

                if (object.mapPaths) {
                  if (!Array.isArray(object.mapPaths)) throw TypeError(".proto.GameData.mapPaths: array expected");
                  message.mapPaths = [];

                  for (var i = 0; i < object.mapPaths.length; ++i) message.mapPaths[i] = object.mapPaths[i] | 0;
                }

                return message;
              };
              /**
               * Creates a plain object from a GameData message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GameData
               * @static
               * @param {proto.GameData} message GameData
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GameData.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.arrays || options.defaults) object.mapPaths = [];

                if (options.defaults) {
                  object.player = null;
                  object.encounter = null;
                }

                if (message.player != null && message.hasOwnProperty("player")) object.player = $root.proto.PlayerInfo.toObject(message.player, options);
                if (message.encounter != null && message.hasOwnProperty("encounter")) object.encounter = $root.proto.EncounterInfo.toObject(message.encounter, options);

                if (message.mapPaths && message.mapPaths.length) {
                  object.mapPaths = [];

                  for (var j = 0; j < message.mapPaths.length; ++j) object.mapPaths[j] = message.mapPaths[j];
                }

                return object;
              };
              /**
               * Converts this GameData to JSON.
               * @function toJSON
               * @memberof proto.GameData
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GameData.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GameData;
            }();

            proto.MapNode = function () {
              /**
               * Properties of a MapNode.
               * @memberof proto
               * @interface IMapNode
               * @property {number|null} [nodeId] MapNode nodeId
               * @property {Array.<number>|null} [children] MapNode children
               */

              /**
               * Constructs a new MapNode.
               * @memberof proto
               * @classdesc Represents a MapNode.
               * @implements IMapNode
               * @constructor
               * @param {proto.IMapNode=} [properties] Properties to set
               */
              function MapNode(properties) {
                this.children = [];
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * MapNode nodeId.
               * @member {number} nodeId
               * @memberof proto.MapNode
               * @instance
               */


              MapNode.prototype.nodeId = 0;
              /**
               * MapNode children.
               * @member {Array.<number>} children
               * @memberof proto.MapNode
               * @instance
               */

              MapNode.prototype.children = $util.emptyArray;
              /**
               * Creates a new MapNode instance using the specified properties.
               * @function create
               * @memberof proto.MapNode
               * @static
               * @param {proto.IMapNode=} [properties] Properties to set
               * @returns {proto.MapNode} MapNode instance
               */

              MapNode.create = function create(properties) {
                return new MapNode(properties);
              };
              /**
               * Encodes the specified MapNode message. Does not implicitly {@link proto.MapNode.verify|verify} messages.
               * @function encode
               * @memberof proto.MapNode
               * @static
               * @param {proto.IMapNode} message MapNode message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              MapNode.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.nodeId != null && message.hasOwnProperty("nodeId")) writer.uint32(
                /* id 1, wireType 0 =*/
                8).int32(message.nodeId);

                if (message.children != null && message.children.length) {
                  writer.uint32(
                  /* id 2, wireType 2 =*/
                  18).fork();

                  for (var i = 0; i < message.children.length; ++i) writer.int32(message.children[i]);

                  writer.ldelim();
                }

                return writer;
              };
              /**
               * Encodes the specified MapNode message, length delimited. Does not implicitly {@link proto.MapNode.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.MapNode
               * @static
               * @param {proto.IMapNode} message MapNode message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              MapNode.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a MapNode message from the specified reader or buffer.
               * @function decode
               * @memberof proto.MapNode
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.MapNode} MapNode
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              MapNode.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.MapNode();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.nodeId = reader.int32();
                      break;

                    case 2:
                      if (!(message.children && message.children.length)) message.children = [];

                      if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;

                        while (reader.pos < end2) message.children.push(reader.int32());
                      } else message.children.push(reader.int32());

                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a MapNode message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.MapNode
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.MapNode} MapNode
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              MapNode.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a MapNode message.
               * @function verify
               * @memberof proto.MapNode
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              MapNode.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.nodeId != null && message.hasOwnProperty("nodeId")) if (!$util.isInteger(message.nodeId)) return "nodeId: integer expected";

                if (message.children != null && message.hasOwnProperty("children")) {
                  if (!Array.isArray(message.children)) return "children: array expected";

                  for (var i = 0; i < message.children.length; ++i) if (!$util.isInteger(message.children[i])) return "children: integer[] expected";
                }

                return null;
              };
              /**
               * Creates a MapNode message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.MapNode
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.MapNode} MapNode
               */


              MapNode.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.MapNode) return object;
                var message = new $root.proto.MapNode();
                if (object.nodeId != null) message.nodeId = object.nodeId | 0;

                if (object.children) {
                  if (!Array.isArray(object.children)) throw TypeError(".proto.MapNode.children: array expected");
                  message.children = [];

                  for (var i = 0; i < object.children.length; ++i) message.children[i] = object.children[i] | 0;
                }

                return message;
              };
              /**
               * Creates a plain object from a MapNode message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.MapNode
               * @static
               * @param {proto.MapNode} message MapNode
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              MapNode.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.arrays || options.defaults) object.children = [];
                if (options.defaults) object.nodeId = 0;
                if (message.nodeId != null && message.hasOwnProperty("nodeId")) object.nodeId = message.nodeId;

                if (message.children && message.children.length) {
                  object.children = [];

                  for (var j = 0; j < message.children.length; ++j) object.children[j] = message.children[j];
                }

                return object;
              };
              /**
               * Converts this MapNode to JSON.
               * @function toJSON
               * @memberof proto.MapNode
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              MapNode.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return MapNode;
            }();

            proto.MapLayer = function () {
              /**
               * Properties of a MapLayer.
               * @memberof proto
               * @interface IMapLayer
               * @property {Array.<proto.IMapNode>|null} [nodes] MapLayer nodes
               */

              /**
               * Constructs a new MapLayer.
               * @memberof proto
               * @classdesc Represents a MapLayer.
               * @implements IMapLayer
               * @constructor
               * @param {proto.IMapLayer=} [properties] Properties to set
               */
              function MapLayer(properties) {
                this.nodes = [];
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * MapLayer nodes.
               * @member {Array.<proto.IMapNode>} nodes
               * @memberof proto.MapLayer
               * @instance
               */


              MapLayer.prototype.nodes = $util.emptyArray;
              /**
               * Creates a new MapLayer instance using the specified properties.
               * @function create
               * @memberof proto.MapLayer
               * @static
               * @param {proto.IMapLayer=} [properties] Properties to set
               * @returns {proto.MapLayer} MapLayer instance
               */

              MapLayer.create = function create(properties) {
                return new MapLayer(properties);
              };
              /**
               * Encodes the specified MapLayer message. Does not implicitly {@link proto.MapLayer.verify|verify} messages.
               * @function encode
               * @memberof proto.MapLayer
               * @static
               * @param {proto.IMapLayer} message MapLayer message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              MapLayer.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.nodes != null && message.nodes.length) for (var i = 0; i < message.nodes.length; ++i) $root.proto.MapNode.encode(message.nodes[i], writer.uint32(
                /* id 1, wireType 2 =*/
                10).fork()).ldelim();
                return writer;
              };
              /**
               * Encodes the specified MapLayer message, length delimited. Does not implicitly {@link proto.MapLayer.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.MapLayer
               * @static
               * @param {proto.IMapLayer} message MapLayer message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              MapLayer.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a MapLayer message from the specified reader or buffer.
               * @function decode
               * @memberof proto.MapLayer
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.MapLayer} MapLayer
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              MapLayer.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.MapLayer();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      if (!(message.nodes && message.nodes.length)) message.nodes = [];
                      message.nodes.push($root.proto.MapNode.decode(reader, reader.uint32()));
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a MapLayer message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.MapLayer
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.MapLayer} MapLayer
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              MapLayer.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a MapLayer message.
               * @function verify
               * @memberof proto.MapLayer
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              MapLayer.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.nodes != null && message.hasOwnProperty("nodes")) {
                  if (!Array.isArray(message.nodes)) return "nodes: array expected";

                  for (var i = 0; i < message.nodes.length; ++i) {
                    var error = $root.proto.MapNode.verify(message.nodes[i]);
                    if (error) return "nodes." + error;
                  }
                }

                return null;
              };
              /**
               * Creates a MapLayer message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.MapLayer
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.MapLayer} MapLayer
               */


              MapLayer.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.MapLayer) return object;
                var message = new $root.proto.MapLayer();

                if (object.nodes) {
                  if (!Array.isArray(object.nodes)) throw TypeError(".proto.MapLayer.nodes: array expected");
                  message.nodes = [];

                  for (var i = 0; i < object.nodes.length; ++i) {
                    if (typeof object.nodes[i] !== "object") throw TypeError(".proto.MapLayer.nodes: object expected");
                    message.nodes[i] = $root.proto.MapNode.fromObject(object.nodes[i]);
                  }
                }

                return message;
              };
              /**
               * Creates a plain object from a MapLayer message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.MapLayer
               * @static
               * @param {proto.MapLayer} message MapLayer
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              MapLayer.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.arrays || options.defaults) object.nodes = [];

                if (message.nodes && message.nodes.length) {
                  object.nodes = [];

                  for (var j = 0; j < message.nodes.length; ++j) object.nodes[j] = $root.proto.MapNode.toObject(message.nodes[j], options);
                }

                return object;
              };
              /**
               * Converts this MapLayer to JSON.
               * @function toJSON
               * @memberof proto.MapLayer
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              MapLayer.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return MapLayer;
            }();

            proto.MapData = function () {
              /**
               * Properties of a MapData.
               * @memberof proto
               * @interface IMapData
               * @property {Array.<proto.IMapLayer>|null} [maps] MapData maps
               */

              /**
               * Constructs a new MapData.
               * @memberof proto
               * @classdesc Represents a MapData.
               * @implements IMapData
               * @constructor
               * @param {proto.IMapData=} [properties] Properties to set
               */
              function MapData(properties) {
                this.maps = [];
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * MapData maps.
               * @member {Array.<proto.IMapLayer>} maps
               * @memberof proto.MapData
               * @instance
               */


              MapData.prototype.maps = $util.emptyArray;
              /**
               * Creates a new MapData instance using the specified properties.
               * @function create
               * @memberof proto.MapData
               * @static
               * @param {proto.IMapData=} [properties] Properties to set
               * @returns {proto.MapData} MapData instance
               */

              MapData.create = function create(properties) {
                return new MapData(properties);
              };
              /**
               * Encodes the specified MapData message. Does not implicitly {@link proto.MapData.verify|verify} messages.
               * @function encode
               * @memberof proto.MapData
               * @static
               * @param {proto.IMapData} message MapData message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              MapData.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.maps != null && message.maps.length) for (var i = 0; i < message.maps.length; ++i) $root.proto.MapLayer.encode(message.maps[i], writer.uint32(
                /* id 1, wireType 2 =*/
                10).fork()).ldelim();
                return writer;
              };
              /**
               * Encodes the specified MapData message, length delimited. Does not implicitly {@link proto.MapData.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.MapData
               * @static
               * @param {proto.IMapData} message MapData message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              MapData.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a MapData message from the specified reader or buffer.
               * @function decode
               * @memberof proto.MapData
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.MapData} MapData
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              MapData.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.MapData();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      if (!(message.maps && message.maps.length)) message.maps = [];
                      message.maps.push($root.proto.MapLayer.decode(reader, reader.uint32()));
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a MapData message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.MapData
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.MapData} MapData
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              MapData.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a MapData message.
               * @function verify
               * @memberof proto.MapData
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              MapData.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.maps != null && message.hasOwnProperty("maps")) {
                  if (!Array.isArray(message.maps)) return "maps: array expected";

                  for (var i = 0; i < message.maps.length; ++i) {
                    var error = $root.proto.MapLayer.verify(message.maps[i]);
                    if (error) return "maps." + error;
                  }
                }

                return null;
              };
              /**
               * Creates a MapData message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.MapData
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.MapData} MapData
               */


              MapData.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.MapData) return object;
                var message = new $root.proto.MapData();

                if (object.maps) {
                  if (!Array.isArray(object.maps)) throw TypeError(".proto.MapData.maps: array expected");
                  message.maps = [];

                  for (var i = 0; i < object.maps.length; ++i) {
                    if (typeof object.maps[i] !== "object") throw TypeError(".proto.MapData.maps: object expected");
                    message.maps[i] = $root.proto.MapLayer.fromObject(object.maps[i]);
                  }
                }

                return message;
              };
              /**
               * Creates a plain object from a MapData message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.MapData
               * @static
               * @param {proto.MapData} message MapData
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              MapData.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.arrays || options.defaults) object.maps = [];

                if (message.maps && message.maps.length) {
                  object.maps = [];

                  for (var j = 0; j < message.maps.length; ++j) object.maps[j] = $root.proto.MapLayer.toObject(message.maps[j], options);
                }

                return object;
              };
              /**
               * Converts this MapData to JSON.
               * @function toJSON
               * @memberof proto.MapData
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              MapData.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return MapData;
            }();

            proto.S2C_RESULT = function () {
              /**
               * Properties of a S2C_RESULT.
               * @memberof proto
               * @interface IS2C_RESULT
               * @property {Uint8Array|null} [data] S2C_RESULT data
               * @property {string|null} [error] S2C_RESULT error
               */

              /**
               * Constructs a new S2C_RESULT.
               * @memberof proto
               * @classdesc Represents a S2C_RESULT.
               * @implements IS2C_RESULT
               * @constructor
               * @param {proto.IS2C_RESULT=} [properties] Properties to set
               */
              function S2C_RESULT(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * S2C_RESULT data.
               * @member {Uint8Array} data
               * @memberof proto.S2C_RESULT
               * @instance
               */


              S2C_RESULT.prototype.data = $util.newBuffer([]);
              /**
               * S2C_RESULT error.
               * @member {string} error
               * @memberof proto.S2C_RESULT
               * @instance
               */

              S2C_RESULT.prototype.error = "";
              /**
               * Creates a new S2C_RESULT instance using the specified properties.
               * @function create
               * @memberof proto.S2C_RESULT
               * @static
               * @param {proto.IS2C_RESULT=} [properties] Properties to set
               * @returns {proto.S2C_RESULT} S2C_RESULT instance
               */

              S2C_RESULT.create = function create(properties) {
                return new S2C_RESULT(properties);
              };
              /**
               * Encodes the specified S2C_RESULT message. Does not implicitly {@link proto.S2C_RESULT.verify|verify} messages.
               * @function encode
               * @memberof proto.S2C_RESULT
               * @static
               * @param {proto.IS2C_RESULT} message S2C_RESULT message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              S2C_RESULT.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.data != null && message.hasOwnProperty("data")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).bytes(message.data);
                if (message.error != null && message.hasOwnProperty("error")) writer.uint32(
                /* id 2, wireType 2 =*/
                18).string(message.error);
                return writer;
              };
              /**
               * Encodes the specified S2C_RESULT message, length delimited. Does not implicitly {@link proto.S2C_RESULT.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.S2C_RESULT
               * @static
               * @param {proto.IS2C_RESULT} message S2C_RESULT message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              S2C_RESULT.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a S2C_RESULT message from the specified reader or buffer.
               * @function decode
               * @memberof proto.S2C_RESULT
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.S2C_RESULT} S2C_RESULT
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              S2C_RESULT.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.S2C_RESULT();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.data = reader.bytes();
                      break;

                    case 2:
                      message.error = reader.string();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a S2C_RESULT message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.S2C_RESULT
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.S2C_RESULT} S2C_RESULT
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              S2C_RESULT.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a S2C_RESULT message.
               * @function verify
               * @memberof proto.S2C_RESULT
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              S2C_RESULT.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.data != null && message.hasOwnProperty("data")) if (!(message.data && typeof message.data.length === "number" || $util.isString(message.data))) return "data: buffer expected";
                if (message.error != null && message.hasOwnProperty("error")) if (!$util.isString(message.error)) return "error: string expected";
                return null;
              };
              /**
               * Creates a S2C_RESULT message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.S2C_RESULT
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.S2C_RESULT} S2C_RESULT
               */


              S2C_RESULT.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.S2C_RESULT) return object;
                var message = new $root.proto.S2C_RESULT();
                if (object.data != null) if (typeof object.data === "string") $util.base64.decode(object.data, message.data = $util.newBuffer($util.base64.length(object.data)), 0);else if (object.data.length) message.data = object.data;
                if (object.error != null) message.error = String(object.error);
                return message;
              };
              /**
               * Creates a plain object from a S2C_RESULT message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.S2C_RESULT
               * @static
               * @param {proto.S2C_RESULT} message S2C_RESULT
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              S2C_RESULT.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  if (options.bytes === String) object.data = "";else {
                    object.data = [];
                    if (options.bytes !== Array) object.data = $util.newBuffer(object.data);
                  }
                  object.error = "";
                }

                if (message.data != null && message.hasOwnProperty("data")) object.data = options.bytes === String ? $util.base64.encode(message.data, 0, message.data.length) : options.bytes === Array ? Array.prototype.slice.call(message.data) : message.data;
                if (message.error != null && message.hasOwnProperty("error")) object.error = message.error;
                return object;
              };
              /**
               * Converts this S2C_RESULT to JSON.
               * @function toJSON
               * @memberof proto.S2C_RESULT
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              S2C_RESULT.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return S2C_RESULT;
            }();

            proto.LOGIN_HD_GUESTLOGIN_C2S = function () {
              /**
               * Properties of a LOGIN_HD_GUESTLOGIN_C2S.
               * @memberof proto
               * @interface ILOGIN_HD_GUESTLOGIN_C2S
               * @property {string|null} [guestId] LOGIN_HD_GUESTLOGIN_C2S guestId
               * @property {string|null} [nickname] LOGIN_HD_GUESTLOGIN_C2S nickname
               * @property {string|null} [platform] LOGIN_HD_GUESTLOGIN_C2S platform
               */

              /**
               * Constructs a new LOGIN_HD_GUESTLOGIN_C2S.
               * @memberof proto
               * @classdesc Represents a LOGIN_HD_GUESTLOGIN_C2S.
               * @implements ILOGIN_HD_GUESTLOGIN_C2S
               * @constructor
               * @param {proto.ILOGIN_HD_GUESTLOGIN_C2S=} [properties] Properties to set
               */
              function LOGIN_HD_GUESTLOGIN_C2S(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * LOGIN_HD_GUESTLOGIN_C2S guestId.
               * @member {string} guestId
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @instance
               */


              LOGIN_HD_GUESTLOGIN_C2S.prototype.guestId = "";
              /**
               * LOGIN_HD_GUESTLOGIN_C2S nickname.
               * @member {string} nickname
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @instance
               */

              LOGIN_HD_GUESTLOGIN_C2S.prototype.nickname = "";
              /**
               * LOGIN_HD_GUESTLOGIN_C2S platform.
               * @member {string} platform
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @instance
               */

              LOGIN_HD_GUESTLOGIN_C2S.prototype.platform = "";
              /**
               * Creates a new LOGIN_HD_GUESTLOGIN_C2S instance using the specified properties.
               * @function create
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @static
               * @param {proto.ILOGIN_HD_GUESTLOGIN_C2S=} [properties] Properties to set
               * @returns {proto.LOGIN_HD_GUESTLOGIN_C2S} LOGIN_HD_GUESTLOGIN_C2S instance
               */

              LOGIN_HD_GUESTLOGIN_C2S.create = function create(properties) {
                return new LOGIN_HD_GUESTLOGIN_C2S(properties);
              };
              /**
               * Encodes the specified LOGIN_HD_GUESTLOGIN_C2S message. Does not implicitly {@link proto.LOGIN_HD_GUESTLOGIN_C2S.verify|verify} messages.
               * @function encode
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @static
               * @param {proto.ILOGIN_HD_GUESTLOGIN_C2S} message LOGIN_HD_GUESTLOGIN_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOGIN_HD_GUESTLOGIN_C2S.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.guestId != null && message.hasOwnProperty("guestId")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.guestId);
                if (message.nickname != null && message.hasOwnProperty("nickname")) writer.uint32(
                /* id 2, wireType 2 =*/
                18).string(message.nickname);
                if (message.platform != null && message.hasOwnProperty("platform")) writer.uint32(
                /* id 3, wireType 2 =*/
                26).string(message.platform);
                return writer;
              };
              /**
               * Encodes the specified LOGIN_HD_GUESTLOGIN_C2S message, length delimited. Does not implicitly {@link proto.LOGIN_HD_GUESTLOGIN_C2S.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @static
               * @param {proto.ILOGIN_HD_GUESTLOGIN_C2S} message LOGIN_HD_GUESTLOGIN_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOGIN_HD_GUESTLOGIN_C2S.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a LOGIN_HD_GUESTLOGIN_C2S message from the specified reader or buffer.
               * @function decode
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.LOGIN_HD_GUESTLOGIN_C2S} LOGIN_HD_GUESTLOGIN_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOGIN_HD_GUESTLOGIN_C2S.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.LOGIN_HD_GUESTLOGIN_C2S();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.guestId = reader.string();
                      break;

                    case 2:
                      message.nickname = reader.string();
                      break;

                    case 3:
                      message.platform = reader.string();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a LOGIN_HD_GUESTLOGIN_C2S message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.LOGIN_HD_GUESTLOGIN_C2S} LOGIN_HD_GUESTLOGIN_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOGIN_HD_GUESTLOGIN_C2S.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a LOGIN_HD_GUESTLOGIN_C2S message.
               * @function verify
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              LOGIN_HD_GUESTLOGIN_C2S.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.guestId != null && message.hasOwnProperty("guestId")) if (!$util.isString(message.guestId)) return "guestId: string expected";
                if (message.nickname != null && message.hasOwnProperty("nickname")) if (!$util.isString(message.nickname)) return "nickname: string expected";
                if (message.platform != null && message.hasOwnProperty("platform")) if (!$util.isString(message.platform)) return "platform: string expected";
                return null;
              };
              /**
               * Creates a LOGIN_HD_GUESTLOGIN_C2S message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.LOGIN_HD_GUESTLOGIN_C2S} LOGIN_HD_GUESTLOGIN_C2S
               */


              LOGIN_HD_GUESTLOGIN_C2S.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.LOGIN_HD_GUESTLOGIN_C2S) return object;
                var message = new $root.proto.LOGIN_HD_GUESTLOGIN_C2S();
                if (object.guestId != null) message.guestId = String(object.guestId);
                if (object.nickname != null) message.nickname = String(object.nickname);
                if (object.platform != null) message.platform = String(object.platform);
                return message;
              };
              /**
               * Creates a plain object from a LOGIN_HD_GUESTLOGIN_C2S message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @static
               * @param {proto.LOGIN_HD_GUESTLOGIN_C2S} message LOGIN_HD_GUESTLOGIN_C2S
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              LOGIN_HD_GUESTLOGIN_C2S.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.guestId = "";
                  object.nickname = "";
                  object.platform = "";
                }

                if (message.guestId != null && message.hasOwnProperty("guestId")) object.guestId = message.guestId;
                if (message.nickname != null && message.hasOwnProperty("nickname")) object.nickname = message.nickname;
                if (message.platform != null && message.hasOwnProperty("platform")) object.platform = message.platform;
                return object;
              };
              /**
               * Converts this LOGIN_HD_GUESTLOGIN_C2S to JSON.
               * @function toJSON
               * @memberof proto.LOGIN_HD_GUESTLOGIN_C2S
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              LOGIN_HD_GUESTLOGIN_C2S.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return LOGIN_HD_GUESTLOGIN_C2S;
            }();

            proto.LOGIN_HD_GUESTLOGIN_S2C = function () {
              /**
               * Properties of a LOGIN_HD_GUESTLOGIN_S2C.
               * @memberof proto
               * @interface ILOGIN_HD_GUESTLOGIN_S2C
               * @property {string|null} [accountToken] LOGIN_HD_GUESTLOGIN_S2C accountToken
               * @property {string|null} [guestId] LOGIN_HD_GUESTLOGIN_S2C guestId
               */

              /**
               * Constructs a new LOGIN_HD_GUESTLOGIN_S2C.
               * @memberof proto
               * @classdesc Represents a LOGIN_HD_GUESTLOGIN_S2C.
               * @implements ILOGIN_HD_GUESTLOGIN_S2C
               * @constructor
               * @param {proto.ILOGIN_HD_GUESTLOGIN_S2C=} [properties] Properties to set
               */
              function LOGIN_HD_GUESTLOGIN_S2C(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * LOGIN_HD_GUESTLOGIN_S2C accountToken.
               * @member {string} accountToken
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @instance
               */


              LOGIN_HD_GUESTLOGIN_S2C.prototype.accountToken = "";
              /**
               * LOGIN_HD_GUESTLOGIN_S2C guestId.
               * @member {string} guestId
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @instance
               */

              LOGIN_HD_GUESTLOGIN_S2C.prototype.guestId = "";
              /**
               * Creates a new LOGIN_HD_GUESTLOGIN_S2C instance using the specified properties.
               * @function create
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @static
               * @param {proto.ILOGIN_HD_GUESTLOGIN_S2C=} [properties] Properties to set
               * @returns {proto.LOGIN_HD_GUESTLOGIN_S2C} LOGIN_HD_GUESTLOGIN_S2C instance
               */

              LOGIN_HD_GUESTLOGIN_S2C.create = function create(properties) {
                return new LOGIN_HD_GUESTLOGIN_S2C(properties);
              };
              /**
               * Encodes the specified LOGIN_HD_GUESTLOGIN_S2C message. Does not implicitly {@link proto.LOGIN_HD_GUESTLOGIN_S2C.verify|verify} messages.
               * @function encode
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @static
               * @param {proto.ILOGIN_HD_GUESTLOGIN_S2C} message LOGIN_HD_GUESTLOGIN_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOGIN_HD_GUESTLOGIN_S2C.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.accountToken != null && message.hasOwnProperty("accountToken")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.accountToken);
                if (message.guestId != null && message.hasOwnProperty("guestId")) writer.uint32(
                /* id 2, wireType 2 =*/
                18).string(message.guestId);
                return writer;
              };
              /**
               * Encodes the specified LOGIN_HD_GUESTLOGIN_S2C message, length delimited. Does not implicitly {@link proto.LOGIN_HD_GUESTLOGIN_S2C.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @static
               * @param {proto.ILOGIN_HD_GUESTLOGIN_S2C} message LOGIN_HD_GUESTLOGIN_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOGIN_HD_GUESTLOGIN_S2C.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a LOGIN_HD_GUESTLOGIN_S2C message from the specified reader or buffer.
               * @function decode
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.LOGIN_HD_GUESTLOGIN_S2C} LOGIN_HD_GUESTLOGIN_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOGIN_HD_GUESTLOGIN_S2C.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.LOGIN_HD_GUESTLOGIN_S2C();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.accountToken = reader.string();
                      break;

                    case 2:
                      message.guestId = reader.string();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a LOGIN_HD_GUESTLOGIN_S2C message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.LOGIN_HD_GUESTLOGIN_S2C} LOGIN_HD_GUESTLOGIN_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOGIN_HD_GUESTLOGIN_S2C.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a LOGIN_HD_GUESTLOGIN_S2C message.
               * @function verify
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              LOGIN_HD_GUESTLOGIN_S2C.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.accountToken != null && message.hasOwnProperty("accountToken")) if (!$util.isString(message.accountToken)) return "accountToken: string expected";
                if (message.guestId != null && message.hasOwnProperty("guestId")) if (!$util.isString(message.guestId)) return "guestId: string expected";
                return null;
              };
              /**
               * Creates a LOGIN_HD_GUESTLOGIN_S2C message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.LOGIN_HD_GUESTLOGIN_S2C} LOGIN_HD_GUESTLOGIN_S2C
               */


              LOGIN_HD_GUESTLOGIN_S2C.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.LOGIN_HD_GUESTLOGIN_S2C) return object;
                var message = new $root.proto.LOGIN_HD_GUESTLOGIN_S2C();
                if (object.accountToken != null) message.accountToken = String(object.accountToken);
                if (object.guestId != null) message.guestId = String(object.guestId);
                return message;
              };
              /**
               * Creates a plain object from a LOGIN_HD_GUESTLOGIN_S2C message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @static
               * @param {proto.LOGIN_HD_GUESTLOGIN_S2C} message LOGIN_HD_GUESTLOGIN_S2C
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              LOGIN_HD_GUESTLOGIN_S2C.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.accountToken = "";
                  object.guestId = "";
                }

                if (message.accountToken != null && message.hasOwnProperty("accountToken")) object.accountToken = message.accountToken;
                if (message.guestId != null && message.hasOwnProperty("guestId")) object.guestId = message.guestId;
                return object;
              };
              /**
               * Converts this LOGIN_HD_GUESTLOGIN_S2C to JSON.
               * @function toJSON
               * @memberof proto.LOGIN_HD_GUESTLOGIN_S2C
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              LOGIN_HD_GUESTLOGIN_S2C.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return LOGIN_HD_GUESTLOGIN_S2C;
            }();

            proto.LOBBY_HD_TRYLOGIN_C2S = function () {
              /**
               * Properties of a LOBBY_HD_TRYLOGIN_C2S.
               * @memberof proto
               * @interface ILOBBY_HD_TRYLOGIN_C2S
               * @property {string|null} [accountToken] LOBBY_HD_TRYLOGIN_C2S accountToken
               * @property {string|null} [lang] LOBBY_HD_TRYLOGIN_C2S lang
               * @property {string|null} [platform] LOBBY_HD_TRYLOGIN_C2S platform
               * @property {string|null} [version] LOBBY_HD_TRYLOGIN_C2S version
               */

              /**
               * Constructs a new LOBBY_HD_TRYLOGIN_C2S.
               * @memberof proto
               * @classdesc Represents a LOBBY_HD_TRYLOGIN_C2S.
               * @implements ILOBBY_HD_TRYLOGIN_C2S
               * @constructor
               * @param {proto.ILOBBY_HD_TRYLOGIN_C2S=} [properties] Properties to set
               */
              function LOBBY_HD_TRYLOGIN_C2S(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * LOBBY_HD_TRYLOGIN_C2S accountToken.
               * @member {string} accountToken
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @instance
               */


              LOBBY_HD_TRYLOGIN_C2S.prototype.accountToken = "";
              /**
               * LOBBY_HD_TRYLOGIN_C2S lang.
               * @member {string} lang
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @instance
               */

              LOBBY_HD_TRYLOGIN_C2S.prototype.lang = "";
              /**
               * LOBBY_HD_TRYLOGIN_C2S platform.
               * @member {string} platform
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @instance
               */

              LOBBY_HD_TRYLOGIN_C2S.prototype.platform = "";
              /**
               * LOBBY_HD_TRYLOGIN_C2S version.
               * @member {string} version
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @instance
               */

              LOBBY_HD_TRYLOGIN_C2S.prototype.version = "";
              /**
               * Creates a new LOBBY_HD_TRYLOGIN_C2S instance using the specified properties.
               * @function create
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @static
               * @param {proto.ILOBBY_HD_TRYLOGIN_C2S=} [properties] Properties to set
               * @returns {proto.LOBBY_HD_TRYLOGIN_C2S} LOBBY_HD_TRYLOGIN_C2S instance
               */

              LOBBY_HD_TRYLOGIN_C2S.create = function create(properties) {
                return new LOBBY_HD_TRYLOGIN_C2S(properties);
              };
              /**
               * Encodes the specified LOBBY_HD_TRYLOGIN_C2S message. Does not implicitly {@link proto.LOBBY_HD_TRYLOGIN_C2S.verify|verify} messages.
               * @function encode
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @static
               * @param {proto.ILOBBY_HD_TRYLOGIN_C2S} message LOBBY_HD_TRYLOGIN_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOBBY_HD_TRYLOGIN_C2S.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.accountToken != null && message.hasOwnProperty("accountToken")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.accountToken);
                if (message.lang != null && message.hasOwnProperty("lang")) writer.uint32(
                /* id 2, wireType 2 =*/
                18).string(message.lang);
                if (message.platform != null && message.hasOwnProperty("platform")) writer.uint32(
                /* id 3, wireType 2 =*/
                26).string(message.platform);
                if (message.version != null && message.hasOwnProperty("version")) writer.uint32(
                /* id 4, wireType 2 =*/
                34).string(message.version);
                return writer;
              };
              /**
               * Encodes the specified LOBBY_HD_TRYLOGIN_C2S message, length delimited. Does not implicitly {@link proto.LOBBY_HD_TRYLOGIN_C2S.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @static
               * @param {proto.ILOBBY_HD_TRYLOGIN_C2S} message LOBBY_HD_TRYLOGIN_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOBBY_HD_TRYLOGIN_C2S.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a LOBBY_HD_TRYLOGIN_C2S message from the specified reader or buffer.
               * @function decode
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.LOBBY_HD_TRYLOGIN_C2S} LOBBY_HD_TRYLOGIN_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOBBY_HD_TRYLOGIN_C2S.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.LOBBY_HD_TRYLOGIN_C2S();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.accountToken = reader.string();
                      break;

                    case 2:
                      message.lang = reader.string();
                      break;

                    case 3:
                      message.platform = reader.string();
                      break;

                    case 4:
                      message.version = reader.string();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a LOBBY_HD_TRYLOGIN_C2S message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.LOBBY_HD_TRYLOGIN_C2S} LOBBY_HD_TRYLOGIN_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOBBY_HD_TRYLOGIN_C2S.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a LOBBY_HD_TRYLOGIN_C2S message.
               * @function verify
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              LOBBY_HD_TRYLOGIN_C2S.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.accountToken != null && message.hasOwnProperty("accountToken")) if (!$util.isString(message.accountToken)) return "accountToken: string expected";
                if (message.lang != null && message.hasOwnProperty("lang")) if (!$util.isString(message.lang)) return "lang: string expected";
                if (message.platform != null && message.hasOwnProperty("platform")) if (!$util.isString(message.platform)) return "platform: string expected";
                if (message.version != null && message.hasOwnProperty("version")) if (!$util.isString(message.version)) return "version: string expected";
                return null;
              };
              /**
               * Creates a LOBBY_HD_TRYLOGIN_C2S message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.LOBBY_HD_TRYLOGIN_C2S} LOBBY_HD_TRYLOGIN_C2S
               */


              LOBBY_HD_TRYLOGIN_C2S.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.LOBBY_HD_TRYLOGIN_C2S) return object;
                var message = new $root.proto.LOBBY_HD_TRYLOGIN_C2S();
                if (object.accountToken != null) message.accountToken = String(object.accountToken);
                if (object.lang != null) message.lang = String(object.lang);
                if (object.platform != null) message.platform = String(object.platform);
                if (object.version != null) message.version = String(object.version);
                return message;
              };
              /**
               * Creates a plain object from a LOBBY_HD_TRYLOGIN_C2S message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @static
               * @param {proto.LOBBY_HD_TRYLOGIN_C2S} message LOBBY_HD_TRYLOGIN_C2S
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              LOBBY_HD_TRYLOGIN_C2S.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.accountToken = "";
                  object.lang = "";
                  object.platform = "";
                  object.version = "";
                }

                if (message.accountToken != null && message.hasOwnProperty("accountToken")) object.accountToken = message.accountToken;
                if (message.lang != null && message.hasOwnProperty("lang")) object.lang = message.lang;
                if (message.platform != null && message.hasOwnProperty("platform")) object.platform = message.platform;
                if (message.version != null && message.hasOwnProperty("version")) object.version = message.version;
                return object;
              };
              /**
               * Converts this LOBBY_HD_TRYLOGIN_C2S to JSON.
               * @function toJSON
               * @memberof proto.LOBBY_HD_TRYLOGIN_C2S
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              LOBBY_HD_TRYLOGIN_C2S.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return LOBBY_HD_TRYLOGIN_C2S;
            }();

            proto.LOBBY_HD_TRYLOGIN_S2C = function () {
              /**
               * Properties of a LOBBY_HD_TRYLOGIN_S2C.
               * @memberof proto
               * @interface ILOBBY_HD_TRYLOGIN_S2C
               * @property {proto.IUserInfo|null} [user] LOBBY_HD_TRYLOGIN_S2C user
               * @property {string|null} [accountToken] LOBBY_HD_TRYLOGIN_S2C accountToken
               * @property {number|null} [banAccountType] LOBBY_HD_TRYLOGIN_S2C banAccountType
               * @property {number|Long|null} [banAccountSurplusTime] LOBBY_HD_TRYLOGIN_S2C banAccountSurplusTime
               * @property {proto.IPlayerInfo|null} [gameBaseData] LOBBY_HD_TRYLOGIN_S2C gameBaseData
               */

              /**
               * Constructs a new LOBBY_HD_TRYLOGIN_S2C.
               * @memberof proto
               * @classdesc Represents a LOBBY_HD_TRYLOGIN_S2C.
               * @implements ILOBBY_HD_TRYLOGIN_S2C
               * @constructor
               * @param {proto.ILOBBY_HD_TRYLOGIN_S2C=} [properties] Properties to set
               */
              function LOBBY_HD_TRYLOGIN_S2C(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * LOBBY_HD_TRYLOGIN_S2C user.
               * @member {proto.IUserInfo|null|undefined} user
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @instance
               */


              LOBBY_HD_TRYLOGIN_S2C.prototype.user = null;
              /**
               * LOBBY_HD_TRYLOGIN_S2C accountToken.
               * @member {string} accountToken
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @instance
               */

              LOBBY_HD_TRYLOGIN_S2C.prototype.accountToken = "";
              /**
               * LOBBY_HD_TRYLOGIN_S2C banAccountType.
               * @member {number} banAccountType
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @instance
               */

              LOBBY_HD_TRYLOGIN_S2C.prototype.banAccountType = 0;
              /**
               * LOBBY_HD_TRYLOGIN_S2C banAccountSurplusTime.
               * @member {number|Long} banAccountSurplusTime
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @instance
               */

              LOBBY_HD_TRYLOGIN_S2C.prototype.banAccountSurplusTime = $util.Long ? $util.Long.fromBits(0, 0, false) : 0;
              /**
               * LOBBY_HD_TRYLOGIN_S2C gameBaseData.
               * @member {proto.IPlayerInfo|null|undefined} gameBaseData
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @instance
               */

              LOBBY_HD_TRYLOGIN_S2C.prototype.gameBaseData = null;
              /**
               * Creates a new LOBBY_HD_TRYLOGIN_S2C instance using the specified properties.
               * @function create
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @static
               * @param {proto.ILOBBY_HD_TRYLOGIN_S2C=} [properties] Properties to set
               * @returns {proto.LOBBY_HD_TRYLOGIN_S2C} LOBBY_HD_TRYLOGIN_S2C instance
               */

              LOBBY_HD_TRYLOGIN_S2C.create = function create(properties) {
                return new LOBBY_HD_TRYLOGIN_S2C(properties);
              };
              /**
               * Encodes the specified LOBBY_HD_TRYLOGIN_S2C message. Does not implicitly {@link proto.LOBBY_HD_TRYLOGIN_S2C.verify|verify} messages.
               * @function encode
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @static
               * @param {proto.ILOBBY_HD_TRYLOGIN_S2C} message LOBBY_HD_TRYLOGIN_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOBBY_HD_TRYLOGIN_S2C.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.user != null && message.hasOwnProperty("user")) $root.proto.UserInfo.encode(message.user, writer.uint32(
                /* id 1, wireType 2 =*/
                10).fork()).ldelim();
                if (message.accountToken != null && message.hasOwnProperty("accountToken")) writer.uint32(
                /* id 2, wireType 2 =*/
                18).string(message.accountToken);
                if (message.banAccountType != null && message.hasOwnProperty("banAccountType")) writer.uint32(
                /* id 3, wireType 0 =*/
                24).int32(message.banAccountType);
                if (message.banAccountSurplusTime != null && message.hasOwnProperty("banAccountSurplusTime")) writer.uint32(
                /* id 4, wireType 0 =*/
                32).int64(message.banAccountSurplusTime);
                if (message.gameBaseData != null && message.hasOwnProperty("gameBaseData")) $root.proto.PlayerInfo.encode(message.gameBaseData, writer.uint32(
                /* id 5, wireType 2 =*/
                42).fork()).ldelim();
                return writer;
              };
              /**
               * Encodes the specified LOBBY_HD_TRYLOGIN_S2C message, length delimited. Does not implicitly {@link proto.LOBBY_HD_TRYLOGIN_S2C.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @static
               * @param {proto.ILOBBY_HD_TRYLOGIN_S2C} message LOBBY_HD_TRYLOGIN_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOBBY_HD_TRYLOGIN_S2C.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a LOBBY_HD_TRYLOGIN_S2C message from the specified reader or buffer.
               * @function decode
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.LOBBY_HD_TRYLOGIN_S2C} LOBBY_HD_TRYLOGIN_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOBBY_HD_TRYLOGIN_S2C.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.LOBBY_HD_TRYLOGIN_S2C();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.user = $root.proto.UserInfo.decode(reader, reader.uint32());
                      break;

                    case 2:
                      message.accountToken = reader.string();
                      break;

                    case 3:
                      message.banAccountType = reader.int32();
                      break;

                    case 4:
                      message.banAccountSurplusTime = reader.int64();
                      break;

                    case 5:
                      message.gameBaseData = $root.proto.PlayerInfo.decode(reader, reader.uint32());
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a LOBBY_HD_TRYLOGIN_S2C message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.LOBBY_HD_TRYLOGIN_S2C} LOBBY_HD_TRYLOGIN_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOBBY_HD_TRYLOGIN_S2C.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a LOBBY_HD_TRYLOGIN_S2C message.
               * @function verify
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              LOBBY_HD_TRYLOGIN_S2C.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.user != null && message.hasOwnProperty("user")) {
                  var error = $root.proto.UserInfo.verify(message.user);
                  if (error) return "user." + error;
                }

                if (message.accountToken != null && message.hasOwnProperty("accountToken")) if (!$util.isString(message.accountToken)) return "accountToken: string expected";
                if (message.banAccountType != null && message.hasOwnProperty("banAccountType")) if (!$util.isInteger(message.banAccountType)) return "banAccountType: integer expected";
                if (message.banAccountSurplusTime != null && message.hasOwnProperty("banAccountSurplusTime")) if (!$util.isInteger(message.banAccountSurplusTime) && !(message.banAccountSurplusTime && $util.isInteger(message.banAccountSurplusTime.low) && $util.isInteger(message.banAccountSurplusTime.high))) return "banAccountSurplusTime: integer|Long expected";

                if (message.gameBaseData != null && message.hasOwnProperty("gameBaseData")) {
                  var error = $root.proto.PlayerInfo.verify(message.gameBaseData);
                  if (error) return "gameBaseData." + error;
                }

                return null;
              };
              /**
               * Creates a LOBBY_HD_TRYLOGIN_S2C message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.LOBBY_HD_TRYLOGIN_S2C} LOBBY_HD_TRYLOGIN_S2C
               */


              LOBBY_HD_TRYLOGIN_S2C.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.LOBBY_HD_TRYLOGIN_S2C) return object;
                var message = new $root.proto.LOBBY_HD_TRYLOGIN_S2C();

                if (object.user != null) {
                  if (typeof object.user !== "object") throw TypeError(".proto.LOBBY_HD_TRYLOGIN_S2C.user: object expected");
                  message.user = $root.proto.UserInfo.fromObject(object.user);
                }

                if (object.accountToken != null) message.accountToken = String(object.accountToken);
                if (object.banAccountType != null) message.banAccountType = object.banAccountType | 0;
                if (object.banAccountSurplusTime != null) if ($util.Long) (message.banAccountSurplusTime = $util.Long.fromValue(object.banAccountSurplusTime)).unsigned = false;else if (typeof object.banAccountSurplusTime === "string") message.banAccountSurplusTime = parseInt(object.banAccountSurplusTime, 10);else if (typeof object.banAccountSurplusTime === "number") message.banAccountSurplusTime = object.banAccountSurplusTime;else if (typeof object.banAccountSurplusTime === "object") message.banAccountSurplusTime = new $util.LongBits(object.banAccountSurplusTime.low >>> 0, object.banAccountSurplusTime.high >>> 0).toNumber();

                if (object.gameBaseData != null) {
                  if (typeof object.gameBaseData !== "object") throw TypeError(".proto.LOBBY_HD_TRYLOGIN_S2C.gameBaseData: object expected");
                  message.gameBaseData = $root.proto.PlayerInfo.fromObject(object.gameBaseData);
                }

                return message;
              };
              /**
               * Creates a plain object from a LOBBY_HD_TRYLOGIN_S2C message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @static
               * @param {proto.LOBBY_HD_TRYLOGIN_S2C} message LOBBY_HD_TRYLOGIN_S2C
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              LOBBY_HD_TRYLOGIN_S2C.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.user = null;
                  object.accountToken = "";
                  object.banAccountType = 0;

                  if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.banAccountSurplusTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                  } else object.banAccountSurplusTime = options.longs === String ? "0" : 0;

                  object.gameBaseData = null;
                }

                if (message.user != null && message.hasOwnProperty("user")) object.user = $root.proto.UserInfo.toObject(message.user, options);
                if (message.accountToken != null && message.hasOwnProperty("accountToken")) object.accountToken = message.accountToken;
                if (message.banAccountType != null && message.hasOwnProperty("banAccountType")) object.banAccountType = message.banAccountType;
                if (message.banAccountSurplusTime != null && message.hasOwnProperty("banAccountSurplusTime")) if (typeof message.banAccountSurplusTime === "number") object.banAccountSurplusTime = options.longs === String ? String(message.banAccountSurplusTime) : message.banAccountSurplusTime;else object.banAccountSurplusTime = options.longs === String ? $util.Long.prototype.toString.call(message.banAccountSurplusTime) : options.longs === Number ? new $util.LongBits(message.banAccountSurplusTime.low >>> 0, message.banAccountSurplusTime.high >>> 0).toNumber() : message.banAccountSurplusTime;
                if (message.gameBaseData != null && message.hasOwnProperty("gameBaseData")) object.gameBaseData = $root.proto.PlayerInfo.toObject(message.gameBaseData, options);
                return object;
              };
              /**
               * Converts this LOBBY_HD_TRYLOGIN_S2C to JSON.
               * @function toJSON
               * @memberof proto.LOBBY_HD_TRYLOGIN_S2C
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              LOBBY_HD_TRYLOGIN_S2C.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return LOBBY_HD_TRYLOGIN_S2C;
            }();

            proto.GAME_HD_ENTRY_C2S = function () {
              /**
               * Properties of a GAME_HD_ENTRY_C2S.
               * @memberof proto
               * @interface IGAME_HD_ENTRY_C2S
               * @property {string|null} [nickname] GAME_HD_ENTRY_C2S nickname
               * @property {number|null} [roleId] GAME_HD_ENTRY_C2S roleId
               */

              /**
               * Constructs a new GAME_HD_ENTRY_C2S.
               * @memberof proto
               * @classdesc Represents a GAME_HD_ENTRY_C2S.
               * @implements IGAME_HD_ENTRY_C2S
               * @constructor
               * @param {proto.IGAME_HD_ENTRY_C2S=} [properties] Properties to set
               */
              function GAME_HD_ENTRY_C2S(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_ENTRY_C2S nickname.
               * @member {string} nickname
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @instance
               */


              GAME_HD_ENTRY_C2S.prototype.nickname = "";
              /**
               * GAME_HD_ENTRY_C2S roleId.
               * @member {number} roleId
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @instance
               */

              GAME_HD_ENTRY_C2S.prototype.roleId = 0;
              /**
               * Creates a new GAME_HD_ENTRY_C2S instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @static
               * @param {proto.IGAME_HD_ENTRY_C2S=} [properties] Properties to set
               * @returns {proto.GAME_HD_ENTRY_C2S} GAME_HD_ENTRY_C2S instance
               */

              GAME_HD_ENTRY_C2S.create = function create(properties) {
                return new GAME_HD_ENTRY_C2S(properties);
              };
              /**
               * Encodes the specified GAME_HD_ENTRY_C2S message. Does not implicitly {@link proto.GAME_HD_ENTRY_C2S.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @static
               * @param {proto.IGAME_HD_ENTRY_C2S} message GAME_HD_ENTRY_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_ENTRY_C2S.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.nickname != null && message.hasOwnProperty("nickname")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.nickname);
                if (message.roleId != null && message.hasOwnProperty("roleId")) writer.uint32(
                /* id 2, wireType 0 =*/
                16).int32(message.roleId);
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_ENTRY_C2S message, length delimited. Does not implicitly {@link proto.GAME_HD_ENTRY_C2S.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @static
               * @param {proto.IGAME_HD_ENTRY_C2S} message GAME_HD_ENTRY_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_ENTRY_C2S.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_ENTRY_C2S message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_ENTRY_C2S} GAME_HD_ENTRY_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_ENTRY_C2S.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_ENTRY_C2S();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.nickname = reader.string();
                      break;

                    case 2:
                      message.roleId = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_ENTRY_C2S message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_ENTRY_C2S} GAME_HD_ENTRY_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_ENTRY_C2S.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_ENTRY_C2S message.
               * @function verify
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_ENTRY_C2S.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.nickname != null && message.hasOwnProperty("nickname")) if (!$util.isString(message.nickname)) return "nickname: string expected";
                if (message.roleId != null && message.hasOwnProperty("roleId")) if (!$util.isInteger(message.roleId)) return "roleId: integer expected";
                return null;
              };
              /**
               * Creates a GAME_HD_ENTRY_C2S message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_ENTRY_C2S} GAME_HD_ENTRY_C2S
               */


              GAME_HD_ENTRY_C2S.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_ENTRY_C2S) return object;
                var message = new $root.proto.GAME_HD_ENTRY_C2S();
                if (object.nickname != null) message.nickname = String(object.nickname);
                if (object.roleId != null) message.roleId = object.roleId | 0;
                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_ENTRY_C2S message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @static
               * @param {proto.GAME_HD_ENTRY_C2S} message GAME_HD_ENTRY_C2S
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_ENTRY_C2S.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.nickname = "";
                  object.roleId = 0;
                }

                if (message.nickname != null && message.hasOwnProperty("nickname")) object.nickname = message.nickname;
                if (message.roleId != null && message.hasOwnProperty("roleId")) object.roleId = message.roleId;
                return object;
              };
              /**
               * Converts this GAME_HD_ENTRY_C2S to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_ENTRY_C2S
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_ENTRY_C2S.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_ENTRY_C2S;
            }();

            proto.GAME_HD_ENTRY_S2C = function () {
              /**
               * Properties of a GAME_HD_ENTRY_S2C.
               * @memberof proto
               * @interface IGAME_HD_ENTRY_S2C
               * @property {proto.IMapData|null} [mapData] GAME_HD_ENTRY_S2C mapData
               * @property {proto.IGameData|null} [gameData] GAME_HD_ENTRY_S2C gameData
               */

              /**
               * Constructs a new GAME_HD_ENTRY_S2C.
               * @memberof proto
               * @classdesc Represents a GAME_HD_ENTRY_S2C.
               * @implements IGAME_HD_ENTRY_S2C
               * @constructor
               * @param {proto.IGAME_HD_ENTRY_S2C=} [properties] Properties to set
               */
              function GAME_HD_ENTRY_S2C(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_ENTRY_S2C mapData.
               * @member {proto.IMapData|null|undefined} mapData
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @instance
               */


              GAME_HD_ENTRY_S2C.prototype.mapData = null;
              /**
               * GAME_HD_ENTRY_S2C gameData.
               * @member {proto.IGameData|null|undefined} gameData
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @instance
               */

              GAME_HD_ENTRY_S2C.prototype.gameData = null;
              /**
               * Creates a new GAME_HD_ENTRY_S2C instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @static
               * @param {proto.IGAME_HD_ENTRY_S2C=} [properties] Properties to set
               * @returns {proto.GAME_HD_ENTRY_S2C} GAME_HD_ENTRY_S2C instance
               */

              GAME_HD_ENTRY_S2C.create = function create(properties) {
                return new GAME_HD_ENTRY_S2C(properties);
              };
              /**
               * Encodes the specified GAME_HD_ENTRY_S2C message. Does not implicitly {@link proto.GAME_HD_ENTRY_S2C.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @static
               * @param {proto.IGAME_HD_ENTRY_S2C} message GAME_HD_ENTRY_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_ENTRY_S2C.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.mapData != null && message.hasOwnProperty("mapData")) $root.proto.MapData.encode(message.mapData, writer.uint32(
                /* id 1, wireType 2 =*/
                10).fork()).ldelim();
                if (message.gameData != null && message.hasOwnProperty("gameData")) $root.proto.GameData.encode(message.gameData, writer.uint32(
                /* id 2, wireType 2 =*/
                18).fork()).ldelim();
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_ENTRY_S2C message, length delimited. Does not implicitly {@link proto.GAME_HD_ENTRY_S2C.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @static
               * @param {proto.IGAME_HD_ENTRY_S2C} message GAME_HD_ENTRY_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_ENTRY_S2C.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_ENTRY_S2C message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_ENTRY_S2C} GAME_HD_ENTRY_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_ENTRY_S2C.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_ENTRY_S2C();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.mapData = $root.proto.MapData.decode(reader, reader.uint32());
                      break;

                    case 2:
                      message.gameData = $root.proto.GameData.decode(reader, reader.uint32());
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_ENTRY_S2C message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_ENTRY_S2C} GAME_HD_ENTRY_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_ENTRY_S2C.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_ENTRY_S2C message.
               * @function verify
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_ENTRY_S2C.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.mapData != null && message.hasOwnProperty("mapData")) {
                  var error = $root.proto.MapData.verify(message.mapData);
                  if (error) return "mapData." + error;
                }

                if (message.gameData != null && message.hasOwnProperty("gameData")) {
                  var error = $root.proto.GameData.verify(message.gameData);
                  if (error) return "gameData." + error;
                }

                return null;
              };
              /**
               * Creates a GAME_HD_ENTRY_S2C message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_ENTRY_S2C} GAME_HD_ENTRY_S2C
               */


              GAME_HD_ENTRY_S2C.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_ENTRY_S2C) return object;
                var message = new $root.proto.GAME_HD_ENTRY_S2C();

                if (object.mapData != null) {
                  if (typeof object.mapData !== "object") throw TypeError(".proto.GAME_HD_ENTRY_S2C.mapData: object expected");
                  message.mapData = $root.proto.MapData.fromObject(object.mapData);
                }

                if (object.gameData != null) {
                  if (typeof object.gameData !== "object") throw TypeError(".proto.GAME_HD_ENTRY_S2C.gameData: object expected");
                  message.gameData = $root.proto.GameData.fromObject(object.gameData);
                }

                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_ENTRY_S2C message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @static
               * @param {proto.GAME_HD_ENTRY_S2C} message GAME_HD_ENTRY_S2C
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_ENTRY_S2C.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.mapData = null;
                  object.gameData = null;
                }

                if (message.mapData != null && message.hasOwnProperty("mapData")) object.mapData = $root.proto.MapData.toObject(message.mapData, options);
                if (message.gameData != null && message.hasOwnProperty("gameData")) object.gameData = $root.proto.GameData.toObject(message.gameData, options);
                return object;
              };
              /**
               * Converts this GAME_HD_ENTRY_S2C to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_ENTRY_S2C
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_ENTRY_S2C.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_ENTRY_S2C;
            }();

            proto.GAME_HD_SELECTMAPNODE_C2S = function () {
              /**
               * Properties of a GAME_HD_SELECTMAPNODE_C2S.
               * @memberof proto
               * @interface IGAME_HD_SELECTMAPNODE_C2S
               * @property {number|null} [index] GAME_HD_SELECTMAPNODE_C2S index
               */

              /**
               * Constructs a new GAME_HD_SELECTMAPNODE_C2S.
               * @memberof proto
               * @classdesc Represents a GAME_HD_SELECTMAPNODE_C2S.
               * @implements IGAME_HD_SELECTMAPNODE_C2S
               * @constructor
               * @param {proto.IGAME_HD_SELECTMAPNODE_C2S=} [properties] Properties to set
               */
              function GAME_HD_SELECTMAPNODE_C2S(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_SELECTMAPNODE_C2S index.
               * @member {number} index
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @instance
               */


              GAME_HD_SELECTMAPNODE_C2S.prototype.index = 0;
              /**
               * Creates a new GAME_HD_SELECTMAPNODE_C2S instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @static
               * @param {proto.IGAME_HD_SELECTMAPNODE_C2S=} [properties] Properties to set
               * @returns {proto.GAME_HD_SELECTMAPNODE_C2S} GAME_HD_SELECTMAPNODE_C2S instance
               */

              GAME_HD_SELECTMAPNODE_C2S.create = function create(properties) {
                return new GAME_HD_SELECTMAPNODE_C2S(properties);
              };
              /**
               * Encodes the specified GAME_HD_SELECTMAPNODE_C2S message. Does not implicitly {@link proto.GAME_HD_SELECTMAPNODE_C2S.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @static
               * @param {proto.IGAME_HD_SELECTMAPNODE_C2S} message GAME_HD_SELECTMAPNODE_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_SELECTMAPNODE_C2S.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.index != null && message.hasOwnProperty("index")) writer.uint32(
                /* id 1, wireType 0 =*/
                8).int32(message.index);
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_SELECTMAPNODE_C2S message, length delimited. Does not implicitly {@link proto.GAME_HD_SELECTMAPNODE_C2S.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @static
               * @param {proto.IGAME_HD_SELECTMAPNODE_C2S} message GAME_HD_SELECTMAPNODE_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_SELECTMAPNODE_C2S.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_SELECTMAPNODE_C2S message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_SELECTMAPNODE_C2S} GAME_HD_SELECTMAPNODE_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_SELECTMAPNODE_C2S.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_SELECTMAPNODE_C2S();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.index = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_SELECTMAPNODE_C2S message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_SELECTMAPNODE_C2S} GAME_HD_SELECTMAPNODE_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_SELECTMAPNODE_C2S.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_SELECTMAPNODE_C2S message.
               * @function verify
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_SELECTMAPNODE_C2S.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.index != null && message.hasOwnProperty("index")) if (!$util.isInteger(message.index)) return "index: integer expected";
                return null;
              };
              /**
               * Creates a GAME_HD_SELECTMAPNODE_C2S message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_SELECTMAPNODE_C2S} GAME_HD_SELECTMAPNODE_C2S
               */


              GAME_HD_SELECTMAPNODE_C2S.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_SELECTMAPNODE_C2S) return object;
                var message = new $root.proto.GAME_HD_SELECTMAPNODE_C2S();
                if (object.index != null) message.index = object.index | 0;
                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_SELECTMAPNODE_C2S message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @static
               * @param {proto.GAME_HD_SELECTMAPNODE_C2S} message GAME_HD_SELECTMAPNODE_C2S
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_SELECTMAPNODE_C2S.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.defaults) object.index = 0;
                if (message.index != null && message.hasOwnProperty("index")) object.index = message.index;
                return object;
              };
              /**
               * Converts this GAME_HD_SELECTMAPNODE_C2S to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_SELECTMAPNODE_C2S
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_SELECTMAPNODE_C2S.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_SELECTMAPNODE_C2S;
            }();

            proto.GAME_HD_SELECTMAPNODE_S2C = function () {
              /**
               * Properties of a GAME_HD_SELECTMAPNODE_S2C.
               * @memberof proto
               * @interface IGAME_HD_SELECTMAPNODE_S2C
               * @property {proto.IGameData|null} [gameData] GAME_HD_SELECTMAPNODE_S2C gameData
               */

              /**
               * Constructs a new GAME_HD_SELECTMAPNODE_S2C.
               * @memberof proto
               * @classdesc Represents a GAME_HD_SELECTMAPNODE_S2C.
               * @implements IGAME_HD_SELECTMAPNODE_S2C
               * @constructor
               * @param {proto.IGAME_HD_SELECTMAPNODE_S2C=} [properties] Properties to set
               */
              function GAME_HD_SELECTMAPNODE_S2C(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_SELECTMAPNODE_S2C gameData.
               * @member {proto.IGameData|null|undefined} gameData
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @instance
               */


              GAME_HD_SELECTMAPNODE_S2C.prototype.gameData = null;
              /**
               * Creates a new GAME_HD_SELECTMAPNODE_S2C instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @static
               * @param {proto.IGAME_HD_SELECTMAPNODE_S2C=} [properties] Properties to set
               * @returns {proto.GAME_HD_SELECTMAPNODE_S2C} GAME_HD_SELECTMAPNODE_S2C instance
               */

              GAME_HD_SELECTMAPNODE_S2C.create = function create(properties) {
                return new GAME_HD_SELECTMAPNODE_S2C(properties);
              };
              /**
               * Encodes the specified GAME_HD_SELECTMAPNODE_S2C message. Does not implicitly {@link proto.GAME_HD_SELECTMAPNODE_S2C.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @static
               * @param {proto.IGAME_HD_SELECTMAPNODE_S2C} message GAME_HD_SELECTMAPNODE_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_SELECTMAPNODE_S2C.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.gameData != null && message.hasOwnProperty("gameData")) $root.proto.GameData.encode(message.gameData, writer.uint32(
                /* id 1, wireType 2 =*/
                10).fork()).ldelim();
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_SELECTMAPNODE_S2C message, length delimited. Does not implicitly {@link proto.GAME_HD_SELECTMAPNODE_S2C.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @static
               * @param {proto.IGAME_HD_SELECTMAPNODE_S2C} message GAME_HD_SELECTMAPNODE_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_SELECTMAPNODE_S2C.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_SELECTMAPNODE_S2C message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_SELECTMAPNODE_S2C} GAME_HD_SELECTMAPNODE_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_SELECTMAPNODE_S2C.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_SELECTMAPNODE_S2C();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.gameData = $root.proto.GameData.decode(reader, reader.uint32());
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_SELECTMAPNODE_S2C message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_SELECTMAPNODE_S2C} GAME_HD_SELECTMAPNODE_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_SELECTMAPNODE_S2C.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_SELECTMAPNODE_S2C message.
               * @function verify
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_SELECTMAPNODE_S2C.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.gameData != null && message.hasOwnProperty("gameData")) {
                  var error = $root.proto.GameData.verify(message.gameData);
                  if (error) return "gameData." + error;
                }

                return null;
              };
              /**
               * Creates a GAME_HD_SELECTMAPNODE_S2C message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_SELECTMAPNODE_S2C} GAME_HD_SELECTMAPNODE_S2C
               */


              GAME_HD_SELECTMAPNODE_S2C.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_SELECTMAPNODE_S2C) return object;
                var message = new $root.proto.GAME_HD_SELECTMAPNODE_S2C();

                if (object.gameData != null) {
                  if (typeof object.gameData !== "object") throw TypeError(".proto.GAME_HD_SELECTMAPNODE_S2C.gameData: object expected");
                  message.gameData = $root.proto.GameData.fromObject(object.gameData);
                }

                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_SELECTMAPNODE_S2C message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @static
               * @param {proto.GAME_HD_SELECTMAPNODE_S2C} message GAME_HD_SELECTMAPNODE_S2C
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_SELECTMAPNODE_S2C.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.defaults) object.gameData = null;
                if (message.gameData != null && message.hasOwnProperty("gameData")) object.gameData = $root.proto.GameData.toObject(message.gameData, options);
                return object;
              };
              /**
               * Converts this GAME_HD_SELECTMAPNODE_S2C to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_SELECTMAPNODE_S2C
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_SELECTMAPNODE_S2C.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_SELECTMAPNODE_S2C;
            }();

            proto.GAME_HD_BUYHERO_C2S = function () {
              /**
               * Properties of a GAME_HD_BUYHERO_C2S.
               * @memberof proto
               * @interface IGAME_HD_BUYHERO_C2S
               * @property {string|null} [heroUid] GAME_HD_BUYHERO_C2S heroUid
               * @property {number|null} [areaType] GAME_HD_BUYHERO_C2S areaType
               * @property {number|null} [useIndex] GAME_HD_BUYHERO_C2S useIndex
               */

              /**
               * Constructs a new GAME_HD_BUYHERO_C2S.
               * @memberof proto
               * @classdesc Represents a GAME_HD_BUYHERO_C2S.
               * @implements IGAME_HD_BUYHERO_C2S
               * @constructor
               * @param {proto.IGAME_HD_BUYHERO_C2S=} [properties] Properties to set
               */
              function GAME_HD_BUYHERO_C2S(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_BUYHERO_C2S heroUid.
               * @member {string} heroUid
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @instance
               */


              GAME_HD_BUYHERO_C2S.prototype.heroUid = "";
              /**
               * GAME_HD_BUYHERO_C2S areaType.
               * @member {number} areaType
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @instance
               */

              GAME_HD_BUYHERO_C2S.prototype.areaType = 0;
              /**
               * GAME_HD_BUYHERO_C2S useIndex.
               * @member {number} useIndex
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @instance
               */

              GAME_HD_BUYHERO_C2S.prototype.useIndex = 0;
              /**
               * Creates a new GAME_HD_BUYHERO_C2S instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @static
               * @param {proto.IGAME_HD_BUYHERO_C2S=} [properties] Properties to set
               * @returns {proto.GAME_HD_BUYHERO_C2S} GAME_HD_BUYHERO_C2S instance
               */

              GAME_HD_BUYHERO_C2S.create = function create(properties) {
                return new GAME_HD_BUYHERO_C2S(properties);
              };
              /**
               * Encodes the specified GAME_HD_BUYHERO_C2S message. Does not implicitly {@link proto.GAME_HD_BUYHERO_C2S.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @static
               * @param {proto.IGAME_HD_BUYHERO_C2S} message GAME_HD_BUYHERO_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_BUYHERO_C2S.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.heroUid != null && message.hasOwnProperty("heroUid")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.heroUid);
                if (message.areaType != null && message.hasOwnProperty("areaType")) writer.uint32(
                /* id 2, wireType 0 =*/
                16).int32(message.areaType);
                if (message.useIndex != null && message.hasOwnProperty("useIndex")) writer.uint32(
                /* id 3, wireType 0 =*/
                24).int32(message.useIndex);
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_BUYHERO_C2S message, length delimited. Does not implicitly {@link proto.GAME_HD_BUYHERO_C2S.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @static
               * @param {proto.IGAME_HD_BUYHERO_C2S} message GAME_HD_BUYHERO_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_BUYHERO_C2S.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_BUYHERO_C2S message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_BUYHERO_C2S} GAME_HD_BUYHERO_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_BUYHERO_C2S.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_BUYHERO_C2S();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.heroUid = reader.string();
                      break;

                    case 2:
                      message.areaType = reader.int32();
                      break;

                    case 3:
                      message.useIndex = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_BUYHERO_C2S message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_BUYHERO_C2S} GAME_HD_BUYHERO_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_BUYHERO_C2S.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_BUYHERO_C2S message.
               * @function verify
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_BUYHERO_C2S.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.heroUid != null && message.hasOwnProperty("heroUid")) if (!$util.isString(message.heroUid)) return "heroUid: string expected";
                if (message.areaType != null && message.hasOwnProperty("areaType")) if (!$util.isInteger(message.areaType)) return "areaType: integer expected";
                if (message.useIndex != null && message.hasOwnProperty("useIndex")) if (!$util.isInteger(message.useIndex)) return "useIndex: integer expected";
                return null;
              };
              /**
               * Creates a GAME_HD_BUYHERO_C2S message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_BUYHERO_C2S} GAME_HD_BUYHERO_C2S
               */


              GAME_HD_BUYHERO_C2S.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_BUYHERO_C2S) return object;
                var message = new $root.proto.GAME_HD_BUYHERO_C2S();
                if (object.heroUid != null) message.heroUid = String(object.heroUid);
                if (object.areaType != null) message.areaType = object.areaType | 0;
                if (object.useIndex != null) message.useIndex = object.useIndex | 0;
                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_BUYHERO_C2S message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @static
               * @param {proto.GAME_HD_BUYHERO_C2S} message GAME_HD_BUYHERO_C2S
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_BUYHERO_C2S.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.heroUid = "";
                  object.areaType = 0;
                  object.useIndex = 0;
                }

                if (message.heroUid != null && message.hasOwnProperty("heroUid")) object.heroUid = message.heroUid;
                if (message.areaType != null && message.hasOwnProperty("areaType")) object.areaType = message.areaType;
                if (message.useIndex != null && message.hasOwnProperty("useIndex")) object.useIndex = message.useIndex;
                return object;
              };
              /**
               * Converts this GAME_HD_BUYHERO_C2S to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_BUYHERO_C2S
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_BUYHERO_C2S.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_BUYHERO_C2S;
            }();

            proto.GAME_HD_BUYHERO_S2C = function () {
              /**
               * Properties of a GAME_HD_BUYHERO_S2C.
               * @memberof proto
               * @interface IGAME_HD_BUYHERO_S2C
               * @property {proto.IGameData|null} [gameData] GAME_HD_BUYHERO_S2C gameData
               */

              /**
               * Constructs a new GAME_HD_BUYHERO_S2C.
               * @memberof proto
               * @classdesc Represents a GAME_HD_BUYHERO_S2C.
               * @implements IGAME_HD_BUYHERO_S2C
               * @constructor
               * @param {proto.IGAME_HD_BUYHERO_S2C=} [properties] Properties to set
               */
              function GAME_HD_BUYHERO_S2C(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_BUYHERO_S2C gameData.
               * @member {proto.IGameData|null|undefined} gameData
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @instance
               */


              GAME_HD_BUYHERO_S2C.prototype.gameData = null;
              /**
               * Creates a new GAME_HD_BUYHERO_S2C instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @static
               * @param {proto.IGAME_HD_BUYHERO_S2C=} [properties] Properties to set
               * @returns {proto.GAME_HD_BUYHERO_S2C} GAME_HD_BUYHERO_S2C instance
               */

              GAME_HD_BUYHERO_S2C.create = function create(properties) {
                return new GAME_HD_BUYHERO_S2C(properties);
              };
              /**
               * Encodes the specified GAME_HD_BUYHERO_S2C message. Does not implicitly {@link proto.GAME_HD_BUYHERO_S2C.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @static
               * @param {proto.IGAME_HD_BUYHERO_S2C} message GAME_HD_BUYHERO_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_BUYHERO_S2C.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.gameData != null && message.hasOwnProperty("gameData")) $root.proto.GameData.encode(message.gameData, writer.uint32(
                /* id 1, wireType 2 =*/
                10).fork()).ldelim();
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_BUYHERO_S2C message, length delimited. Does not implicitly {@link proto.GAME_HD_BUYHERO_S2C.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @static
               * @param {proto.IGAME_HD_BUYHERO_S2C} message GAME_HD_BUYHERO_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_BUYHERO_S2C.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_BUYHERO_S2C message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_BUYHERO_S2C} GAME_HD_BUYHERO_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_BUYHERO_S2C.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_BUYHERO_S2C();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.gameData = $root.proto.GameData.decode(reader, reader.uint32());
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_BUYHERO_S2C message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_BUYHERO_S2C} GAME_HD_BUYHERO_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_BUYHERO_S2C.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_BUYHERO_S2C message.
               * @function verify
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_BUYHERO_S2C.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.gameData != null && message.hasOwnProperty("gameData")) {
                  var error = $root.proto.GameData.verify(message.gameData);
                  if (error) return "gameData." + error;
                }

                return null;
              };
              /**
               * Creates a GAME_HD_BUYHERO_S2C message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_BUYHERO_S2C} GAME_HD_BUYHERO_S2C
               */


              GAME_HD_BUYHERO_S2C.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_BUYHERO_S2C) return object;
                var message = new $root.proto.GAME_HD_BUYHERO_S2C();

                if (object.gameData != null) {
                  if (typeof object.gameData !== "object") throw TypeError(".proto.GAME_HD_BUYHERO_S2C.gameData: object expected");
                  message.gameData = $root.proto.GameData.fromObject(object.gameData);
                }

                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_BUYHERO_S2C message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @static
               * @param {proto.GAME_HD_BUYHERO_S2C} message GAME_HD_BUYHERO_S2C
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_BUYHERO_S2C.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.defaults) object.gameData = null;
                if (message.gameData != null && message.hasOwnProperty("gameData")) object.gameData = $root.proto.GameData.toObject(message.gameData, options);
                return object;
              };
              /**
               * Converts this GAME_HD_BUYHERO_S2C to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_BUYHERO_S2C
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_BUYHERO_S2C.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_BUYHERO_S2C;
            }();

            proto.GAME_HD_SELLHERO_C2S = function () {
              /**
               * Properties of a GAME_HD_SELLHERO_C2S.
               * @memberof proto
               * @interface IGAME_HD_SELLHERO_C2S
               * @property {string|null} [heroUid] GAME_HD_SELLHERO_C2S heroUid
               */

              /**
               * Constructs a new GAME_HD_SELLHERO_C2S.
               * @memberof proto
               * @classdesc Represents a GAME_HD_SELLHERO_C2S.
               * @implements IGAME_HD_SELLHERO_C2S
               * @constructor
               * @param {proto.IGAME_HD_SELLHERO_C2S=} [properties] Properties to set
               */
              function GAME_HD_SELLHERO_C2S(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_SELLHERO_C2S heroUid.
               * @member {string} heroUid
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @instance
               */


              GAME_HD_SELLHERO_C2S.prototype.heroUid = "";
              /**
               * Creates a new GAME_HD_SELLHERO_C2S instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @static
               * @param {proto.IGAME_HD_SELLHERO_C2S=} [properties] Properties to set
               * @returns {proto.GAME_HD_SELLHERO_C2S} GAME_HD_SELLHERO_C2S instance
               */

              GAME_HD_SELLHERO_C2S.create = function create(properties) {
                return new GAME_HD_SELLHERO_C2S(properties);
              };
              /**
               * Encodes the specified GAME_HD_SELLHERO_C2S message. Does not implicitly {@link proto.GAME_HD_SELLHERO_C2S.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @static
               * @param {proto.IGAME_HD_SELLHERO_C2S} message GAME_HD_SELLHERO_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_SELLHERO_C2S.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.heroUid != null && message.hasOwnProperty("heroUid")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.heroUid);
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_SELLHERO_C2S message, length delimited. Does not implicitly {@link proto.GAME_HD_SELLHERO_C2S.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @static
               * @param {proto.IGAME_HD_SELLHERO_C2S} message GAME_HD_SELLHERO_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_SELLHERO_C2S.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_SELLHERO_C2S message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_SELLHERO_C2S} GAME_HD_SELLHERO_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_SELLHERO_C2S.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_SELLHERO_C2S();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.heroUid = reader.string();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_SELLHERO_C2S message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_SELLHERO_C2S} GAME_HD_SELLHERO_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_SELLHERO_C2S.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_SELLHERO_C2S message.
               * @function verify
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_SELLHERO_C2S.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.heroUid != null && message.hasOwnProperty("heroUid")) if (!$util.isString(message.heroUid)) return "heroUid: string expected";
                return null;
              };
              /**
               * Creates a GAME_HD_SELLHERO_C2S message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_SELLHERO_C2S} GAME_HD_SELLHERO_C2S
               */


              GAME_HD_SELLHERO_C2S.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_SELLHERO_C2S) return object;
                var message = new $root.proto.GAME_HD_SELLHERO_C2S();
                if (object.heroUid != null) message.heroUid = String(object.heroUid);
                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_SELLHERO_C2S message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @static
               * @param {proto.GAME_HD_SELLHERO_C2S} message GAME_HD_SELLHERO_C2S
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_SELLHERO_C2S.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.defaults) object.heroUid = "";
                if (message.heroUid != null && message.hasOwnProperty("heroUid")) object.heroUid = message.heroUid;
                return object;
              };
              /**
               * Converts this GAME_HD_SELLHERO_C2S to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_SELLHERO_C2S
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_SELLHERO_C2S.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_SELLHERO_C2S;
            }();

            proto.GAME_HD_SELLHERO_S2C = function () {
              /**
               * Properties of a GAME_HD_SELLHERO_S2C.
               * @memberof proto
               * @interface IGAME_HD_SELLHERO_S2C
               * @property {Object.<string,proto.IHero>|null} [battleAreas] GAME_HD_SELLHERO_S2C battleAreas
               * @property {Object.<string,proto.IHero>|null} [prepareAreas] GAME_HD_SELLHERO_S2C prepareAreas
               * @property {number|null} [gold] GAME_HD_SELLHERO_S2C gold
               */

              /**
               * Constructs a new GAME_HD_SELLHERO_S2C.
               * @memberof proto
               * @classdesc Represents a GAME_HD_SELLHERO_S2C.
               * @implements IGAME_HD_SELLHERO_S2C
               * @constructor
               * @param {proto.IGAME_HD_SELLHERO_S2C=} [properties] Properties to set
               */
              function GAME_HD_SELLHERO_S2C(properties) {
                this.battleAreas = {};
                this.prepareAreas = {};
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_SELLHERO_S2C battleAreas.
               * @member {Object.<string,proto.IHero>} battleAreas
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @instance
               */


              GAME_HD_SELLHERO_S2C.prototype.battleAreas = $util.emptyObject;
              /**
               * GAME_HD_SELLHERO_S2C prepareAreas.
               * @member {Object.<string,proto.IHero>} prepareAreas
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @instance
               */

              GAME_HD_SELLHERO_S2C.prototype.prepareAreas = $util.emptyObject;
              /**
               * GAME_HD_SELLHERO_S2C gold.
               * @member {number} gold
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @instance
               */

              GAME_HD_SELLHERO_S2C.prototype.gold = 0;
              /**
               * Creates a new GAME_HD_SELLHERO_S2C instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @static
               * @param {proto.IGAME_HD_SELLHERO_S2C=} [properties] Properties to set
               * @returns {proto.GAME_HD_SELLHERO_S2C} GAME_HD_SELLHERO_S2C instance
               */

              GAME_HD_SELLHERO_S2C.create = function create(properties) {
                return new GAME_HD_SELLHERO_S2C(properties);
              };
              /**
               * Encodes the specified GAME_HD_SELLHERO_S2C message. Does not implicitly {@link proto.GAME_HD_SELLHERO_S2C.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @static
               * @param {proto.IGAME_HD_SELLHERO_S2C} message GAME_HD_SELLHERO_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_SELLHERO_S2C.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.battleAreas != null && message.hasOwnProperty("battleAreas")) for (var keys = Object.keys(message.battleAreas), i = 0; i < keys.length; ++i) {
                  writer.uint32(
                  /* id 1, wireType 2 =*/
                  10).fork().uint32(
                  /* id 1, wireType 0 =*/
                  8).int32(keys[i]);
                  $root.proto.Hero.encode(message.battleAreas[keys[i]], writer.uint32(
                  /* id 2, wireType 2 =*/
                  18).fork()).ldelim().ldelim();
                }
                if (message.prepareAreas != null && message.hasOwnProperty("prepareAreas")) for (var keys = Object.keys(message.prepareAreas), i = 0; i < keys.length; ++i) {
                  writer.uint32(
                  /* id 2, wireType 2 =*/
                  18).fork().uint32(
                  /* id 1, wireType 0 =*/
                  8).int32(keys[i]);
                  $root.proto.Hero.encode(message.prepareAreas[keys[i]], writer.uint32(
                  /* id 2, wireType 2 =*/
                  18).fork()).ldelim().ldelim();
                }
                if (message.gold != null && message.hasOwnProperty("gold")) writer.uint32(
                /* id 3, wireType 0 =*/
                24).int32(message.gold);
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_SELLHERO_S2C message, length delimited. Does not implicitly {@link proto.GAME_HD_SELLHERO_S2C.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @static
               * @param {proto.IGAME_HD_SELLHERO_S2C} message GAME_HD_SELLHERO_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_SELLHERO_S2C.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_SELLHERO_S2C message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_SELLHERO_S2C} GAME_HD_SELLHERO_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_SELLHERO_S2C.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_SELLHERO_S2C(),
                    key;

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      reader.skip().pos++;
                      if (message.battleAreas === $util.emptyObject) message.battleAreas = {};
                      key = reader.int32();
                      reader.pos++;
                      message.battleAreas[key] = $root.proto.Hero.decode(reader, reader.uint32());
                      break;

                    case 2:
                      reader.skip().pos++;
                      if (message.prepareAreas === $util.emptyObject) message.prepareAreas = {};
                      key = reader.int32();
                      reader.pos++;
                      message.prepareAreas[key] = $root.proto.Hero.decode(reader, reader.uint32());
                      break;

                    case 3:
                      message.gold = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_SELLHERO_S2C message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_SELLHERO_S2C} GAME_HD_SELLHERO_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_SELLHERO_S2C.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_SELLHERO_S2C message.
               * @function verify
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_SELLHERO_S2C.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.battleAreas != null && message.hasOwnProperty("battleAreas")) {
                  if (!$util.isObject(message.battleAreas)) return "battleAreas: object expected";
                  var key = Object.keys(message.battleAreas);

                  for (var i = 0; i < key.length; ++i) {
                    if (!$util.key32Re.test(key[i])) return "battleAreas: integer key{k:int32} expected";
                    {
                      var error = $root.proto.Hero.verify(message.battleAreas[key[i]]);
                      if (error) return "battleAreas." + error;
                    }
                  }
                }

                if (message.prepareAreas != null && message.hasOwnProperty("prepareAreas")) {
                  if (!$util.isObject(message.prepareAreas)) return "prepareAreas: object expected";
                  var key = Object.keys(message.prepareAreas);

                  for (var i = 0; i < key.length; ++i) {
                    if (!$util.key32Re.test(key[i])) return "prepareAreas: integer key{k:int32} expected";
                    {
                      var error = $root.proto.Hero.verify(message.prepareAreas[key[i]]);
                      if (error) return "prepareAreas." + error;
                    }
                  }
                }

                if (message.gold != null && message.hasOwnProperty("gold")) if (!$util.isInteger(message.gold)) return "gold: integer expected";
                return null;
              };
              /**
               * Creates a GAME_HD_SELLHERO_S2C message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_SELLHERO_S2C} GAME_HD_SELLHERO_S2C
               */


              GAME_HD_SELLHERO_S2C.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_SELLHERO_S2C) return object;
                var message = new $root.proto.GAME_HD_SELLHERO_S2C();

                if (object.battleAreas) {
                  if (typeof object.battleAreas !== "object") throw TypeError(".proto.GAME_HD_SELLHERO_S2C.battleAreas: object expected");
                  message.battleAreas = {};

                  for (var keys = Object.keys(object.battleAreas), i = 0; i < keys.length; ++i) {
                    if (typeof object.battleAreas[keys[i]] !== "object") throw TypeError(".proto.GAME_HD_SELLHERO_S2C.battleAreas: object expected");
                    message.battleAreas[keys[i]] = $root.proto.Hero.fromObject(object.battleAreas[keys[i]]);
                  }
                }

                if (object.prepareAreas) {
                  if (typeof object.prepareAreas !== "object") throw TypeError(".proto.GAME_HD_SELLHERO_S2C.prepareAreas: object expected");
                  message.prepareAreas = {};

                  for (var keys = Object.keys(object.prepareAreas), i = 0; i < keys.length; ++i) {
                    if (typeof object.prepareAreas[keys[i]] !== "object") throw TypeError(".proto.GAME_HD_SELLHERO_S2C.prepareAreas: object expected");
                    message.prepareAreas[keys[i]] = $root.proto.Hero.fromObject(object.prepareAreas[keys[i]]);
                  }
                }

                if (object.gold != null) message.gold = object.gold | 0;
                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_SELLHERO_S2C message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @static
               * @param {proto.GAME_HD_SELLHERO_S2C} message GAME_HD_SELLHERO_S2C
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_SELLHERO_S2C.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.objects || options.defaults) {
                  object.battleAreas = {};
                  object.prepareAreas = {};
                }

                if (options.defaults) object.gold = 0;
                var keys2;

                if (message.battleAreas && (keys2 = Object.keys(message.battleAreas)).length) {
                  object.battleAreas = {};

                  for (var j = 0; j < keys2.length; ++j) object.battleAreas[keys2[j]] = $root.proto.Hero.toObject(message.battleAreas[keys2[j]], options);
                }

                if (message.prepareAreas && (keys2 = Object.keys(message.prepareAreas)).length) {
                  object.prepareAreas = {};

                  for (var j = 0; j < keys2.length; ++j) object.prepareAreas[keys2[j]] = $root.proto.Hero.toObject(message.prepareAreas[keys2[j]], options);
                }

                if (message.gold != null && message.hasOwnProperty("gold")) object.gold = message.gold;
                return object;
              };
              /**
               * Converts this GAME_HD_SELLHERO_S2C to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_SELLHERO_S2C
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_SELLHERO_S2C.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_SELLHERO_S2C;
            }();

            proto.GAME_HD_MOVEHERO_C2S = function () {
              /**
               * Properties of a GAME_HD_MOVEHERO_C2S.
               * @memberof proto
               * @interface IGAME_HD_MOVEHERO_C2S
               * @property {string|null} [heroUid] GAME_HD_MOVEHERO_C2S heroUid
               * @property {number|null} [areaType] GAME_HD_MOVEHERO_C2S areaType
               * @property {number|null} [useIndex] GAME_HD_MOVEHERO_C2S useIndex
               */

              /**
               * Constructs a new GAME_HD_MOVEHERO_C2S.
               * @memberof proto
               * @classdesc Represents a GAME_HD_MOVEHERO_C2S.
               * @implements IGAME_HD_MOVEHERO_C2S
               * @constructor
               * @param {proto.IGAME_HD_MOVEHERO_C2S=} [properties] Properties to set
               */
              function GAME_HD_MOVEHERO_C2S(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_MOVEHERO_C2S heroUid.
               * @member {string} heroUid
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @instance
               */


              GAME_HD_MOVEHERO_C2S.prototype.heroUid = "";
              /**
               * GAME_HD_MOVEHERO_C2S areaType.
               * @member {number} areaType
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @instance
               */

              GAME_HD_MOVEHERO_C2S.prototype.areaType = 0;
              /**
               * GAME_HD_MOVEHERO_C2S useIndex.
               * @member {number} useIndex
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @instance
               */

              GAME_HD_MOVEHERO_C2S.prototype.useIndex = 0;
              /**
               * Creates a new GAME_HD_MOVEHERO_C2S instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @static
               * @param {proto.IGAME_HD_MOVEHERO_C2S=} [properties] Properties to set
               * @returns {proto.GAME_HD_MOVEHERO_C2S} GAME_HD_MOVEHERO_C2S instance
               */

              GAME_HD_MOVEHERO_C2S.create = function create(properties) {
                return new GAME_HD_MOVEHERO_C2S(properties);
              };
              /**
               * Encodes the specified GAME_HD_MOVEHERO_C2S message. Does not implicitly {@link proto.GAME_HD_MOVEHERO_C2S.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @static
               * @param {proto.IGAME_HD_MOVEHERO_C2S} message GAME_HD_MOVEHERO_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_MOVEHERO_C2S.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.heroUid != null && message.hasOwnProperty("heroUid")) writer.uint32(
                /* id 1, wireType 2 =*/
                10).string(message.heroUid);
                if (message.areaType != null && message.hasOwnProperty("areaType")) writer.uint32(
                /* id 2, wireType 0 =*/
                16).int32(message.areaType);
                if (message.useIndex != null && message.hasOwnProperty("useIndex")) writer.uint32(
                /* id 3, wireType 0 =*/
                24).int32(message.useIndex);
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_MOVEHERO_C2S message, length delimited. Does not implicitly {@link proto.GAME_HD_MOVEHERO_C2S.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @static
               * @param {proto.IGAME_HD_MOVEHERO_C2S} message GAME_HD_MOVEHERO_C2S message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_MOVEHERO_C2S.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_MOVEHERO_C2S message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_MOVEHERO_C2S} GAME_HD_MOVEHERO_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_MOVEHERO_C2S.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_MOVEHERO_C2S();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.heroUid = reader.string();
                      break;

                    case 2:
                      message.areaType = reader.int32();
                      break;

                    case 3:
                      message.useIndex = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_MOVEHERO_C2S message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_MOVEHERO_C2S} GAME_HD_MOVEHERO_C2S
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_MOVEHERO_C2S.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_MOVEHERO_C2S message.
               * @function verify
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_MOVEHERO_C2S.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.heroUid != null && message.hasOwnProperty("heroUid")) if (!$util.isString(message.heroUid)) return "heroUid: string expected";
                if (message.areaType != null && message.hasOwnProperty("areaType")) if (!$util.isInteger(message.areaType)) return "areaType: integer expected";
                if (message.useIndex != null && message.hasOwnProperty("useIndex")) if (!$util.isInteger(message.useIndex)) return "useIndex: integer expected";
                return null;
              };
              /**
               * Creates a GAME_HD_MOVEHERO_C2S message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_MOVEHERO_C2S} GAME_HD_MOVEHERO_C2S
               */


              GAME_HD_MOVEHERO_C2S.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_MOVEHERO_C2S) return object;
                var message = new $root.proto.GAME_HD_MOVEHERO_C2S();
                if (object.heroUid != null) message.heroUid = String(object.heroUid);
                if (object.areaType != null) message.areaType = object.areaType | 0;
                if (object.useIndex != null) message.useIndex = object.useIndex | 0;
                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_MOVEHERO_C2S message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @static
               * @param {proto.GAME_HD_MOVEHERO_C2S} message GAME_HD_MOVEHERO_C2S
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_MOVEHERO_C2S.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.heroUid = "";
                  object.areaType = 0;
                  object.useIndex = 0;
                }

                if (message.heroUid != null && message.hasOwnProperty("heroUid")) object.heroUid = message.heroUid;
                if (message.areaType != null && message.hasOwnProperty("areaType")) object.areaType = message.areaType;
                if (message.useIndex != null && message.hasOwnProperty("useIndex")) object.useIndex = message.useIndex;
                return object;
              };
              /**
               * Converts this GAME_HD_MOVEHERO_C2S to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_MOVEHERO_C2S
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_MOVEHERO_C2S.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_MOVEHERO_C2S;
            }();

            proto.GAME_HD_MOVEHERO_S2C = function () {
              /**
               * Properties of a GAME_HD_MOVEHERO_S2C.
               * @memberof proto
               * @interface IGAME_HD_MOVEHERO_S2C
               * @property {Object.<string,proto.IHero>|null} [battleAreas] GAME_HD_MOVEHERO_S2C battleAreas
               * @property {Object.<string,proto.IHero>|null} [prepareAreas] GAME_HD_MOVEHERO_S2C prepareAreas
               */

              /**
               * Constructs a new GAME_HD_MOVEHERO_S2C.
               * @memberof proto
               * @classdesc Represents a GAME_HD_MOVEHERO_S2C.
               * @implements IGAME_HD_MOVEHERO_S2C
               * @constructor
               * @param {proto.IGAME_HD_MOVEHERO_S2C=} [properties] Properties to set
               */
              function GAME_HD_MOVEHERO_S2C(properties) {
                this.battleAreas = {};
                this.prepareAreas = {};
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_HD_MOVEHERO_S2C battleAreas.
               * @member {Object.<string,proto.IHero>} battleAreas
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @instance
               */


              GAME_HD_MOVEHERO_S2C.prototype.battleAreas = $util.emptyObject;
              /**
               * GAME_HD_MOVEHERO_S2C prepareAreas.
               * @member {Object.<string,proto.IHero>} prepareAreas
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @instance
               */

              GAME_HD_MOVEHERO_S2C.prototype.prepareAreas = $util.emptyObject;
              /**
               * Creates a new GAME_HD_MOVEHERO_S2C instance using the specified properties.
               * @function create
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @static
               * @param {proto.IGAME_HD_MOVEHERO_S2C=} [properties] Properties to set
               * @returns {proto.GAME_HD_MOVEHERO_S2C} GAME_HD_MOVEHERO_S2C instance
               */

              GAME_HD_MOVEHERO_S2C.create = function create(properties) {
                return new GAME_HD_MOVEHERO_S2C(properties);
              };
              /**
               * Encodes the specified GAME_HD_MOVEHERO_S2C message. Does not implicitly {@link proto.GAME_HD_MOVEHERO_S2C.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @static
               * @param {proto.IGAME_HD_MOVEHERO_S2C} message GAME_HD_MOVEHERO_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_MOVEHERO_S2C.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.battleAreas != null && message.hasOwnProperty("battleAreas")) for (var keys = Object.keys(message.battleAreas), i = 0; i < keys.length; ++i) {
                  writer.uint32(
                  /* id 1, wireType 2 =*/
                  10).fork().uint32(
                  /* id 1, wireType 0 =*/
                  8).int32(keys[i]);
                  $root.proto.Hero.encode(message.battleAreas[keys[i]], writer.uint32(
                  /* id 2, wireType 2 =*/
                  18).fork()).ldelim().ldelim();
                }
                if (message.prepareAreas != null && message.hasOwnProperty("prepareAreas")) for (var keys = Object.keys(message.prepareAreas), i = 0; i < keys.length; ++i) {
                  writer.uint32(
                  /* id 2, wireType 2 =*/
                  18).fork().uint32(
                  /* id 1, wireType 0 =*/
                  8).int32(keys[i]);
                  $root.proto.Hero.encode(message.prepareAreas[keys[i]], writer.uint32(
                  /* id 2, wireType 2 =*/
                  18).fork()).ldelim().ldelim();
                }
                return writer;
              };
              /**
               * Encodes the specified GAME_HD_MOVEHERO_S2C message, length delimited. Does not implicitly {@link proto.GAME_HD_MOVEHERO_S2C.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @static
               * @param {proto.IGAME_HD_MOVEHERO_S2C} message GAME_HD_MOVEHERO_S2C message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_HD_MOVEHERO_S2C.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_HD_MOVEHERO_S2C message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_HD_MOVEHERO_S2C} GAME_HD_MOVEHERO_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_MOVEHERO_S2C.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_HD_MOVEHERO_S2C(),
                    key;

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      reader.skip().pos++;
                      if (message.battleAreas === $util.emptyObject) message.battleAreas = {};
                      key = reader.int32();
                      reader.pos++;
                      message.battleAreas[key] = $root.proto.Hero.decode(reader, reader.uint32());
                      break;

                    case 2:
                      reader.skip().pos++;
                      if (message.prepareAreas === $util.emptyObject) message.prepareAreas = {};
                      key = reader.int32();
                      reader.pos++;
                      message.prepareAreas[key] = $root.proto.Hero.decode(reader, reader.uint32());
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_HD_MOVEHERO_S2C message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_HD_MOVEHERO_S2C} GAME_HD_MOVEHERO_S2C
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_HD_MOVEHERO_S2C.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_HD_MOVEHERO_S2C message.
               * @function verify
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_HD_MOVEHERO_S2C.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.battleAreas != null && message.hasOwnProperty("battleAreas")) {
                  if (!$util.isObject(message.battleAreas)) return "battleAreas: object expected";
                  var key = Object.keys(message.battleAreas);

                  for (var i = 0; i < key.length; ++i) {
                    if (!$util.key32Re.test(key[i])) return "battleAreas: integer key{k:int32} expected";
                    {
                      var error = $root.proto.Hero.verify(message.battleAreas[key[i]]);
                      if (error) return "battleAreas." + error;
                    }
                  }
                }

                if (message.prepareAreas != null && message.hasOwnProperty("prepareAreas")) {
                  if (!$util.isObject(message.prepareAreas)) return "prepareAreas: object expected";
                  var key = Object.keys(message.prepareAreas);

                  for (var i = 0; i < key.length; ++i) {
                    if (!$util.key32Re.test(key[i])) return "prepareAreas: integer key{k:int32} expected";
                    {
                      var error = $root.proto.Hero.verify(message.prepareAreas[key[i]]);
                      if (error) return "prepareAreas." + error;
                    }
                  }
                }

                return null;
              };
              /**
               * Creates a GAME_HD_MOVEHERO_S2C message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_HD_MOVEHERO_S2C} GAME_HD_MOVEHERO_S2C
               */


              GAME_HD_MOVEHERO_S2C.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_HD_MOVEHERO_S2C) return object;
                var message = new $root.proto.GAME_HD_MOVEHERO_S2C();

                if (object.battleAreas) {
                  if (typeof object.battleAreas !== "object") throw TypeError(".proto.GAME_HD_MOVEHERO_S2C.battleAreas: object expected");
                  message.battleAreas = {};

                  for (var keys = Object.keys(object.battleAreas), i = 0; i < keys.length; ++i) {
                    if (typeof object.battleAreas[keys[i]] !== "object") throw TypeError(".proto.GAME_HD_MOVEHERO_S2C.battleAreas: object expected");
                    message.battleAreas[keys[i]] = $root.proto.Hero.fromObject(object.battleAreas[keys[i]]);
                  }
                }

                if (object.prepareAreas) {
                  if (typeof object.prepareAreas !== "object") throw TypeError(".proto.GAME_HD_MOVEHERO_S2C.prepareAreas: object expected");
                  message.prepareAreas = {};

                  for (var keys = Object.keys(object.prepareAreas), i = 0; i < keys.length; ++i) {
                    if (typeof object.prepareAreas[keys[i]] !== "object") throw TypeError(".proto.GAME_HD_MOVEHERO_S2C.prepareAreas: object expected");
                    message.prepareAreas[keys[i]] = $root.proto.Hero.fromObject(object.prepareAreas[keys[i]]);
                  }
                }

                return message;
              };
              /**
               * Creates a plain object from a GAME_HD_MOVEHERO_S2C message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @static
               * @param {proto.GAME_HD_MOVEHERO_S2C} message GAME_HD_MOVEHERO_S2C
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_HD_MOVEHERO_S2C.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.objects || options.defaults) {
                  object.battleAreas = {};
                  object.prepareAreas = {};
                }

                var keys2;

                if (message.battleAreas && (keys2 = Object.keys(message.battleAreas)).length) {
                  object.battleAreas = {};

                  for (var j = 0; j < keys2.length; ++j) object.battleAreas[keys2[j]] = $root.proto.Hero.toObject(message.battleAreas[keys2[j]], options);
                }

                if (message.prepareAreas && (keys2 = Object.keys(message.prepareAreas)).length) {
                  object.prepareAreas = {};

                  for (var j = 0; j < keys2.length; ++j) object.prepareAreas[keys2[j]] = $root.proto.Hero.toObject(message.prepareAreas[keys2[j]], options);
                }

                return object;
              };
              /**
               * Converts this GAME_HD_MOVEHERO_S2C to JSON.
               * @function toJSON
               * @memberof proto.GAME_HD_MOVEHERO_S2C
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_HD_MOVEHERO_S2C.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_HD_MOVEHERO_S2C;
            }();

            proto.LOBBY_ONUPDATEUSERINFO_NOTIFY = function () {
              /**
               * Properties of a LOBBY_ONUPDATEUSERINFO_NOTIFY.
               * @memberof proto
               * @interface ILOBBY_ONUPDATEUSERINFO_NOTIFY
               * @property {Array.<proto.IOnUpdatePlayerInfoNotify>|null} [list] LOBBY_ONUPDATEUSERINFO_NOTIFY list
               */

              /**
               * Constructs a new LOBBY_ONUPDATEUSERINFO_NOTIFY.
               * @memberof proto
               * @classdesc Represents a LOBBY_ONUPDATEUSERINFO_NOTIFY.
               * @implements ILOBBY_ONUPDATEUSERINFO_NOTIFY
               * @constructor
               * @param {proto.ILOBBY_ONUPDATEUSERINFO_NOTIFY=} [properties] Properties to set
               */
              function LOBBY_ONUPDATEUSERINFO_NOTIFY(properties) {
                this.list = [];
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * LOBBY_ONUPDATEUSERINFO_NOTIFY list.
               * @member {Array.<proto.IOnUpdatePlayerInfoNotify>} list
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @instance
               */


              LOBBY_ONUPDATEUSERINFO_NOTIFY.prototype.list = $util.emptyArray;
              /**
               * Creates a new LOBBY_ONUPDATEUSERINFO_NOTIFY instance using the specified properties.
               * @function create
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @static
               * @param {proto.ILOBBY_ONUPDATEUSERINFO_NOTIFY=} [properties] Properties to set
               * @returns {proto.LOBBY_ONUPDATEUSERINFO_NOTIFY} LOBBY_ONUPDATEUSERINFO_NOTIFY instance
               */

              LOBBY_ONUPDATEUSERINFO_NOTIFY.create = function create(properties) {
                return new LOBBY_ONUPDATEUSERINFO_NOTIFY(properties);
              };
              /**
               * Encodes the specified LOBBY_ONUPDATEUSERINFO_NOTIFY message. Does not implicitly {@link proto.LOBBY_ONUPDATEUSERINFO_NOTIFY.verify|verify} messages.
               * @function encode
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @static
               * @param {proto.ILOBBY_ONUPDATEUSERINFO_NOTIFY} message LOBBY_ONUPDATEUSERINFO_NOTIFY message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOBBY_ONUPDATEUSERINFO_NOTIFY.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.list != null && message.list.length) for (var i = 0; i < message.list.length; ++i) $root.proto.OnUpdatePlayerInfoNotify.encode(message.list[i], writer.uint32(
                /* id 1, wireType 2 =*/
                10).fork()).ldelim();
                return writer;
              };
              /**
               * Encodes the specified LOBBY_ONUPDATEUSERINFO_NOTIFY message, length delimited. Does not implicitly {@link proto.LOBBY_ONUPDATEUSERINFO_NOTIFY.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @static
               * @param {proto.ILOBBY_ONUPDATEUSERINFO_NOTIFY} message LOBBY_ONUPDATEUSERINFO_NOTIFY message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              LOBBY_ONUPDATEUSERINFO_NOTIFY.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a LOBBY_ONUPDATEUSERINFO_NOTIFY message from the specified reader or buffer.
               * @function decode
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.LOBBY_ONUPDATEUSERINFO_NOTIFY} LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOBBY_ONUPDATEUSERINFO_NOTIFY.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.LOBBY_ONUPDATEUSERINFO_NOTIFY();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      if (!(message.list && message.list.length)) message.list = [];
                      message.list.push($root.proto.OnUpdatePlayerInfoNotify.decode(reader, reader.uint32()));
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a LOBBY_ONUPDATEUSERINFO_NOTIFY message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.LOBBY_ONUPDATEUSERINFO_NOTIFY} LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              LOBBY_ONUPDATEUSERINFO_NOTIFY.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a LOBBY_ONUPDATEUSERINFO_NOTIFY message.
               * @function verify
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              LOBBY_ONUPDATEUSERINFO_NOTIFY.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";

                if (message.list != null && message.hasOwnProperty("list")) {
                  if (!Array.isArray(message.list)) return "list: array expected";

                  for (var i = 0; i < message.list.length; ++i) {
                    var error = $root.proto.OnUpdatePlayerInfoNotify.verify(message.list[i]);
                    if (error) return "list." + error;
                  }
                }

                return null;
              };
              /**
               * Creates a LOBBY_ONUPDATEUSERINFO_NOTIFY message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.LOBBY_ONUPDATEUSERINFO_NOTIFY} LOBBY_ONUPDATEUSERINFO_NOTIFY
               */


              LOBBY_ONUPDATEUSERINFO_NOTIFY.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.LOBBY_ONUPDATEUSERINFO_NOTIFY) return object;
                var message = new $root.proto.LOBBY_ONUPDATEUSERINFO_NOTIFY();

                if (object.list) {
                  if (!Array.isArray(object.list)) throw TypeError(".proto.LOBBY_ONUPDATEUSERINFO_NOTIFY.list: array expected");
                  message.list = [];

                  for (var i = 0; i < object.list.length; ++i) {
                    if (typeof object.list[i] !== "object") throw TypeError(".proto.LOBBY_ONUPDATEUSERINFO_NOTIFY.list: object expected");
                    message.list[i] = $root.proto.OnUpdatePlayerInfoNotify.fromObject(object.list[i]);
                  }
                }

                return message;
              };
              /**
               * Creates a plain object from a LOBBY_ONUPDATEUSERINFO_NOTIFY message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @static
               * @param {proto.LOBBY_ONUPDATEUSERINFO_NOTIFY} message LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              LOBBY_ONUPDATEUSERINFO_NOTIFY.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.arrays || options.defaults) object.list = [];

                if (message.list && message.list.length) {
                  object.list = [];

                  for (var j = 0; j < message.list.length; ++j) object.list[j] = $root.proto.OnUpdatePlayerInfoNotify.toObject(message.list[j], options);
                }

                return object;
              };
              /**
               * Converts this LOBBY_ONUPDATEUSERINFO_NOTIFY to JSON.
               * @function toJSON
               * @memberof proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              LOBBY_ONUPDATEUSERINFO_NOTIFY.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return LOBBY_ONUPDATEUSERINFO_NOTIFY;
            }();

            proto.OnUpdatePlayerInfoNotify = function () {
              /**
               * Properties of an OnUpdatePlayerInfoNotify.
               * @memberof proto
               * @interface IOnUpdatePlayerInfoNotify
               * @property {number|null} [type] OnUpdatePlayerInfoNotify type
               */

              /**
               * Constructs a new OnUpdatePlayerInfoNotify.
               * @memberof proto
               * @classdesc Represents an OnUpdatePlayerInfoNotify.
               * @implements IOnUpdatePlayerInfoNotify
               * @constructor
               * @param {proto.IOnUpdatePlayerInfoNotify=} [properties] Properties to set
               */
              function OnUpdatePlayerInfoNotify(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * OnUpdatePlayerInfoNotify type.
               * @member {number} type
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @instance
               */


              OnUpdatePlayerInfoNotify.prototype.type = 0;
              /**
               * Creates a new OnUpdatePlayerInfoNotify instance using the specified properties.
               * @function create
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @static
               * @param {proto.IOnUpdatePlayerInfoNotify=} [properties] Properties to set
               * @returns {proto.OnUpdatePlayerInfoNotify} OnUpdatePlayerInfoNotify instance
               */

              OnUpdatePlayerInfoNotify.create = function create(properties) {
                return new OnUpdatePlayerInfoNotify(properties);
              };
              /**
               * Encodes the specified OnUpdatePlayerInfoNotify message. Does not implicitly {@link proto.OnUpdatePlayerInfoNotify.verify|verify} messages.
               * @function encode
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @static
               * @param {proto.IOnUpdatePlayerInfoNotify} message OnUpdatePlayerInfoNotify message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              OnUpdatePlayerInfoNotify.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.type != null && message.hasOwnProperty("type")) writer.uint32(
                /* id 1, wireType 0 =*/
                8).int32(message.type);
                return writer;
              };
              /**
               * Encodes the specified OnUpdatePlayerInfoNotify message, length delimited. Does not implicitly {@link proto.OnUpdatePlayerInfoNotify.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @static
               * @param {proto.IOnUpdatePlayerInfoNotify} message OnUpdatePlayerInfoNotify message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              OnUpdatePlayerInfoNotify.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes an OnUpdatePlayerInfoNotify message from the specified reader or buffer.
               * @function decode
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.OnUpdatePlayerInfoNotify} OnUpdatePlayerInfoNotify
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              OnUpdatePlayerInfoNotify.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.OnUpdatePlayerInfoNotify();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.type = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes an OnUpdatePlayerInfoNotify message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.OnUpdatePlayerInfoNotify} OnUpdatePlayerInfoNotify
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              OnUpdatePlayerInfoNotify.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies an OnUpdatePlayerInfoNotify message.
               * @function verify
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              OnUpdatePlayerInfoNotify.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.type != null && message.hasOwnProperty("type")) if (!$util.isInteger(message.type)) return "type: integer expected";
                return null;
              };
              /**
               * Creates an OnUpdatePlayerInfoNotify message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.OnUpdatePlayerInfoNotify} OnUpdatePlayerInfoNotify
               */


              OnUpdatePlayerInfoNotify.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.OnUpdatePlayerInfoNotify) return object;
                var message = new $root.proto.OnUpdatePlayerInfoNotify();
                if (object.type != null) message.type = object.type | 0;
                return message;
              };
              /**
               * Creates a plain object from an OnUpdatePlayerInfoNotify message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @static
               * @param {proto.OnUpdatePlayerInfoNotify} message OnUpdatePlayerInfoNotify
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              OnUpdatePlayerInfoNotify.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};
                if (options.defaults) object.type = 0;
                if (message.type != null && message.hasOwnProperty("type")) object.type = message.type;
                return object;
              };
              /**
               * Converts this OnUpdatePlayerInfoNotify to JSON.
               * @function toJSON
               * @memberof proto.OnUpdatePlayerInfoNotify
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              OnUpdatePlayerInfoNotify.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return OnUpdatePlayerInfoNotify;
            }();

            proto.GAME_ONKICK_NOTIFY = function () {
              /**
               * Properties of a GAME_ONKICK_NOTIFY.
               * @memberof proto
               * @interface IGAME_ONKICK_NOTIFY
               * @property {number|null} [type] GAME_ONKICK_NOTIFY type
               * @property {number|null} [banType] GAME_ONKICK_NOTIFY banType
               */

              /**
               * Constructs a new GAME_ONKICK_NOTIFY.
               * @memberof proto
               * @classdesc Represents a GAME_ONKICK_NOTIFY.
               * @implements IGAME_ONKICK_NOTIFY
               * @constructor
               * @param {proto.IGAME_ONKICK_NOTIFY=} [properties] Properties to set
               */
              function GAME_ONKICK_NOTIFY(properties) {
                if (properties) for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i) if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]];
              }
              /**
               * GAME_ONKICK_NOTIFY type.
               * @member {number} type
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @instance
               */


              GAME_ONKICK_NOTIFY.prototype.type = 0;
              /**
               * GAME_ONKICK_NOTIFY banType.
               * @member {number} banType
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @instance
               */

              GAME_ONKICK_NOTIFY.prototype.banType = 0;
              /**
               * Creates a new GAME_ONKICK_NOTIFY instance using the specified properties.
               * @function create
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @static
               * @param {proto.IGAME_ONKICK_NOTIFY=} [properties] Properties to set
               * @returns {proto.GAME_ONKICK_NOTIFY} GAME_ONKICK_NOTIFY instance
               */

              GAME_ONKICK_NOTIFY.create = function create(properties) {
                return new GAME_ONKICK_NOTIFY(properties);
              };
              /**
               * Encodes the specified GAME_ONKICK_NOTIFY message. Does not implicitly {@link proto.GAME_ONKICK_NOTIFY.verify|verify} messages.
               * @function encode
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @static
               * @param {proto.IGAME_ONKICK_NOTIFY} message GAME_ONKICK_NOTIFY message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_ONKICK_NOTIFY.encode = function encode(message, writer) {
                if (!writer) writer = $Writer.create();
                if (message.type != null && message.hasOwnProperty("type")) writer.uint32(
                /* id 1, wireType 0 =*/
                8).int32(message.type);
                if (message.banType != null && message.hasOwnProperty("banType")) writer.uint32(
                /* id 2, wireType 0 =*/
                16).int32(message.banType);
                return writer;
              };
              /**
               * Encodes the specified GAME_ONKICK_NOTIFY message, length delimited. Does not implicitly {@link proto.GAME_ONKICK_NOTIFY.verify|verify} messages.
               * @function encodeDelimited
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @static
               * @param {proto.IGAME_ONKICK_NOTIFY} message GAME_ONKICK_NOTIFY message or plain object to encode
               * @param {$protobuf.Writer} [writer] Writer to encode to
               * @returns {$protobuf.Writer} Writer
               */


              GAME_ONKICK_NOTIFY.encodeDelimited = function encodeDelimited(message, writer) {
                return this.encode(message, writer).ldelim();
              };
              /**
               * Decodes a GAME_ONKICK_NOTIFY message from the specified reader or buffer.
               * @function decode
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @param {number} [length] Message length if known beforehand
               * @returns {proto.GAME_ONKICK_NOTIFY} GAME_ONKICK_NOTIFY
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_ONKICK_NOTIFY.decode = function decode(reader, length) {
                if (!(reader instanceof $Reader)) reader = $Reader.create(reader);
                var end = length === undefined ? reader.len : reader.pos + length,
                    message = new $root.proto.GAME_ONKICK_NOTIFY();

                while (reader.pos < end) {
                  var tag = reader.uint32();

                  switch (tag >>> 3) {
                    case 1:
                      message.type = reader.int32();
                      break;

                    case 2:
                      message.banType = reader.int32();
                      break;

                    default:
                      reader.skipType(tag & 7);
                      break;
                  }
                }

                return message;
              };
              /**
               * Decodes a GAME_ONKICK_NOTIFY message from the specified reader or buffer, length delimited.
               * @function decodeDelimited
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @static
               * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
               * @returns {proto.GAME_ONKICK_NOTIFY} GAME_ONKICK_NOTIFY
               * @throws {Error} If the payload is not a reader or valid buffer
               * @throws {$protobuf.util.ProtocolError} If required fields are missing
               */


              GAME_ONKICK_NOTIFY.decodeDelimited = function decodeDelimited(reader) {
                if (!(reader instanceof $Reader)) reader = new $Reader(reader);
                return this.decode(reader, reader.uint32());
              };
              /**
               * Verifies a GAME_ONKICK_NOTIFY message.
               * @function verify
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @static
               * @param {Object.<string,*>} message Plain object to verify
               * @returns {string|null} `null` if valid, otherwise the reason why it is not
               */


              GAME_ONKICK_NOTIFY.verify = function verify(message) {
                if (typeof message !== "object" || message === null) return "object expected";
                if (message.type != null && message.hasOwnProperty("type")) if (!$util.isInteger(message.type)) return "type: integer expected";
                if (message.banType != null && message.hasOwnProperty("banType")) if (!$util.isInteger(message.banType)) return "banType: integer expected";
                return null;
              };
              /**
               * Creates a GAME_ONKICK_NOTIFY message from a plain object. Also converts values to their respective internal types.
               * @function fromObject
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @static
               * @param {Object.<string,*>} object Plain object
               * @returns {proto.GAME_ONKICK_NOTIFY} GAME_ONKICK_NOTIFY
               */


              GAME_ONKICK_NOTIFY.fromObject = function fromObject(object) {
                if (object instanceof $root.proto.GAME_ONKICK_NOTIFY) return object;
                var message = new $root.proto.GAME_ONKICK_NOTIFY();
                if (object.type != null) message.type = object.type | 0;
                if (object.banType != null) message.banType = object.banType | 0;
                return message;
              };
              /**
               * Creates a plain object from a GAME_ONKICK_NOTIFY message. Also converts values to other types if specified.
               * @function toObject
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @static
               * @param {proto.GAME_ONKICK_NOTIFY} message GAME_ONKICK_NOTIFY
               * @param {$protobuf.IConversionOptions} [options] Conversion options
               * @returns {Object.<string,*>} Plain object
               */


              GAME_ONKICK_NOTIFY.toObject = function toObject(message, options) {
                if (!options) options = {};
                var object = {};

                if (options.defaults) {
                  object.type = 0;
                  object.banType = 0;
                }

                if (message.type != null && message.hasOwnProperty("type")) object.type = message.type;
                if (message.banType != null && message.hasOwnProperty("banType")) object.banType = message.banType;
                return object;
              };
              /**
               * Converts this GAME_ONKICK_NOTIFY to JSON.
               * @function toJSON
               * @memberof proto.GAME_ONKICK_NOTIFY
               * @instance
               * @returns {Object.<string,*>} JSON object
               */


              GAME_ONKICK_NOTIFY.prototype.toJSON = function toJSON() {
                return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
              };

              return GAME_ONKICK_NOTIFY;
            }();

            return proto;
          }();

          return $root;
        }(protobuf).proto; // #endregion ORIGINAL CODE


        _export("default", _cjsExports = module.exports);
      }, {});
    }
  };
});
//# sourceMappingURL=6a63d6a6370ecc331ab9f8edf6d8ca960dee45e4.js.map