import { LoginState } from "../../common/constant/Enums"
import NetEvent from "../../common/event/NetEvent"
import { gHelper } from "../../common/helper/GameHelper"
import NetworkModel from "../common/NetworkModel"
import UserModel from "../common/UserModel"
import { ecode } from "../../common/constant/ECode"
import { log } from "cc"
import PlayerModel from "../game/PlayerModel"
import ac from "db://assets/scene/ac"

/**
 * 登录模块
 */
@mc.addmodel('login')
export default class LoginModel extends mc.BaseModel {

    private readonly DEFAULT_MAX_RECONNECT_ATTEMPTS = 60 //最大重连次数

    private net: NetworkModel = null
    private user: UserModel = null
    private player: PlayerModel = null

    private initBaseAsset: boolean = false //是否初始化基础资源
    private initGameAsset: boolean = false //是否初始化游戏资源
    private reconnectAttempts: number = 0
    private reconnectionDelay: number = 5 //重连间隔（秒）

    public onCreate() {
        this.net = this.getModel('net')
        this.user = this.getModel('user')
        this.player = this.getModel('player')
    }

    public isInitBaseAsset() { return this.initBaseAsset }
    public setInitBaseAsset(val: boolean) { this.initBaseAsset = val }
    public isInitGameAsset() { return this.initGameAsset }
    public setInitGameAsset(val: boolean) { this.initGameAsset = val }

    // 服务器断开连接
    private onDisconnect(err: any) {
        this.emit(NetEvent.NET_DISCONNECT, err)
    }

    // 连接服务器
    public async connect() {
        this.net.offAll()
        // 连接服务器
        const ok = await this.net.connect(gHelper.getServerUrl())
        if (ok) {
            this.net.on('disconnect', this.onDisconnect, this)
        }
        return ok
    }

    // 重新连接
    public async reconnect() {
        this.reconnectAttempts = 0
        this.reconnectionDelay = 2
        let ok = false
        while (this.reconnectAttempts < this.DEFAULT_MAX_RECONNECT_ATTEMPTS) {
            if (this.net.isKick()) {
                return false
            }
            ok = await this.net.connect(gHelper.getServerUrl())
            if (ok) {
                this.net.on('disconnect', this.onDisconnect, this)
                break
            }
            await ut.wait(this.reconnectionDelay)
            this.reconnectAttempts += 1
            this.reconnectionDelay = Math.min(5, this.reconnectionDelay + 1) //下一次久一点
        }
        return ok
    }

    // 断开网络
    public disconnect() {
        this.net.off('disconnect')
        this.net.close()
    }

    // 加载游客id
    private async loadGuestId() {
        let id = storageMgr.loadString('guest_id')
        if (!id) {
            // id = await jsbHelper.getDeviceData('guest_id', 'ca_account')
        }
        return id || ut.UID()
    }

    private saveGuestId(id: string) {
        storageMgr.saveString('guest_id', id)
        // jsbHelper.saveDeviceData('guest_id', id, 'ca_account')
    }

    // 获取大厅服务器是否需要排队
    public async getLobbyServerIsNeedQueueUp() {
        const res = await this.net.post({ url: gHelper.getHttpServerUrl() + '/getServerLoad' })
        return !!res?.data?.lobbyFull
    }

    // 尝试登录
    public async tryLogin(accountToken?: string): Promise<any> {
        accountToken = accountToken || storageMgr.loadString('account_token')
        if (!accountToken || accountToken === '0') {
            if (accountToken !== '0') {
                // taHelper.track('ta_tutorial', { tutorial_step: '0-1' }) //首个场景打开
            }
            return { state: LoginState.NOT_ACCOUNT_TOKEN }
        }
        const { err, data } = await this.net.request('lobby/HD_TryLogin', {
            accountToken,
            lang: mc.lang,
            platform: gHelper.getShopPlatform(),
            version: ac.VERSION,
        })
        if (err === ecode.NOT_ACCOUNT_TOKEN || err === ecode.TOKEN_INVALID) { //没有token或者无效 都重新登录
            return { state: LoginState.NOT_ACCOUNT_TOKEN }
        } else if (err === ecode.VERSION_TOOLOW) { //提示版本过低
            return { state: LoginState.VERSION_TOOLOW, data }
        } else if (err === ecode.CUR_LOBBY_FULL) { //当前大厅服满了 重新登陆
            await ut.wait(0.5)
            return this.tryLogin(accountToken)
        } else if (err === ecode.LOBBY_QUEUE_UP) { //大厅服都满了 需要排队
            const ok = await this.getLobbyServerIsNeedQueueUp()
            if (ok) {
                return { state: LoginState.QUEUE_UP } //满了 需要排队
            }
            return this.tryLogin(accountToken) //没满继续登陆
        } else if (err === ecode.BAN_ACCOUNT) { //被封禁了
            return { state: LoginState.BANACCOUNT_TIME, type: data.banAccountType, time: data.banAccountSurplusTime }
        } else if (err) {
            log('tryLogin err!', err)
            return { state: LoginState.FAILURE, err: err }
        }
        const info = data.user
        this.user.init(info)
        if (data.gameBaseData) {
            this.player.initBaseData(data.gameBaseData)
        }
        // 这里大厅服 不再返回账号token 统一使用登陆服的token 重复使用
        storageMgr.saveString('account_token', data.accountToken || '0')
        return { state: LoginState.SUCCEED, data: { sid: info.sid } }
    }

    // 游客登录
    public async guestLogin() {
        let guestId = ut.getBrowserParamByKey('id')
        if (!guestId) {
            guestId = await this.loadGuestId()
        }
        const ok = await this.reconnect()
        if (!ok) {
            return { state: LoginState.FAILURE, err: 'login.failed' }
        }
        const { err, data } = await this.net.request('login/HD_GuestLogin', {
            guestId,
            nickname: assetsMgr.lang('login.guest_nickname'),
            platform: gHelper.getShopPlatform(),
        }, true)
        if (!err) {
            this.saveGuestId(data?.guestId || '')
        }
        return this.loginVerifyRet(err, data?.accountToken)
    }

    // 返回登陆验证结果
    private loginVerifyRet(err: string, accountToken: string): any {
        if (err) {
            return { state: LoginState.FAILURE, err: err }
        }
        storageMgr.saveString('account_token', accountToken || '0')
        //
        return { state: LoginState.SUCCEED, accountToken }
    }
}