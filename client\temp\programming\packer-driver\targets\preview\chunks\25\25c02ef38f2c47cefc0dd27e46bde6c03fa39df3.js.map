{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/common/config/HeroFrameAnimConf.ts"], "names": ["getHeroFrameAnimConf", "id", "conf", "HERO_FRAME_ANIM_CONF", "error", "url", "v3", "offset", "anims", "name", "interval", "loop", "frameIndexs"], "mappings": ";;;;;AAEA;AACA,WAASA,oBAAT,CAA8BC,EAA9B,EAA0C;AACtC,QAAMC,IAAI,GAAGC,oBAAoB,CAACF,EAAD,CAAjC;;AACA,QAAI,CAACC,IAAL,EAAW;AACPE,MAAAA,KAAK,CAAC,qCAAqCH,EAAtC,CAAL;AACA,aAAO,IAAP;AACH,KAHD,MAGO,IAAI,CAACC,IAAI,CAACG,GAAV,EAAe;AAClBH,MAAAA,IAAI,CAACG,GAAL,aAAmBJ,EAAnB,cAA8BA,EAA9B;AACH;;AACD,WAAOC,IAAP;AACH,G,CAED;;;kCA2CIF,oB;;;;;;;AAzDKI,MAAAA,K,OAAAA,K;AAAOE,MAAAA,E,OAAAA,E;;;;;;;;;AAeVH,MAAAA,oB,GAAuB;AACzB,gBAAQ;AAAE;AACNI,UAAAA,MAAM,EAAED,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,EAAN,CADN;AAEJE,UAAAA,KAAK,EAAE,CACH;AAAEC,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,QAAQ,EAAE,GAA1B;AAA+BC,YAAAA,IAAI,EAAE,IAArC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb;AAAxD,WADG,EAEH;AAAEH,YAAAA,IAAI,EAAE,QAAR;AAAkBC,YAAAA,QAAQ,EAAE,GAA5B;AAAiCC,YAAAA,IAAI,EAAE,IAAvC;AAA6CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB;AAA1D,WAFG,EAGH;AAAEH,YAAAA,IAAI,EAAE,QAAR;AAAkBC,YAAAA,QAAQ,EAAE,GAA5B;AAAiCC,YAAAA,IAAI,EAAE,KAAvC;AAA8CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB;AAA3D,WAHG,EAIH;AAAEH,YAAAA,IAAI,EAAE,KAAR;AAAeC,YAAAA,QAAQ,EAAE,GAAzB;AAA8BC,YAAAA,IAAI,EAAE,KAApC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB;AAAxD,WAJG,EAKH;AAAEH,YAAAA,IAAI,EAAE,KAAR;AAAeC,YAAAA,QAAQ,EAAE,GAAzB;AAA8BC,YAAAA,IAAI,EAAE,KAApC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC,EAA2C,IAA3C,EAAiD,IAAjD,EAAuD,IAAvD;AAAxD,WALG;AAFH,SADiB;AAWzB,gBAAQ;AAAE;AACNL,UAAAA,MAAM,EAAED,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CADN;AAEJE,UAAAA,KAAK,EAAE,CACH;AAAEC,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,QAAQ,EAAE,GAA1B;AAA+BC,YAAAA,IAAI,EAAE,IAArC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb;AAAxD,WADG,EAEH;AAAEH,YAAAA,IAAI,EAAE,QAAR;AAAkBC,YAAAA,QAAQ,EAAE,GAA5B;AAAiCC,YAAAA,IAAI,EAAE,IAAvC;AAA6CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB;AAA1D,WAFG;AAFH,SAXiB;AAkBzB,gBAAQ;AAAE;AACNL,UAAAA,MAAM,EAAED,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CADN;AAEJE,UAAAA,KAAK,EAAE,CACH;AAAEC,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,QAAQ,EAAE,GAA1B;AAA+BC,YAAAA,IAAI,EAAE,IAArC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb;AAAxD,WADG,EAEH;AAAEH,YAAAA,IAAI,EAAE,QAAR;AAAkBC,YAAAA,QAAQ,EAAE,GAA5B;AAAiCC,YAAAA,IAAI,EAAE,IAAvC;AAA6CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB;AAA1D,WAFG;AAFH,SAlBiB;AAyBzB,gBAAQ;AAAE;AACNL,UAAAA,MAAM,EAAED,EAAE,CAAC,EAAD,EAAK,CAAC,GAAN,CADN;AAEJE,UAAAA,KAAK,EAAE,CACH;AAAEC,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,QAAQ,EAAE,GAA1B;AAA+BC,YAAAA,IAAI,EAAE,IAArC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb;AAAxD,WADG,EAEH;AAAEH,YAAAA,IAAI,EAAE,QAAR;AAAkBC,YAAAA,QAAQ,EAAE,GAA5B;AAAiCC,YAAAA,IAAI,EAAE,IAAvC;AAA6CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB;AAA1D,WAFG;AAFH,SAzBiB;AAgCzB,gBAAQ;AAAE;AACNL,UAAAA,MAAM,EAAED,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CADN;AAEJE,UAAAA,KAAK,EAAE,CACH;AAAEC,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,QAAQ,EAAE,GAA1B;AAA+BC,YAAAA,IAAI,EAAE,IAArC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb;AAAxD,WADG,EAEH;AAAEH,YAAAA,IAAI,EAAE,QAAR;AAAkBC,YAAAA,QAAQ,EAAE,GAA5B;AAAiCC,YAAAA,IAAI,EAAE,IAAvC;AAA6CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB;AAA1D,WAFG;AAFH;AAhCiB,O", "sourcesContent": ["import { error, v3 } from \"cc\"\r\n\r\n// 获取英雄的帧动画配置\r\nfunction getHeroFrameAnimConf(id: number) {\r\n    const conf = HERO_FRAME_ANIM_CONF[id]\r\n    if (!conf) {\r\n        error('getHeroFrameAnimConf error. id: ' + id)\r\n        return null\r\n    } else if (!conf.url) {\r\n        conf.url = `hero/${id}/hero_${id}_`\r\n    }\r\n    return conf\r\n}\r\n\r\n// 英雄动画帧配置\r\nconst HERO_FRAME_ANIM_CONF = {\r\n    201001: { //张辽\r\n        offset: v3(-4, -40),\r\n        anims: [\r\n            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },\r\n            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },\r\n            { name: 'attack', interval: 120, loop: false, frameIndexs: ['01', '13', '14', '15', '16'] },\r\n            { name: 'hit', interval: 100, loop: false, frameIndexs: ['01', '27', '28', '29'] },\r\n            { name: 'die', interval: 160, loop: false, frameIndexs: ['01', '29', '30', '31', '32', '33', '00', '33', '00', '33'] },\r\n        ]\r\n    },\r\n    202001: { //刘备\r\n        offset: v3(10, -68),\r\n        anims: [\r\n            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },\r\n            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },\r\n        ]\r\n    },\r\n    202002: { //关羽\r\n        offset: v3(14, -68),\r\n        anims: [\r\n            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },\r\n            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },\r\n        ]\r\n    },\r\n    202003: { //张飞\r\n        offset: v3(16, -148),\r\n        anims: [\r\n            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },\r\n            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },\r\n        ]\r\n    },\r\n    202004: { //赵云\r\n        offset: v3(14, -76),\r\n        anims: [\r\n            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },\r\n            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },\r\n        ]\r\n    },\r\n}\r\n\r\nexport {\r\n    getHeroFrameAnimConf,\r\n}"]}