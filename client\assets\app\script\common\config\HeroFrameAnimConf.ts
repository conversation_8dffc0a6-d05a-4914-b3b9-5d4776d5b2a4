import { error, v3 } from "cc"

// 获取英雄的帧动画配置
function getHeroFrameAnimConf(id: number) {
    const conf = HERO_FRAME_ANIM_CONF[id]
    if (!conf) {
        error('getHeroFrameAnimConf error. id: ' + id)
        return null
    } else if (!conf.url) {
        conf.url = `hero/${id}/hero_${id}_`
    }
    return conf
}

// 英雄动画帧配置
const HERO_FRAME_ANIM_CONF = {
    201001: { //张辽
        offset: v3(-4, -40),
        anims: [
            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },
            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },
            { name: 'attack', interval: 120, loop: false, frameIndexs: ['01', '13', '14', '15', '16'] },
            { name: 'hit', interval: 100, loop: false, frameIndexs: ['01', '27', '28', '29'] },
            { name: 'die', interval: 160, loop: false, frameIndexs: ['01', '29', '30', '31', '32', '33', '00', '33', '00', '33'] },
        ]
    },
    202001: { //刘备
        offset: v3(10, -68),
        anims: [
            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },
            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },
        ]
    },
    202002: { //关羽
        offset: v3(14, -68),
        anims: [
            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },
            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },
        ]
    },
    202003: { //张飞
        offset: v3(16, -148),
        anims: [
            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },
            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },
        ]
    },
    202004: { //赵云
        offset: v3(14, -76),
        anims: [
            { name: 'idle', interval: 180, loop: true, frameIndexs: ['02', '03', '03'] },
            { name: 'caught', interval: 100, loop: true, frameIndexs: ['06', '07', '08', '09', '10'] },
        ]
    },
}

export {
    getHeroFrameAnimConf,
}