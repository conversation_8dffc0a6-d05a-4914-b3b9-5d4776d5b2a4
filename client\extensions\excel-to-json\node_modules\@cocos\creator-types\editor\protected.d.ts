/// <reference path="./editor.d.ts"/>
/// <reference path="./message.d.ts"/>
/// <reference path="./utils.d.ts"/>

import type { FileFilter, BrowserWindow, OpenDialogReturnValue, SaveDialogReturnValue, MessageBoxReturnValue, MenuItemConstructorOptions } from 'electron';

import type { EventEmitter } from 'events';

declare global {
    export namespace Editor {
        export namespace Dialog {
            export type DialogMode = 'normal' | 'command';

            // 覆盖外部配置，增加最后一个参数 window
            export function select(options?: SelectDialogOptions, window?: BrowserWindow): Promise<OpenDialogReturnValue>;
            export function save(options?: SaveDialogOptions, window?: BrowserWindow): Promise<SaveDialogReturnValue>;
            export function info(message: string, options?: MessageDialogOptions, window?: BrowserWindow): Promise<MessageBoxReturnValue>;
            export function warn(message: string, options?: MessageDialogOptions, window?: BrowserWindow): Promise<MessageBoxReturnValue>;
            export function error(message: string, options?: MessageDialogOptions, window?: BrowserWindow): Promise<MessageBoxReturnValue>;
            export const __protected__: {
                getMode(): DialogMode;
                setMode(mode: DialogMode): void;
            }
        }
        export namespace EditMode {
            export const __protected__: {

            }
        }
        export namespace I18n {
            export type I18nRegisterMap = {
                [key: string]: I18nRegisterMap | string;
            };
            export const __protected__: {
                /**
                 * 动态注册 i18n 数据
                 * @param language 语言
                 * @param key 翻译路径
                 * @param map 翻译表
                 */
                register(language: string, key: string, map: I18nRegisterMap): void;

                /**
                 * 导出语言数据
                 * @param language
                 */
                exportLanguageData(language?: string): I18nRegisterMap;
                /**
                 * 查询当前的语言列表
                 */
                getLanguageList(): string[];
            }
        }
        export namespace Layout {
            export interface CacheLayoutInfo {
                builtin: Record<string, any>;
                custom: Record<string, any>;
                version: string;
            }

            export const __protected__: {
                /**
                 * 初始化布局系统
                 */
                init(layout: Editor.Layout.ILayout): void;
                add(name: string, layout?: Editor.Layout.ILayout): void;
                remove(name: string): void;
                query(name?: string): Promise<Editor.Layout.ILayout>;
                queryList(): Promise<CacheLayoutInfo>;
                on(action: string, handle: (...args: any[]) => void): EventEmitter;
                once(action: string, handle: (...args: any[]) => void): EventEmitter;
                emit(action: string): boolean;
                removeListener(action: string, handle: (...args: any[]) => void): EventEmitter;
            };
        }
        export namespace Panel {
            export interface IPCOpenKitPanelOption {
                // 传给面板的参数
                params: (string | boolean | number | Record<string, string | boolean | number | Record<string, any>>)[];
                // 在面板上绑定的事件
                listeners: string[];
                // 当前面板的 id
                kitID: number;
                // 屏幕在完整的渲染区域的坐标
                // 多屏幕的时候，会有空区域，用于计算窗口的显示位置
                screen: {
                    x: number;
                    y: number;
                    width: number;
                    height: number;
                };
                position: {
                    x: number;
                    y: number;
                };
            }
            export interface KitOpenOption extends IPCOpenKitPanelOption {
                webContentID: number;
            }
            export interface OpenKitPanelOption {
                // 打开面板的传入参数
                params?: (string | boolean | number | Record<string, string | boolean | number | Record<string, any>>)[];
                // 绑定在元素上的事件
                listeners?: { [key: string]: (...args: any[]) => void };
                // 显示在哪一个元素上
                elem: Element;
            }
            export interface KitEventParams {
                // uuid 值
                value: string;
                // 其他详细信息 name，path, type， uuid, iconInfo
                info: Record<string, any>;
            }
            export const __protected__: {
                preloadKit(): void;
                openKit(name: string, options: OpenKitPanelOption): void;
                closeKit(name): void;
                holdKit(): void;
                /**
                 * 打开指定面板的开发者工具
                 * @param name
                 */
                _openDevTools(name: string): void;
            }
        }
        export namespace Logger {

            export const __protected__: {
                record(str: string): void;

                /**
                 * 监听 Logger 内发送的事件
                 * 谨慎使用，之后会被移除
                 * @param action 监听动作
                 * @param handle 处理函数
                 */
                on(action: string, handle: (...args: any[]) => void): EventEmitter;
                /**
                 * 监听 Logger 内发送的事件
                 * 谨慎使用，之后会被移除
                 * @param action 监听动作
                 * @param handle 处理函数
                 */
                once(action: string, handle: (...args: any[]) => void): EventEmitter;
                /**
                 * 触发一个监听事件
                 * @param action 监听动作
                 * @param args[]
                 */
                emit(action: string, ...args: any[]): boolean;
                /**
                 * 移除监听的事件
                 * 谨慎使用，之后会被移除
                 * @param action 监听动作
                 * @param handle 处理函数
                 */
                removeListener(action: string, handle: (...args: any[]) => void): EventEmitter;
            };
        }
        export namespace Menu {

            export const __protected__: {
                /**
                 * 添加一个菜单
                 * 只有主进程可以使用
                 * @param path
                 * @param options
                 */
                add(path: string, options: BaseMenuItem): void;

                /**
                 * 删除一个菜单
                 * 只有主进程可以使用
                 * @param path
                 * @param options
                 */
                remove(path: string, options: BaseMenuItem): void;

                /**
                 * 应用之前的菜单修改
                 * 只有主进程可以使用
                 */
                apply(): void;

                /**
                 * 添加分组信息
                 * @param path
                 * @param name
                 * @param order
                 */
                addGroup(path: string, name: string, order?: number): void;

                /**
                 * 删除分组信息
                 * @param path
                 * @param name
                 */
                removeGroup(path: string, name: string): void;

                /**
                 * 查询当前弹出的右键菜单的模版信息
                 */
                queryPopup(): Promise<ContextMenuItem[]>;

                /**
                 * 查询当前弹出的右键菜单的模版信息
                 * @param searcher 选择器
                 */
                clickPopup(searcher: string): Promise<void>;

                /**
                 * 查询主菜单的模版信息
                 */
                queryMain(): Promise<{ [key: string]: MainMenuItem }>;

                /**
                 * 查询当前弹出的右键菜单的模版信息
                 * @param searcher 选择器
                 */
                clickMain(searcher: string): Promise<void>;
                disconnectMainMenu(): void;
                connectMainMenu(): void;
            }
        }
        export namespace Message {
            export interface MessageRegisterInfo {
                [message: string]: MessageConfig;
            }
            export const __protected__: {
                /**
                 * 注册
                 * @param name
                 * @param messageInfo
                 */
                __register__(name: string, messageInfo: MessageRegisterInfo): void;
                /**
                 * 反注册
                 * @param name
                 */
                __unregister__(name: string): void;
                __eb__: EventEmitter;
                /**
                 * 新增一个广播消息监听器
                 * 不监听的时候，需要主动取消监听
                 * @param message 消息名
                 * @param func 处理函数
                 */
                addBroadcastListener(message: string, func: Function): void;
                /**
                 * 新增一个广播消息监听器
                 * @param message 消息名
                 * @param func 处理函数
                 */
                removeBroadcastListener(message: string, func: Function): void;
            }
        }
        export namespace Network {
            export const __protected__: {
                /**
                 * 测试是否可以联通 passport.cocos.com 服务器
                 */
                testConnectServer(): Promise<boolean>;
            }
        }
        export namespace Package {

            export type packageType = 'builtin' | 'cover' | 'local' | 'global' | 'other';

            export interface IDisableInfo {
                name: string;
                time: number;
            }

            export const __protected__: {
                /**
                 * 启动已经扫描到的插件
                 */
                startup(handle: (name: string, path: string) => Promise<void>): Promise<void>;
                /**
                 * 记录关闭的插件
                 * @param path
                 */
                addDisableInfo(path: string): void;
                /**
                 * 移除记录的关闭信息
                 * @param path
                 */
                removeDisableInfo(path: string): void;
                /**
                 * 获取一个插件是否被有被关闭的记录信息
                 * @param path
                 */
                queryDisableInfo(path: string): Promise<IDisableInfo | void>;
                /**
                 * 关闭同名的其他更高优先级的插件
                 * @param path
                 */
                disableOther(path: string): Promise<void>;
                /**
                 * 关闭后打开同名的插件，根据优先级顺序打开
                 * @param path
                 */
                enableOther(path: string): Promise<void>;
                /**
                 * 扫描一个目录，并将返回目录内的所有插件路径数组
                 * @param dir
                 */
                scan(dir: string): Promise<string[]>;
                /**
                 * 传入插件目录，返回插件的类别
                 * @param dir
                 */
                checkType(dir: string): packageType;
                /**
                 * 检查是否能够在当前版本打开
                 * @param version
                 */
                checkVersion(version: string): boolean;
                /**
                 * 检查是否需要刷新插件数据
                 * @param path
                 */
                checkReload(path: string): boolean;
                /**
                 * 格式化一个路径地址
                 * @param path
                 */
                normalizePath(path: string): string;
                /**
                 * 监听插件事件
                 */
                on(action: string, handle: (...args: any[]) => void): any;
                /**
                 * 监听一次插件事件
                 */
                once(action: string, handle: (...args: any[]) => void): any;
                /**
                 * 触发一个监听事件
                 */
                emit(action: string, ...args: any[]): any;
                /**
                 * 移除监听插件的事件
                 */
                removeListener(action: string, handle: (...args: any[]) => void): any;
                /**
                 * 比对版本号
                 * A > B => 1
                 * A = B => 0
                 * A < B => -1
                 * @param versionA 版本号
                 * @param versionB 版本号
                 */
                compareVersion(versionA: string, versionB: string): number;
            };
        }
        export namespace Profile {
            export const __protected__: {
                /**
                 * 迁移插件某个版本的本地配置数据到指定版本
                 * @param pkgName
                 * @param profileVersion
                 * @param profileData
                 * @param targetVersion
                 */
                migrateLocal(pkgName: string, profileVersion: string, profileData: Record<string, any>, targetVersion?: string): Promise<boolean>;
                /**
                 * 迁移插件某个版本的全局配置数据到指定版本
                 * @param pkgName
                 * @param profileVersion
                 * @param profileData
                 * @param targetVersion
                 */
                migrateGlobal(pkgName: string, profileVersion: string, profileData: Record<string, any>, targetVersion?: string): Promise<boolean>;
                /**
                 * 迁移插件某个版本的项目配置数据到指定版本
                 * @param pkgName
                 * @param profileVersion
                 * @param profileData
                 * @param targetVersion
                 */
                migrateProject(pkgName: string, profileVersion: string, profileData: Record<string, any>, targetVersion?: string): Promise<boolean>;
                /**
                 * 生成 profiles 对象
                 * @param name
                 */
                ensureProfiles(name: string): any;
                /**
                 * 监听 profile 事件
                 */
                on(action: string, handle: (...args: any[]) => void): EventEmitter;
                /**
                 * 监听一次 profile 事件
                 */
                once(action: string, handle: (...args: any[]) => void): EventEmitter;
                /**
                 * 触发一个监听事件
                 */
                emit(action: string, ...args: any[]): boolean;
                /**
                 * 移除监听的 profile 事件
                 */
                removeListener(action: string, handle: (...args: any[]) => void): EventEmitter;
                _registerMigration(pkgName: string, migrations: any[]): void;
            }
        }
        export namespace Project {
            export const __protected__: {
                type: string;
                /**
                 * 当前项目路径
                 */
                path: string;
                /**
                 * 当前项目 uuid
                 */
                uuid: string;
                /**
                 * 当前项目名称(取自 package.json)
                 */
                name: string;
                /**
                 * 当前项目临时文件夹
                 */
                tmpDir: string;

                /**
                 * 创建一个项目
                 */
                create(): void;
                /**
                 * 打开一个项目
                 *
                 * @param path
                 */
                open(path?: string): void;
                /**
                 * 添加一个项目
                 *
                 * @param path
                 */
                add(path: string): void;
                /**
                 * 获取最后打开的编辑器版本号
                 */
                getLastEditorVersion(): Promise<string>;
            }
        }

        export namespace User {
            /**
             * 跳过 User
             */
            export function skip(): void;
            /**
             * 显示用户登陆遮罩层
             */
            export function showMask(): void;
            /**
             * 隐藏用户登陆遮罩层
             */
            export function hideMask(): void;
            /**
             * 监听事件
             * @param action
             * @param handle
             */
            export function on(action: string, handle: (...args: any[]) => void): void;
            /**
             * 监听一次事件
             * @param action
             * @param handle
             */
            export function once(action: string, handle: (...args: any[]) => void): void;
            /**
             * 取消已经监听的事件
             * @param action
             * @param handle
             */
            export function removeListener(action: string, handle: (...args: any[]) => void): void;

            export const __protected__: {
                /**
                 * 初始化 User 模块
                 */
                init(): void;
                /**
                 * 通过插件名，获取证书信息
                 * @param pluginName
                 */
                queryLicenseByPluginName(pluginName: string);
                /**
                 * 通过证书名，获取证书信息
                 * @param name
                 */
                queryLicenseByName(name: string);
            }
        }

        export namespace Selection {
            export const __protected__: {
                /**
                 * 监听 profile 事件
                 */
                on(action: string, handle: (...args: any[]) => void): EventEmitter;
                /**
                 * 监听一次 profile 事件
                 */
                once(action: string, handle: (...args: any[]) => void): EventEmitter;
                /**
                 * 触发一个监听事件
                 */
                emit(action: string, ...args: any[]): boolean;
                /**
                 * 移除监听的 profile 事件
                 */
                removeListener(action: string, handle: (...args: any[]) => void): EventEmitter;
            }
        }
        export namespace Startup {
            export const __protected__: {
                init(options: Editor.Startup.builtinJSON): void;
                ready: {
                    readonly window: any;
                    readonly package: any;
                };
                window(options: {
                    beforeOpen(options: Editor.Windows.IWindowOptions, userData: { layout: Editor.Layout.ILayout }): void,
                    afterOpen(windows: any): void,
                }): Promise<void>;
                manager(skipLogin: boolean, outputMetricLog?: boolean): Promise<void>;
                startPackage(options?: {
                    preList?: string[];
                    list?: string[];
                }): Promise<void>;
                build(options: any, debug: boolean): Promise<any>;
                test(options: any, debug: boolean): Promise<any>;
                on(action: string, handle: (...args: any[]) => void): void;
                once(action: string, handle: (...args: any[]) => void): void;
                removeListener(action: string, handle: (...args: any[]) => void): void;
            }
            export interface builtinJSON {
                // 关闭的内置插件
                disableBuiltin?: string[];
                // 启动的外部插件
                extensions?: string[];
                //
                env?: {
                    // 更改编辑器配置目录
                    HOME?: string;
                    // 更改启动项目
                    PROJECT?: string;
                    // 启动默认使用的布局信息，支持文件路径和 json 对象
                    LAYOUT?: string;

                    // 启动默认使用的语言
                    LANGUAGE?: string;

                    // 主窗口钩子，在 Editor 执行后立即执行
                    // MAIN_WINDOW_HOOK?: string;
                    // 主窗口特殊的样式
                    // MAIN_WINDOW_STYLE?: string;
                    // 主窗口上的 Header
                    // MAIN_WINDOW_HEADER?: string;
                    // 叠加新的头部内容
                    MAIN_WINDOW_CUSTOM_HEADER?: string;
                    // 控制内置头部区域显示
                    MAIN_WINDOW_ORIGIN_HEADER?: {
                        ALL?: boolean, // ALL 是控制整个头部的显示；配置为 false 为隐藏; undefined 等同于 true 为显示;
                        CENTER?: boolean, // 中间预览操作区域；true / false 同上意义
                        RIGHT?: boolean, // 右边区域，但不包括关闭窗口的三个按钮区域
                    };

                    // 主窗口上的 Footer
                    MAIN_WINDOW_FOOTER?: string;

                    // 所有窗口钩子，在 Editor 执行后立即执行
                    WINDOW_HOOK?: string;
                    // 所有窗口的样式
                    WINDOW_STYLE?: string;
                };
            }
        }
        export namespace UI {
            export type HTMLCustomElement<T extends {} = Record<string, any>> = HTMLElement & T;

            export interface RegisterProtocolInfo {
                label: string;
                description?: string;
                path: string; // 与转换 handlers 二选一
                invalidInfo?: string;// 不符合当前协议头时的文本提示
                // 自定义协议转换
                // handlers?: {
                //     fileToUrl: (path: string) => string;
                //     urlToFile: (path: string) => string;
                // }
            }
            export interface ProtocolInfo extends RegisterProtocolInfo {
                protocol: string;
            }
            export const __protected__: {
                registerTranslator(handle: (key: string) => string): void;
                Base: any;
                Button: any;
                Input: any;
                NumInput: any;
                Loading: any;
                Checkbox: any;
                Section: any;
                Select: any;
                Bit: any;
                Slider: any;
                ColorPicker: any;
                Color: any;
                DragItem: any;
                DragArea: any;
                DragObject: any;
                Prop: any;
                Tooltip: any;
                TextArea: any;
                Progress: any;
                Label: any;
                Code: any;
                Tab: any;
                Gradient: any;
                GradientPicker: any;
                Icon: any;
                File: {
                    resolveToRaw(url: string): string;
                    resolveToUrl(raw: string, protocol: string): string;
                    registerProtocol(protocol: string, protocolInfo: RegisterProtocolInfo): boolean;
                    unregisterProtocol(protocol: string): boolean;
                    getAllProtocolInfos(): ProtocolInfo[];
                };
                Link: any;
                Image: any;
                QRCode: any;
                Markdown: any;
                Curve: any;
                CurveEditor: any;
                NodeGraph: any;
                Setting: any;
                Radio: any;
                RadioGroup: any;
                ScaleSlider: any;
            }
        }
        export namespace Metrics {
            export interface trackEventInfo {
                sendToCocosAnalyticsOnly?: boolean;
                // 是否只发送到新的统计后台
                sendToNewCocosAnalyticsOnly?: boolean;
                [propName: string]: any;
            }
            export interface trackWithTimerEventInfo {
                category: string; // 分组目录
                id: string; // 事件行为 ID
                value: number | string; // 事件值
            }
            export interface trackOptions {
                uid: string;
                cid: string;
                debug?: boolean;
            }
            export interface trackExceptionInfo {
                code: number;
                message: string;
            }
            export interface trackMemoryUsage {
                rss: number; // 常驻集大小 - 为无法与其他进程共享的进程分配的内存量
                heapTotal: number; // 堆的总大小
                heapUsed: number; // 堆使用的内存量
                external: number; // 由 V8 管理的与 JavaScript 对象绑定的 C++ 对象使用的内存量。
            }
            export interface trackTimeEndOptions {
                output?: boolean;
                label?: string;
                value?: number;
            }
            export interface trackTimeEndInfo {
                message: string;
                options: trackTimeEndOptions;
                time?: number;
            }
            export interface MetricsData {
                index: number;
                funcName: string,
                time: number,
                info?: trackEventInfo | trackWithTimerEventInfo | trackTimeEndInfo | trackExceptionInfo;
                process?: string;
                memoryInfo?: trackMemoryUsage;
            }
            /**
             * 追踪一个事件
             * @param info 跟踪的错误信息 Error message for trace
             */
            export function trackEvent(info: trackEventInfo): any;
            /**
             * 数据自增统计接口，添加的数据会与缓存数据结合递增
             * @param info 统计事件数据
             * @returns
             */
            export function _trackEventWithTimer(info: trackWithTimerEventInfo): any;
            /**
             * 追踪一个异常
             * @param info 跟踪的错误信息 Error message for trace
             */
            export function trackException(info: trackExceptionInfo): any;
            /**
             * 追踪进程内存占用情况
             * @param process 进程名
             * @param memoryInfo 内存信息
             */
            export function trackProcessMemory(process: string, memoryInfo: trackMemoryUsage): void;
            /**
             * 开始追踪时间
             * @param message
             */
            export function trackTimeStart(message: string): void;
            /**
             * 结束追踪时间
             * @param message
             * @param options 输出选项 { output：是否 console.debug 打印; label: 打印的消息名词的替换文本，支持 i18n: 写法; value: 直接打印计算好的统计时间}
             * @return 返回统计时间
             */
            export function trackTimeEnd(message: string, options?: { output?: boolean, label?: string, value?: number }): Promise<number>;
            /**
             * 结束统计，用于结束统计，并上传一些需要统计的数据/耗时
             */
            export function close(): Promise<boolean>;

            export const __protected__: {
                /**
                 * 统计崩溃事件
                 * @param info 统计事件数据
                 * @returns
                 */
                _trackCrashEvent(info: trackEventInfo): Promise<any>;
                /**
                 * 发送收集的统计数据
                 */
                _sendEventGroup();
                /**
                 * 注册上层插件调用的接口
                 * @param key
                 * @param func
                 */
                register(key: string, func: any);
                /**
                 * 重置存储统计数据列表与标记
                 */
                reset();
                /**
                 * 获取初始化数据
                 */
                getMetricInitData(): any;
                /**
                 * 设置初始化数据
                 * @param data
                 */
                setMetricInitData(data: any);
                /**
                 * 获取存储统计的数据
                 */
                getTrackInfoList(): any[];
                /**
                 * 获取存储耗时 Map
                 */
                getTrackTimeStartMap(): Map<string, number>;
                /**
                 * 获取上传插件注册的处理函数数组
                 */
                getTrackAwaitHandler(): any[];
                /**
                 * 获取存储内存信息
                 */
                getProcessMemoryMap(): Map<string, number>;
                /**
                 * 开始追踪进程内存
                 * Start tracking process memory
                 * @param message
                 * @param memory
                 */
                trackProcessMemoryStart(message: string, memory?: trackMemoryUsage);
                /**
                 * 结束追踪进程内存
                 * End trace process memory
                 * @param message
                 * @param memory
                 * @return 返回进程内存增量
                 */
                trackProcessMemoryEnd(message: string, memory?: trackMemoryUsage): Promise<number>;
            }
        }

        export namespace Utils {
            export namespace Process {
                // export interface IQuickSpawnOption extends SpawnOptions {
                //     cwd?: string;
                //     env?: any;
                //     // 输出等级，默认 = 0，即 log 级别以上都打印
                //     logLevel?: LogLevel;

                //     downGradeWaring?: boolean; // 警告将会转为 log 打印，默认为 false
                //     downGradeLog?: boolean; // log 将会转为 debug 打印，默认为 true
                //     downGradeError?: boolean; // 错误将会转为警告打印，默认为 false

                //     onlyPrintWhenError?: boolean; // 默认为 true, 日志都正常收集，但仅在发生错误时打印信息，其他时候静默处理

                //     prefix?: string; // 日志输出前缀
                // }
                // /**
                //  * 快速开启子进程，无需再自行监听输出，将会返回一个标记完成的 promise 对象
                //  * @param command 命令
                //  * @param cmdParams 参数数组
                //  * @param options 可选，开启的一些参数配置
                //  */
                //  export function quickSpawn(command: string, cmdParams: string[], options?: IQuickSpawnOption):Promise<boolean>;
            }
        }
        export namespace Module {
            export type RequireOptions = {
                root?: string;
            };
            export type ImportProjectModuleDelegate = (url: string) => Promise<unknown>;

            export const __protected__: {
                /**
                 * 动态加载一个脚本模块
                 * @param file
                 */
                requireFile(file: string, options?: RequireOptions): any;
                /**
                 * 删除加载的模块文件的缓存
                 * @param file
                 */
                removeCache(file: string): any;
                setImportProjectModuleDelegate(delegate: ImportProjectModuleDelegate): void;
            }
        }
        export namespace Windows {
            interface IWindowOptions {
                frame?: boolean;
                center?: boolean;
                width?: number;
                height?: number;
                minWidth?: number;
                minHeight?: number;
                titleBarStyle?: 'hiddenInset';
                titleBarOverlay?: number;
                autoHideMenuBar?: boolean;
                menuBarVisibility?: boolean;
                transparent?: boolean;
                resizable?: boolean;
                fullscreen?: boolean;
                minimizable?: boolean;
                show?: boolean;
                title?: string;
                webPreferences?: {
                    nodeIntegration?: boolean;
                    nodeIntegrationInWorker?: boolean;
                    webviewTag?: boolean;
                    // backgroundThrottling?: boolean;
                    enableRemoteModule?: boolean;
                    contextIsolation?: boolean;
                    backgroundThrottling?: boolean;
                    zoomFactor?: number;
                };
            }

            export const __protected__: {
                startup(): void;
                maximize(): void;
                minimize(): void;
                close(): void;
                open(HTML: string, options?: IWindowOptions, userData?: { [key: string]: any }): Promise<string>;
                /**
                 * 设置默认配置 ZoomLevel
                 * 设置后会同步到所有窗口
                 * @param level
                 */
                setDefaultZoomLevel(level: number): void;
                /**
                 * 设置默认配置 ZoomLevel
                 * 设置后会同步到所有窗口
                 * @return zoom level
                 */
                getDefaultZoomLevel(): Promise<number> | number;
                /**
                 * 设置窗口缩放级别（默认当前聚焦的窗口，如果没有就是主窗口）
                 * @param level - 缩放级别
                 * @param winID - 指定窗口 id
                 */
                setZoomLevel(level: number, winID?: string): void;
                /**
                 * 获取窗口缩放级别（默认当前聚焦的窗口，如果没有就是主窗口）
                 * @param winID - 指定窗口 id
                 * @return level - 缩放级别
                 */
                getZoomLevel(winID?: string): Promise<number>;
                queryUserData(winID?: string): any;
                changeUserData(data: { [key: string]: any }, winID?: string): void;
                changeMinSize(width: number, height: number, winID?: string): void;

                generateBlank(): void;
                openSubWindow(size: { width: number, height: number }, userData: { [key: string]: any })
                openSimpleWindow(size: { width: number, height: number }, userData: { [key: string]: any })

                changeMainTitle(title: string): void;
                queryMainTitle(): Promise<string>;
                setBeforeOpenHook(func: (options: Editor.Windows.IWindowOptions, userData: { layout: Editor.Layout.ILayout, }) => void): void;
                setAfterOpenHook(func: (windows: any) => void): void;
            };
        }

        export namespace Task {
            export const __protected__: {
                /**
                 * 添加一个同步任务
                 * 会在主窗口显示一个遮罩层
                 * @param title 任务名字
                 * @param describe 任务描述
                 * @param message 任务内容
                 */
                addSyncTask(title: string, describe?: string, message?: string): void;
                /**
                 * 更新某一个同步任务显示的数据
                 * @param title 任务名字
                 * @param describe 任务描述
                 * @param message 任务内容
                 */
                updateSyncTask(title: string, describe?: string, message?: string): void;
                /**
                 * 删除一个同步任务
                 * @param title 任务的名字
                 */
                removeSyncTask(title: string): void;
                /**
                 * 是否正在显示遮罩
                 */
                hasSyncTask(): Promise<boolean>;
                /**
                 * 页面进程立即同步一次主进程数据
                 */
                sync(): void;
                /**
                 * 监听插件事件
                 */
                on(action: string, handle: (...args: any[]) => void): EventEmitter;
                /**
                 * 监听一次插件事件
                 */
                once(action: string, handle: (...args: any[]) => void): EventEmitter;
                /**
                 * 触发一个监听事件
                 */
                emit(action: string, ...args: any[]): boolean;
                /**
                 * 移除监听插件的事件
                 */
                removeListener(action: string, handle: (...args: any[]) => void): EventEmitter;
            }
        }
    }
}
export { };
