import { Node, BlockInputEvents, view, sys, Layers, UITransform, Component, log, error, Prefab, instantiate, Sorting2D, UIRenderer } from "cc"
import BasePnlCtrl from "./base/BasePnlCtrl"
import BaseWindCtrl from "./base/BaseWindCtrl"
import BaseNoticeCtrl from "./base/BaseNoticeCtrl"
import BaseWdtCtrl from "./base/BaseWdtCtrl"
import BaseModel from "./base/BaseModel"
import WindCtrlMgr from "./manage/WindCtrlMgr"
import ViewCtrlMgr from "./manage/ViewCtrlMgr"
import ModelMgr from "./manage/ModelMgr"
import CoreEventType from "./event/CoreEventType"
import ViewLayerCtrl from "./layer/ViewLayerCtrl"
import WindLayerCtrl from "./layer/WindLayerCtrl"
import NoticeLayerCtrl from "./layer/NoticeLayerCtrl"
import NoticeCtrlMgr from "./manage/NoticeCtrlMgr"
import ButtonEx from "./component/ButtonEx"
import ScrollViewEx from "./component/ScrollViewEx"
import LabelWaitDot from "./component/LabelWaitDot"
import LabelRollNumber from "./component/LabelRollNumber"
import LabelTimer from "./component/LabelTimer"
import MultiColor from "./component/MultiColor"
import MultiFrame from "./component/MultiFrame"
import LocaleLabel from "./component/LocaleLabel"
import LocaleRichText from "./component/LocaleRichText"
import LocaleSprite from "./component/LocaleSprite"
import ScrollViewPlus from "./component/ScrollViewPlus"
import LocaleFont from "./component/LocaleFont"

class CCMvc {

    public GameNameSpace: string = 'ac'
    public app: Component = null
    public DEBUG: boolean = false

    public readonly BasePnlCtrl = BasePnlCtrl
    public readonly BaseWindCtrl = BaseWindCtrl
    public readonly BaseNoticeCtrl = BaseNoticeCtrl
    public readonly BaseWdtCtrl = BaseWdtCtrl
    public readonly BaseModel = BaseModel

    public readonly ButtonEx = ButtonEx
    public readonly ScrollViewEx = ScrollViewEx
    public readonly ScrollViewPlus = ScrollViewPlus
    public readonly LabelWaitDot = LabelWaitDot
    public readonly LabelRollNumber = LabelRollNumber
    public readonly LabelTimer = LabelTimer
    public readonly MultiColor = MultiColor
    public readonly MultiFrame = MultiFrame
    public readonly LocaleFont = LocaleFont
    public readonly LocaleLabel = LocaleLabel
    public readonly LocaleRichText = LocaleRichText
    public readonly LocaleSprite = LocaleSprite

    public Event: any = null
    private __modelMgr: ModelMgr = new ModelMgr()

    private __canChangeLang: boolean = false //是否实时切换
    private __lang: string = '' //当前语言

    private __windLayerCtrl: WindLayerCtrl = null!
    private __viewLayerCtrl: ViewLayerCtrl = null!
    private __noticeLayerCtrl: NoticeLayerCtrl = null!
    private __lockNode: Node = null
    private __locks: string[] = [] //锁列表

    private __temp_models: any = {}

    public init(name: string, app: Component, opts?: AppParam) {
        // window['__errorHandler'] = this.onErrorHandler.bind(this)
        this.GameNameSpace = name || 'game'
        this.app = app
        this.DEBUG = !!opts.debug
        this.__lang = opts?.lang || this.__lang
        this.__canChangeLang = !!opts?.changeLang
        const root = app.node
        // 清理所有事件
        eventCenter.clean()
        // 创建各个视图层
        this.__windLayerCtrl = this.createNode('Wind', Layers.Enum.DEFAULT, root, 1).addComponent(WindLayerCtrl).__init(new WindCtrlMgr())
        this.__viewLayerCtrl = this.createNode('View', Layers.Enum.UI_2D, root, 2).addComponent(ViewLayerCtrl).__init(new ViewCtrlMgr())
        this.__noticeLayerCtrl = this.createNode('Notice', Layers.Enum.UI_2D, root, 3).addComponent(NoticeLayerCtrl).__init(new NoticeCtrlMgr())
        // 创建锁定触摸节点
        this.__lockNode = this.createNode('LockNode', Layers.Enum.UI_2D, root, 4)
        this.__lockNode.addComponent(BlockInputEvents)
        this.__lockNode.active = false
        // 事件
        this.Event = CoreEventType
        // 初始化模型
        if (this.__temp_models) {
            this.__modelMgr.init(this.__temp_models)
            this.__modelMgr.create()
            this.__temp_models = undefined
        } else if (this.__modelMgr) {
            this.__modelMgr.create()
        }
        // twlog.info('mvc core init!')
        const win = view.getVisibleSize(), safe = sys.getSafeAreaRect()
        log('WinSize=' + win.width + ',' + win.height, 'SafeSzie=' + safe.width + ',' + safe.height)
    }

    private createNode(name: string, layer: number, root: Node, zIndex: number) {
        let node = root.FindChild(name)
        if (node) {
            node.destroy()
        }
        node = new Node(name)
        node.parent = root
        node.layer = layer
        const transform = node.addComponent(UITransform)
        const size = view.getVisibleSize()
        transform.width = size.width
        transform.height = size.height
        node.setSiblingIndex(zIndex)
        return node
    }

    // 错误
    private onErrorHandler(filename: any, line: any, msg: any) {
        error(filename, line, msg)
        eventCenter.emit(CoreEventType.MVC_ERROR_MSG, { name: filename, error: msg, stack: line })
    }

    public getWindNode() { return this.__windLayerCtrl.node }
    public getViewNode() { return this.__viewLayerCtrl.node }
    public getNoticeNode() { return this.__noticeLayerCtrl.node }

    // 设置获取当前wind
    public get currWind(): BaseWindCtrl {
        return this.__windLayerCtrl.getCurrWind()
    }

    // 获取当前wind名字
    public get currWindName(): string {
        let wind = this.__windLayerCtrl.getCurrWind()
        return wind ? wind.key : 'null'
    }

    // 获取当前打开的列表
    public getOpenPnls() {
        return this.__viewLayerCtrl.getOpenPnls()
    }

    // 锁定触摸
    public lockTouch(key: string) {
        this.__lockNode.active = true
        if (this.__locks.has(key)) {
            logger.error('repeat lock! ' + key)
        } else {
            this.__locks.push(key)
        }
        // cc.error('lockTouch', key)
    }

    // 解锁触摸
    public unlockTouch(key: string) {
        this.__locks.remove(key)
        this.__lockNode.active = this.__locks.length > 0
        // cc.error('unlockTouch', key)
    }

    // 当前是否触摸状态
    public isLockTouch() {
        return this.__lockNode.active
    }

    // 实例化
    public instantiate(item: Node | Prefab, parent: Node | Component) {
        const node = instantiate(item) as Node
        node.parent = parent instanceof Component ? parent.node : parent
        return node
    }

    // ----------------------------------多语言------------------------------------------
    // 切换语言
    public get lang() { return this.__lang }
    public set lang(val: string) {
        this.__lang = val
        eventCenter.emit(CoreEventType.LANGUAGE_CHANGED, this.__lang)
    }

    public get canChangeLang() { return this.__canChangeLang }
    public set canChangeLang(val: boolean) {
        this.__canChangeLang = val
    }

    // ----------------------------------模块管理------------------------------------------
    // 添加模型装饰器
    public addmodel(type: string) {
        const self = this
        return function (target: Function) {
            self.__temp_models[type] = target
        }
    }

    public getModel<T>(key: string): T {
        return this.__modelMgr.get<T>(key)
    }

    public pushModel(...params: BaseModel[]): void {
        this.__modelMgr.add(...params)
    }

    public resetModel(model: BaseModel): void {
        this.__modelMgr.reset(model)
    }
}

// @ts-ignore
window['mc'] = new CCMvc()