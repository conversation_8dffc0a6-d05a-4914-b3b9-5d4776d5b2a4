import { Color, Component, game, isValid, misc, Node, Prefab, sys, v2, v3, Vec2, Vec3, Game } from "cc"

class Utils {

    // 时间
    public readonly Time = {
        Day: 24 * 60 * 60 * 1000,// 天
        Hour: 60 * 60 * 1000,// 时
        Minute: 60 * 1000,// 分
        Second: 1000, //秒
    }

    // 2^50
    public readonly MAX_VALUE = 1125899906842624
    public readonly MIN_VALUE = -1125899906842624

    private _accumulation: number = 0
    private _last_now: number = 0

    private _temp_vec2_1: Vec2 = v2()
    private _temp_vec2_2: Vec2 = v2()
    private _temp_vec3_1: Vec3 = v3()
    private _temp_vec3_2: Vec3 = v3()
    private _pad_tbl: any = {}
    private _clone_cache: any[] = []
    private _lock_map: { [key: string]: boolean } = {}

    public now() {
        return Date.now()
    }

    public dateZeroTime(msd?: number) {
        const date = new Date(msd ?? this.now())
        return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0).getTime()
    }

    public millisecondToString(msd: number) {
        const second = msd / 1000
        const day = Math.floor(second / 86400)
        const hour = Math.floor(second / 3600)
        const minute = Math.floor(second / 60)
        if (day > 0) {
            return day + '天'
        } else if (hour > 0) {
            return hour + '小时'
        } else if (minute > 0) {
            return minute + '分钟'
        } else {
            return Math.floor(second) + '秒'
        }
    }

    // 将毫秒数格式化
    public millisecondFormat(msd: number, format: string = 'mm:ss') {
        let second = msd / 1000
        if (/(d+)/i.test(format)) {
            const day = Math.floor(second / 86400)
            second -= day * 86400
            format = format.replace(/(d+)/g, RegExp.$1.length === 1 ? day + '' : this.pad(day))
        }
        if (/(h+)/i.test(format)) {
            const hour = Math.floor(second / 3600)
            second -= hour * 3600
            format = format.replace(/(h+)/g, RegExp.$1.length === 1 ? hour + '' : this.pad(hour))
        }
        if (/(m+)/i.test(format)) {
            const minute = Math.floor(second / 60)
            second -= minute * 60
            format = format.replace(/(m+)/g, RegExp.$1.length === 1 ? minute + '' : this.pad(minute))
        }
        if (/(s+)/i.test(format)) {
            format = format.replace(/(s+)/g, RegExp.$1.length === 1 ? Math.floor(second) + '' : this.pad(Math.floor(second)))
        }
        return format
    }
    public secondFormat(val: number, format: string = 'mm:ss') {
        return this.millisecondFormat(val * 1000, format)
    }

    // 将日期毫秒数格式化 format('yyyy-MM-dd hh:mm:ss')
    public dateFormat(format: string, msd?: number) {
        const date = msd ? new Date(msd) : new Date()
        const re = /(y+)/i
        if (re.test(format)) {
            const t = re.exec(format)[1]
            format = format.replace(t, (date.getFullYear() + '').substring(4 - t.length))
        }
        const obj = {
            'M+': date.getMonth() + 1, //月份
            'd+': date.getDate(), //日
            'h+': date.getHours(), //小时
            'm+': date.getMinutes(), //分
            's+': date.getSeconds(), //秒
            'q+': Math.floor((date.getMonth() + 3) / 3), //季度
            'S+': date.getMilliseconds(), //毫秒
        }
        for (let k in obj) {
            const r = new RegExp('(' + k + ')')
            if (r.test(format)) {
                const t = r.exec(format)[1]
                format = format.replace(t, t.length === 1 ? String(obj[k]) : this.pad(obj[k]))
            }
        }
        return format
    }

    // 首字母变成大写
    public initialUpperCase(str: string): string {
        return str.length > 0 ? (str[0].toUpperCase() + str.substring(1, str.length)) : str
    }

    // 将数字转换为String
    public simplifyMoneyCh(money: number, num: number = 1000): string {
        const value = Math.abs(money)
        if (value >= 100000000 && value >= num) {
            return parseFloat((money / 100000000).toFixed(2)) + '亿'
        } else if (value >= 10000 && value >= num) {
            return parseFloat((money / 10000).toFixed(2)) + '万'
        } else if (value >= 1000 && value >= num) {
            return parseFloat((money / 1000).toFixed(2)) + '千'
        }
        return money + ''
    }

    // 将数字转换为String
    public simplifyMoneyEn(money: number, num: number = 1000): string {
        const value = Math.abs(money)
        if (value < num) {
            return parseFloat(money.toFixed(2)) + ''
        } else if (value >= 1000000000000) {
            return parseFloat((money / 1000000000000).toFixed(2)) + 't'
        } else if (value >= 1000000000) {
            return parseFloat((money / 1000000000).toFixed(2)) + 'g'
        } else if (value >= 1000000) {
            return parseFloat((money / 1000000).toFixed(2)) + 'm'
        } else {
            return parseFloat((money / 1000).toFixed(2)) + 'k'
        }
    }

    // 将数字转换为String
    public simplifyMoney(money: number, num: number = 1000000): string {
        const value = Math.abs(money)
        if (value >= 100000000 && value >= num) {
            return parseFloat((money / 1000000).toFixed(2)) + 'm'
        } else if (value >= 1000000 && value >= num) {
            return parseFloat((money / 1000).toFixed(2)) + 'k'
        }
        return money + ''
    }

    // 名字省略
    public nameFormator(name: string, max: number, extra: string = '...'): string {
        if (!name) {
            return ''
        }
        name = String(name)
        if (name.length <= max) {
            return name
        }
        let cnt = 0, len = 0
        max = max * 2
        for (let i = 0, l = name.length; i < l; i++) {
            const val = name.charCodeAt(i) > 255 ? 2 : 1
            if (len + val <= max - 2) {
                cnt += 1
            }
            len += val
            if (len > max) {
                break
            }
        }
        if (len <= max) {
            return name
        } else if (extra) {
            return name.substring(0, cnt) + extra
        }
        return name.substring(0, cnt + 1)
    }

    // 获取字符串长度 一个汉字2个长度
    public getStringLen(str: string) {
        if (!str) {
            return 0
        }
        let count = 0
        for (let i = 0, l = str.length; i < l; i++) {
            const c = str.charCodeAt(i)
            const val = (c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f) ? 1 : 2
            count += val
        }
        return count
    }

    // 将数字以逗号隔开
    public formatNumberByComma(num: number): string {
        return num.toString().replace(/\d(?=(?:\d{3})+\b)/g, '$&,')
    }

    // 随机一个整数 包括min和max
    public random(min: number, max?: number): number {
        if (max === undefined) {
            max = min
            min = 0
        }
        if (min >= max) {
            return min
        }
        return Math.floor(Math.random() * (Math.max(max - min, 0) + 1)) + min
    }

    // 概率
    public chance(odds: number, mul: number = 100): boolean {
        return odds > 0 && this.random(0, 100 * mul - 1) < odds * mul
    }

    // 随机一个负数到正数的范围
    public randomRange(min: number, max: number): number {
        return Math.floor(Math.random() * min) + Math.floor(Math.random() * max)
    }

    // 随机一个下标出来
    public randomIndex(len: number, count?: number, ignore?: any) {
        if (len === 0) {
            return -1
        }
        let indexs = [], _count = count
        ignore = Array.isArray(ignore) ? ignore : [ignore]
        _count = _count || 1
        _count = _count > len ? len : _count
        for (let i = 0; i < len; i++) {
            if (ignore.indexOf(i) !== -1) {
                continue
            }
            indexs.push(i)
        }
        let ret = []
        while (indexs.length > 0 && ret.length < _count) {
            let idx = this.random(indexs.length - 1)
            ret.push(indexs.splice(idx, 1)[0])
        }
        if (ret.length === 0) {
            return -1
        }
        return count === 1 ? ret[0] : ret
    }

    // 根据权重随机
    public randomIndexByWeight(arr: any[], key?: string | number) {
        if (!arr || !Array.isArray(arr) || !arr.length) {
            return -1
        }
        const totalWeight = arr.reduce((val, cur) => val + Number(key ? cur[key] : cur) * 100, 0)
        if (totalWeight === 0) {
            return this.random(0, arr.length - 1)
        }
        let offset = this.random(0, totalWeight - 1)
        for (let i = 0, l = arr.length; i < l; i++) {
            const val: number = (key ? arr[i][key] : arr[i]) * 100
            if (offset < val) {
                return i
            } else {
                offset -= val
            }
        }
        return this.random(0, arr.length - 1)
    }

    // 打乱数组
    public shuffleArray(arry: any[]) {
        const brr = arry.slice() // 创建原数组的副本，以免修改原始数组
        for (let i = arry.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1)); // 生成随机索引
            // 交换当前元素与随机索引位置上的元素
            [brr[i], brr[j]] = [brr[j], brr[i]]
        }
        return brr
    }

    // 获取两点之间的角度
    public getAngle(a: Vec2, b: Vec2): number {
        return misc.radiansToDegrees(Math.atan2(b.y - a.y, b.x - a.x))
    }
    // 规范角度 
    public normAngle(angle: number) {
        return angle > 0 ? angle - 360 : angle
    }
    public sin(angle: number) {
        return Math.sin(misc.degreesToRadians(angle))
    }
    public cos(angle: number) {
        return Math.cos(misc.degreesToRadians(angle))
    }

    // 根据角度和距离 获取坐标
    public angleToPoint(angle: number, dis: number, out?: Vec2) {
        out = out || this._temp_vec2_1
        out.x = this.cos(angle) * dis
        out.y = this.sin(angle) * dis
        return out
    }

    // 获取某个节点在某个节点里面的坐标
    public convertToNodeAR(node: Node, targetNode: Node, out?: Vec2): Vec2 {
        out = out || this._temp_vec2_2.set(0, 0)
        if (!node?.isValid || !targetNode?.isValid) {
            return out
        }
        // const _out = out.toVec3()
        // // 先将节点转到世界坐标
        node.getWorldMatrix(this._temp_mat4).getTranslation(this._temp_vec3_1)
        // // 再将节点转到目标节点局部坐标
        // return targetNode.transform.convertToNodeSpaceAR(this._temp_vec3_1, _out).toVec2()
        const worldPos = node.getWorldPosition(this._temp_vec3_1) //待转换的世界坐标
        const targetWorldPos = targetNode.getWorldPosition(this._temp_vec3_2) //目标节点的世界坐标
        return out.set(worldPos.subtract(targetWorldPos).toVec2()) // 相减得到局部坐标
    }

    // 数字 字符串补0,根据长度补出前面差的0
    public pad(num: number, length: number = 2): string {
        const len = length - num.toString().length
        if (len <= 0) {
            return num + ''
        } else if (!this._pad_tbl[len]) {
            this._pad_tbl[len] = (new Array(len + 1)).join('0')
        }
        return this._pad_tbl[len] + num
    }

    // 将一个数字 分解成多个类型的数字
    public decomposeNumberToTypes(num: number, types: number[] = [100000, 10000, 1000, 100, 10, 1], out?: any): any {
        let ret = out || {}, type: number, count: number
        types.sort((a, b) => b - a) //先从大到小排个序
        for (let i = 0; i < types.length; i++) {
            type = types[i]
            count = Math.floor(num / types[i])
            if (count >= 1) {
                ret[type] = count
                num -= type * count
            }
        }
        // 如果还有 就默认去最后一个 算一个
        if (num > 0) {
            type = types[types.length - 1]
            count = ret[type] || 0
            ret[type] = count ? count + 1 : 1
        }
        return ret
    }

    // 将一个字符串转换成向量
    public stringToVec2(str: string, separator: string = ','): Vec2 {
        if (!str) {
            return v2()
        }
        const [x, y] = str.split(separator)
        return v2(parseFloat(x), parseFloat(y))
    }

    // 将一个字符串拆分为数组
    public stringToNumbers(str: string | number, separator: string = '|') {
        if (str === null || str === undefined || str === '') {
            return []
        } else if (typeof str === 'number') {
            return [str]
        }
        return str.split(separator).filter(m => m.trim() !== '').map(m => Number(m))
    }

    // 将一个常数变成1
    public normalizeNumber(val: number): number {
        return val === 0 ? 0 : val / Math.abs(val)
    }

    // 将一个数字转换为带正负符号的字符串
    public numberToString(val: number): string {
        return (val >= 0 ? '+' : '') + val
    }

    // 字符串填充参数
    public stringFormat(text: string, params: any[]): string {
        if (!text || !params || params.length === 0) {
            return text
        }
        params.forEach((p, i) => (text = text.replace(new RegExp('\\{' + i + '\\}', 'g'), p)))
        return text
    }

    // 等待 单位秒
    public async wait(delay: number, target?: Component): Promise<void> {
        if (delay <= 0) {
            return Promise.resolve()
        }
        const timer = target || mc.app
        return new Promise(resolve => timer.scheduleOnce(() => resolve(), delay))
    }

    // 等待 
    public async waitTimeout(delay: number): Promise<void> {
        if (delay <= 0) {
            return Promise.resolve()
        }
        return new Promise(resolve => setTimeout(() => resolve(), delay))
    }

    // 
    public setTimeout(cb: Function, delay: number, target?: Component) {
        const timer = target || mc.app
        timer.scheduleOnce(cb, delay * 0.001)
        return cb
    }

    public clearTimeout(cb: Function, target?: Component) {
        const timer = target || mc.app
        timer.unschedule(cb)
    }

    // 等待下一帧
    public async waitNextFrame(frams?: number, target?: Component): Promise<void> {
        frams = Math.max(1, frams || 1)
        return new Promise(resolve => {
            const timer = target || mc.app
            function callback() {
                frams -= 1
                if (frams <= 0) {
                    timer.unschedule(callback)
                    resolve()
                }
            }
            timer.schedule(callback, 0)
        })
    }

    // 锁
    public async lock(tag: string, waitInterval: number = 0.1) {
        while (this._lock_map[tag]) {
            await this.wait(waitInterval)
        }
        this._lock_map[tag] = true
    }

    public unlock(tag: string) {
        delete this._lock_map[tag]
    }

    // 根据字符串切换颜色
    public colorFromHEX(hex: string): Color {
        return Color.WHITE.fromHEX(hex)
    }

    // 生成唯一ID
    public UID(): string {
        let now = Date.now()
        let id = now * 1000 + 1
        if (now !== this._last_now) {
            this._last_now = now
            this._accumulation = 0
        } else if (this._accumulation >= 999) {
            this._last_now = now + 1
            this._accumulation = 0
            id = this._last_now * 1000 + 1
        } else {
            this._accumulation += 1
            id += this._accumulation
        }
        return id + ''
    }

    // 是否对象
    public isObject(o: any) {
        return Object.prototype.toString.call(o) === '[object Object]'
    }

    // 判断对象是否空对象{}
    public isEmptyObject(o: any) {
        if (!o) {
            return true
        }
        for (let k in o) {
            return false
        }
        return true
    }

    // 拷贝对象
    public cloneObject(obj: any) {
        let ret = {}
        for (let k in obj) {
            ret[k] = obj[k]
        }
        return ret
    }

    // 深度拷贝对象
    public deepClone(obj: any, inDeep: boolean = false): any {
        if (!obj) {
            return null
        } else if (!inDeep) {
            this._clone_cache = []
        }
        let objClone = Array.isArray(obj) ? [] : {}
        if (obj && typeof obj === 'object') {
            for (let key in obj) {
                if (!obj.hasOwnProperty(key)) {
                    continue
                }
                //判断ojb子元素是否为对象，如果是，递归复制
                let value = obj[key]
                if (value && typeof value === 'object') {
                    if (this._clone_cache.indexOf(value) === -1) {
                        this._clone_cache.push(value)
                        objClone[key] = this.deepClone(value, true)
                    }
                } else {
                    objClone[key] = value //如果不是，简单复制
                }
            }
        }
        if (!inDeep) {
            this._clone_cache = null
        }
        return objClone
    }

    // 深度比较两个对象是否相等
    public compareObject(x: any, y: any, leftChain = [], rightChain = []) {
        if (isNaN(x) && isNaN(y) && typeof x === 'number' && typeof y === 'number') {
            return true //如果都是NaN 直接返回
        } else if (x === y) {
            return true  //一样直接返回
            // } else if ((typeof x === 'function' && typeof y === 'function') ||
            //     (x instanceof Date && y instanceof Date) ||
            //     (x instanceof RegExp && y instanceof RegExp) ||
            //     (x instanceof String && y instanceof String) ||
            //     (x instanceof Number && y instanceof Number)) {
            //     return x.toString() === y.toString()  //如果是方法
        } else if (!(x instanceof Object && y instanceof Object)) {
            return false
        } else if (x.isPrototypeOf(y) || y.isPrototypeOf(x)) {
            return false
        } else if (x.constructor !== y.constructor) {
            return false
        } else if (x.prototype !== y.prototype) {
            return false
        } else if (leftChain.indexOf(x) > -1 || rightChain.indexOf(y) > -1) {
            return false // Check for infinitive linking loops
        }

        let p: any
        // Quick checking of one object being a subset of another.
        // todo: cache the structure of arguments[0] for performance
        for (p in y) {
            if (y[p] === undefined) {
                continue
            }
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false
            } else if (typeof y[p] !== typeof x[p]) {
                return false
            }
        }
        for (p in x) {
            if (x[p] === undefined) {
                continue
            }
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false
            } else if (typeof y[p] !== typeof x[p]) {
                return false
            }
            const tf = typeof (x[p])
            if (tf === 'object' || tf === 'function') {
                leftChain.push(x)
                rightChain.push(y)
                if (!this.compareObject(x[p], y[p], leftChain, rightChain)) {
                    return false
                }
                leftChain.pop()
                rightChain.pop()
            } else if (x[p] !== y[p]) {
                return false
            }
        }
        return true
    }

    // 循环值
    public loopValue(index: number, len: number) {
        index = index % len
        if (index < 0) {
            return index + len
        }
        return index
    }

    // 组装列表
    public items(arr: Node[], datas: any[], item: Node | Prefab, parent: Node, cb: Function) {
        let i = 0, len = datas.length
        for (let l = arr.length; i < l; i++) {
            if (i < len) {
                cb(arr[i], datas[i], i)
            } else {
                arr[i].active = false
            }
        }
        for (; i < len; i++) {
            const it = mc.instantiate(item, parent)
            cb(it, datas[i], i)
            arr.push(it)
        }
    }

    // 设置屏幕常亮
    public setKeepScreenOn(val: boolean) {
        // @ts-ignore
        CC_JSB && jsb.Device.setKeepScreenOn(val)
    }

    public boolToNumber(val: boolean) {
        return val ? 1 : 0
    }

    // 对象给对象赋值
    public setValue(fields: string, data: any, target?: any) {
        target = target || {}
        fields.split('|').forEach(m => target[m] = data[m])
        return target
    }

    // http请求
    public async httpRequest(method: string, url: string, data?: any) {
        return new Promise<{ status: number, data?: any }>(resolve => {
            method = method.toUpperCase()
            if (method !== 'POST' && method !== 'GET') {
                logger.info('http method error')
                return resolve({ status: 0 })
            }
            logger.info('http request method=' + method + ', url=' + url)
            const xhr = new XMLHttpRequest()
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    let json = null
                    if (xhr.responseText) {
                        try {
                            json = JSON.parse(xhr.responseText)
                        } catch (error) {
                        }
                    }
                    resolve({ status: 200, data: json })
                }
            }
            xhr.timeout = 5000
            xhr.ontimeout = function (e) {
                logger.info('http timeout')
                resolve({ status: 1 })
            }
            xhr.onerror = function (e) {
                logger.info('http disconnect')
                resolve({ status: 2 })
            }
            xhr.open(method, url, true)
            if (method === 'POST' && data) {
                xhr.setRequestHeader("Content-Type", "application/json")
                xhr.send(JSON.stringify(data))
            } else {
                xhr.send(null)
            }
        })
    }

    // 判断是否是ios
    public isIos() {
        return sys.os === sys.OS.IOS
    }

    // 判断是否是安卓
    public isAndroid() {
        return sys.os === sys.OS.ANDROID
    }

    // 是否手机平台
    public isMobile() {
        return sys.isNative && (this.isIos() || this.isAndroid())
    }

    // 是否微信游戏
    public isWechatGame() {
        return sys.platform === sys.Platform.WECHAT_GAME
    }

    // 是否qq游戏
    public isQQGame() {
        return typeof qq !== 'undefined'
    }

    // 判断是否是小程序
    public isMiniGame() {
        return this.isWechatGame() || this.isQQGame()
    }

    // 随机字符串
    public getRandomString(len: number = 8): string {
        const $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
        const maxPos = $chars.length
        let str = ''
        for (let i = 0; i < len; i++) {
            str += $chars.charAt(Math.floor(Math.random() * maxPos))
        }
        return str
    }

    // 创建一个数组
    public newArray(count: number, val?: any) {
        return new Array(count).fill(val)
    }

    // 同步锁 同时调用的时候 只会执行第一个
    public syncLock(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor {
        let key = `__lock_${propertyName}`
        if (target && !propertyName) {
            key = target as string
        }
        const method = propertyDescriptor.value
        propertyDescriptor.value = async function (...args: any[]) {
            if (this[key]) {
                return null
            }
            this[key] = true
            const result = await method.apply(this, args)
            this[key] = false
            return result
        }
        return propertyDescriptor
    }

    // 同步等待 同时调用多个的时候 会等待上一个完成后继续下一个
    public syncWait(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor {
        let key = `__sync_wait_${propertyName}`
        if (target && !propertyName) {
            key = target as string
        }
        const method = propertyDescriptor.value
        async function func(self: any, list: any[]) {
            if (list.length === 0) {
                return
            }
            const it = list[0]
            const result = await method.apply(self, it.args)
            it.resolve(result)
            list.shift() //删除第一个
            if (list.length > 0) {
                func(this, list)
            }
        }
        propertyDescriptor.value = async function (...args: any[]) {
            return new Promise(resolve => {
                let list: any[] = this[key]
                if (!list) {
                    list = this[key] = []
                }
                list.push({ resolve: resolve, args: args })
                if (list.length === 1) {
                    func(this, list)
                }
            })
        }
        return propertyDescriptor
    }

    // 获取浏览器参数
    public getBrowserParams() {
        if (!sys.isBrowser) {
            return {}
        }
        const obj = {}
        location?.search?.replace('?', '').split('&').forEach(m => {
            const [key, val] = m.split('=')
            if (key) {
                obj[key] = val
            }
        })
        return obj
    }

    public getBrowserParamByKey(key: string) {
        const obj = this.getBrowserParams()
        return obj?.[key] ?? ''
    }

    // 是否在多边形里面
    public isInPolygon(point: Vec2, polygonPoints: Vec2[]) {
        let counter = 0, i: number, xinters: number
        let p1: Vec2, p2: Vec2
        let pointCount = polygonPoints.length
        p1 = polygonPoints[0]
        for (i = 1; i <= pointCount; i++) {
            p2 = polygonPoints[i % pointCount]
            if (point.x > Math.min(p1.x, p2.x) && point.x <= Math.max(p1.x, p2.x)) {
                if (point.y <= Math.max(p1.y, p2.y)) {
                    if (p1.x != p2.x) {
                        xinters = (point.x - p1.x) * (p2.y - p1.y) / (p2.x - p1.x) + p1.y
                        if (p1.y == p2.y || point.y <= xinters) {
                            counter++
                        }
                    }
                }
            }
            p1 = p2
        }
        return (counter & 1) !== 0
    }

    // 检测版本 a >= b 为true，反之则为false
    public checkVersion(a: string, b: string) {
        const vA = this.stringToNumbers(a, '.')
        const vB = this.stringToNumbers(b, '.')
        const vALen = vA.length
        const vBLen = vB.length
        for (let i = 0; i < vALen; i++) {
            let a = vA[i], b = 0
            if (i < vBLen) {
                b = vB[i]
            }
            if (a < b) {
                return false
            } else if (a > b) {
                break
            }
        }
        return true
    }

    // 切换到前台事件
    public waitGameShow(): Promise<void> {
        return new Promise(resolve => {
            const cb = () => {
                game.off(Game.EVENT_SHOW, cb)
                resolve()
            }
            game.on(Game.EVENT_SHOW, cb)
        })
    }

    // 查找节点
    public async findNode(path: string, count: number = 30) {
        let node = mc.app, cnt = 0, it: Node = null
        while (true) {
            it = node.FindChild(path)
            if (it && it.active) {
                break
            }
            await this.wait(0.2)
            if (cnt++ > count) {
                break
            }
        }
        return it
    }

    // 消耗节点
    public destroyNode(node: Node) {
        if (node && isValid(node)) {
            node.destroy()
            return true
        }
        return false
    }

    // 时间差
    public timediff(start: number, date: string) {
        let [year, month, day, h, m, s] = date.split('-')
        h = h || '00'
        m = m || '00'
        s = s || '00'
        let time = new Date(`${year}/${month}/${day} ${h}:${m}:${s}`).getTime()
        // time -= 8 * 3600 * 1000// 东八区时区矫正
        return time - start
    }

    // 将number精度一下
    public numberFixed(val: number, fractionDigits: number = 4) {
        return parseFloat(val.toFixed(fractionDigits))
    }
}

window['ut'] = new Utils()