{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/view/game/DragTouchCmpt.ts"], "names": ["_decorator", "Component", "Node", "v2", "CLICK_SPACE", "g<PERSON>elper", "DragTouchType", "ccclass", "property", "DragTouchCmpt", "LONG_PRESS_INTERVAL", "interactable", "target", "isDownClick", "isFirstDrag", "isCanDrag", "touchEvent", "clickTime", "_temp_vec2_1", "_temp_vec2_2", "init", "node", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "TOUCH_CANCEL", "SetSwallowTouches", "clean", "off", "setCanDrag", "val", "event", "clickTouchId", "getID", "startLocation", "getStartLocation", "location", "getLocation", "mag", "subtract", "length", "onDragBegin", "onTouchEvent", "DRAG_MOVE", "DRAG_END", "CLICK", "console", "log", "DRAG_BEGIN", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;;AACzCC,MAAAA,W,iBAAAA,W;;AAEAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,a,iBAAAA,a;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;AAE9B;AACA;AACA;;yBAEqBS,a,GADpBF,O,UAAD,MACqBE,aADrB,SAC2CR,SAD3C,CACqD;AAAA;AAAA;AAAA,eAEhCS,mBAFgC,GAEF,GAFE;AAAA,eAI1CC,YAJ0C,GAIlB,IAJkB;AAAA,eAMzCC,MANyC,GAMnB,IANmB;AAAA,eAOzCC,WAPyC,GAOlB,KAPkB;AAOZ;AAPY,eAQzCC,WARyC,GAQlB,KARkB;AAQZ;AARY,eASzCC,SATyC,GASpB,KAToB;AASd;AATc,eAUzCC,UAVyC,GAUhB,IAVgB;AAAA,eAWzCC,SAXyC,GAWrB,CAXqB;AAAA,eAazCC,YAbyC,GAapBf,EAAE,EAbkB;AAAA,eAczCgB,YAdyC,GAcpBhB,EAAE,EAdkB;AAAA;;AAgB1CiB,QAAAA,IAAI,CAACR,MAAD,EAAsB;AAC7B,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKS,IAAL,CAAUC,EAAV,CAAapB,IAAI,CAACqB,SAAL,CAAeC,WAA5B,EAAyC,KAAKC,YAA9C,EAA4D,IAA5D;AACA,eAAKJ,IAAL,CAAUC,EAAV,CAAapB,IAAI,CAACqB,SAAL,CAAeG,UAA5B,EAAwC,KAAKC,WAA7C,EAA0D,IAA1D;AACA,eAAKN,IAAL,CAAUC,EAAV,CAAapB,IAAI,CAACqB,SAAL,CAAeK,SAA5B,EAAuC,KAAKC,UAA5C,EAAwD,IAAxD;AACA,eAAKR,IAAL,CAAUC,EAAV,CAAapB,IAAI,CAACqB,SAAL,CAAeO,YAA5B,EAA0C,KAAKD,UAA/C,EAA2D,IAA3D;AACA,eAAKR,IAAL,CAAUU,iBAAV,CAA4B,KAA5B,EAN6B,CAMM;;AACnC,iBAAO,IAAP;AACH;;AAEMC,QAAAA,KAAK,GAAG;AACX,eAAKX,IAAL,CAAUY,GAAV,CAAc/B,IAAI,CAACqB,SAAL,CAAeC,WAA7B,EAA0C,KAAKC,YAA/C,EAA6D,IAA7D;AACA,eAAKJ,IAAL,CAAUY,GAAV,CAAc/B,IAAI,CAACqB,SAAL,CAAeG,UAA7B,EAAyC,KAAKC,WAA9C,EAA2D,IAA3D;AACA,eAAKN,IAAL,CAAUY,GAAV,CAAc/B,IAAI,CAACqB,SAAL,CAAeK,SAA7B,EAAwC,KAAKC,UAA7C,EAAyD,IAAzD;AACA,eAAKR,IAAL,CAAUY,GAAV,CAAc/B,IAAI,CAACqB,SAAL,CAAeO,YAA7B,EAA2C,KAAKD,UAAhD,EAA4D,IAA5D;AACA,eAAKjB,MAAL,GAAc,IAAd;AACH,SAhCgD,CAkCjD;;;AACOsB,QAAAA,UAAU,CAACC,GAAD,EAAe;AAC5B,eAAKpB,SAAL,GAAiBoB,GAAjB;AACA,iBAAO,IAAP;AACH,SAtCgD,CAwCjD;;;AACQV,QAAAA,YAAY,CAACW,KAAD,EAAoB;AACpC,cAAI,CAAC,KAAKzB,YAAN,IAAsB;AAAA;AAAA,kCAAQ0B,YAAR,KAAyB,CAAC,CAApD,EAAuD;AACnD;AACH;;AACD;AAAA;AAAA,kCAAQA,YAAR,GAAuBD,KAAK,CAACE,KAAN,EAAvB;AACA,eAAKzB,WAAL,GAAmB,IAAnB;AACA,eAAKC,WAAL,GAAmB,KAAnB;;AACA,cAAI,KAAKC,SAAT,EAAoB;AAChB,iBAAKC,UAAL,GAAkBoB,KAAlB;AACA,iBAAKnB,SAAL,GAAiB,KAAKP,mBAAtB;AACH,WAHD,MAGO;AACH,iBAAKM,UAAL,GAAkB,IAAlB;AACA,iBAAKC,SAAL,GAAiB,CAAjB;AACH;AACJ;;AAEOU,QAAAA,WAAW,CAACS,KAAD,EAAoB;AACnC,cAAI,CAAC,KAAKzB,YAAN,IAAsB;AAAA;AAAA,kCAAQ0B,YAAR,KAAyBD,KAAK,CAACE,KAAN,EAAnD,EAAkE;AAC9D;AACH,WAFD,MAEO,IAAI,CAAC,KAAKvB,SAAV,EAAqB;AACxB;AACH;;AACD,cAAMwB,aAAa,GAAGH,KAAK,CAACI,gBAAN,CAAuB,KAAKtB,YAA5B,CAAtB;AACA,cAAMuB,QAAQ,GAAGL,KAAK,CAACM,WAAN,CAAkB,KAAKvB,YAAvB,CAAjB;AACA,cAAMwB,GAAG,GAAGJ,aAAa,CAACK,QAAd,CAAuBH,QAAvB,EAAiCI,MAAjC,EAAZ;;AACA,cAAIF,GAAG,GAAG,CAAV,EAAa;AACT,gBAAI,CAAC,KAAK7B,WAAV,EAAuB;AACnB,mBAAKgC,WAAL,CAAiBV,KAAjB;AACH;;AACD,iBAAKxB,MAAL,CAAYmC,YAAZ,CAAyB;AAAA;AAAA,gDAAcC,SAAvC,EAAkDZ,KAAlD;AACH;AACJ;;AAEOP,QAAAA,UAAU,CAACO,KAAD,EAAoB;AAClC,cAAI;AAAA;AAAA,kCAAQC,YAAR,KAAyBD,KAAK,CAACE,KAAN,EAAzB,IAA0C,CAAC,KAAK3B,YAApD,EAAkE;AAC9D;AACH;;AACD;AAAA;AAAA,kCAAQ0B,YAAR,GAAuB,CAAC,CAAxB;AACA,eAAKrB,UAAL,GAAkB,IAAlB;AACA,eAAKC,SAAL,GAAiB,CAAjB;;AACA,cAAI,KAAKF,SAAL,IAAkB,KAAKD,WAA3B,EAAwC;AACpC,iBAAKF,MAAL,CAAYmC,YAAZ,CAAyB;AAAA;AAAA,gDAAcE,QAAvC,EAAiDb,KAAjD;AACH,WAFD,MAEO,IAAI,KAAKvB,WAAT,EAAsB;AACzB,gBAAM0B,aAAa,GAAGH,KAAK,CAACI,gBAAN,CAAuB,KAAKtB,YAA5B,CAAtB;AACA,gBAAMuB,QAAQ,GAAGL,KAAK,CAACM,WAAN,CAAkB,KAAKvB,YAAvB,CAAjB;AACA,gBAAMwB,GAAG,GAAGJ,aAAa,CAACK,QAAd,CAAuBH,QAAvB,EAAiCI,MAAjC,EAAZ;;AACA,gBAAIF,GAAG;AAAA;AAAA,2CAAP,EAAwB;AACpB,qBAAO,KAAK/B,MAAL,CAAYmC,YAAZ,CAAyB;AAAA;AAAA,kDAAcG,KAAvC,EAA8Cd,KAA9C,CAAP;AACH;AACJ,WAPM,MAOA;AACHe,YAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACH;AACJ,SA7FgD,CA+FjD;;;AACQN,QAAAA,WAAW,CAACV,KAAD,EAAoB;AACnC,eAAKpB,UAAL,GAAkB,IAAlB;AACA,eAAKC,SAAL,GAAiB,CAAjB;AACA,eAAKH,WAAL,GAAmB,IAAnB;AACA,eAAKD,WAAL,GAAmB,KAAnB;AACA,eAAKD,MAAL,CAAYmC,YAAZ,CAAyB;AAAA;AAAA,8CAAcM,UAAvC,EAAmDjB,KAAnD;AACH;;AAEDkB,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,cAAI,CAAC,KAAKxC,SAAN,IAAmB,CAAC,KAAKC,UAAzB,IAAuC,KAAKF,WAA5C,IAA2D,KAAKG,SAAL,IAAkB,CAAjF,EAAoF;AAChF;AACH;;AACD,eAAKA,SAAL,IAAkBsC,EAAlB;;AACA,cAAI,KAAKtC,SAAL,IAAkB,CAAtB,EAAyB;AACrB,iBAAK6B,WAAL,CAAiB,KAAK9B,UAAtB;AACH;AACJ;;AAhHgD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, Node, v2, Vec2 } from \"cc\";\r\nimport { CLICK_SPACE } from \"../../common/constant/Constant\";\r\nimport { IDragTarget } from \"../../common/constant/interface\";\r\nimport { gHelper } from \"../../common/helper/GameHelper\";\r\nimport { DragTouchType } from \"../../common/constant/Enums\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 用于拖动的组建\r\n */\r\n@ccclass\r\nexport default class DragTouchCmpt extends Component {\r\n\r\n    private readonly LONG_PRESS_INTERVAL: number = 0.5\r\n\r\n    public interactable: boolean = true\r\n\r\n    private target: IDragTarget = null\r\n    private isDownClick: boolean = false //是否按下点击\r\n    private isFirstDrag: boolean = false //是否首次拖动\r\n    private isCanDrag: boolean = false //是否可以拖动\r\n    private touchEvent: EventTouch = null\r\n    private clickTime: number = 0\r\n\r\n    private _temp_vec2_1: Vec2 = v2()\r\n    private _temp_vec2_2: Vec2 = v2()\r\n\r\n    public init(target: IDragTarget) {\r\n        this.target = target\r\n        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this)\r\n        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this)\r\n        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this)\r\n        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this)\r\n        this.node.SetSwallowTouches(false) //默认开启穿透\r\n        return this\r\n    }\r\n\r\n    public clean() {\r\n        this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this)\r\n        this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this)\r\n        this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this)\r\n        this.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this)\r\n        this.target = null\r\n    }\r\n\r\n    // 设置是否可以拖动\r\n    public setCanDrag(val: boolean) {\r\n        this.isCanDrag = val\r\n        return this\r\n    }\r\n\r\n    // 触摸开始\r\n    private onTouchStart(event: EventTouch) {\r\n        if (!this.interactable || gHelper.clickTouchId !== -1) {\r\n            return\r\n        }\r\n        gHelper.clickTouchId = event.getID()\r\n        this.isDownClick = true\r\n        this.isFirstDrag = false\r\n        if (this.isCanDrag) {\r\n            this.touchEvent = event\r\n            this.clickTime = this.LONG_PRESS_INTERVAL\r\n        } else {\r\n            this.touchEvent = null\r\n            this.clickTime = 0\r\n        }\r\n    }\r\n\r\n    private onTouchMove(event: EventTouch) {\r\n        if (!this.interactable || gHelper.clickTouchId !== event.getID()) {\r\n            return\r\n        } else if (!this.isCanDrag) {\r\n            return\r\n        }\r\n        const startLocation = event.getStartLocation(this._temp_vec2_1)\r\n        const location = event.getLocation(this._temp_vec2_2)\r\n        const mag = startLocation.subtract(location).length()\r\n        if (mag > 4) {\r\n            if (!this.isFirstDrag) {\r\n                this.onDragBegin(event)\r\n            }\r\n            this.target.onTouchEvent(DragTouchType.DRAG_MOVE, event)\r\n        }\r\n    }\r\n\r\n    private onTouchEnd(event: EventTouch) {\r\n        if (gHelper.clickTouchId !== event.getID() || !this.interactable) {\r\n            return\r\n        }\r\n        gHelper.clickTouchId = -1\r\n        this.touchEvent = null\r\n        this.clickTime = 0\r\n        if (this.isCanDrag && this.isFirstDrag) {\r\n            this.target.onTouchEvent(DragTouchType.DRAG_END, event)\r\n        } else if (this.isDownClick) {\r\n            const startLocation = event.getStartLocation(this._temp_vec2_1)\r\n            const location = event.getLocation(this._temp_vec2_2)\r\n            const mag = startLocation.subtract(location).length()\r\n            if (mag <= CLICK_SPACE) {\r\n                return this.target.onTouchEvent(DragTouchType.CLICK, event)\r\n            }\r\n        } else {\r\n            console.log('onTouchEnd none!')\r\n        }\r\n    }\r\n\r\n    // 拖动开始\r\n    private onDragBegin(event: EventTouch) {\r\n        this.touchEvent = null\r\n        this.clickTime = 0\r\n        this.isFirstDrag = true\r\n        this.isDownClick = false\r\n        this.target.onTouchEvent(DragTouchType.DRAG_BEGIN, event)\r\n    }\r\n\r\n    update(dt: number) {\r\n        if (!this.isCanDrag || !this.touchEvent || this.isFirstDrag || this.clickTime <= 0) {\r\n            return\r\n        }\r\n        this.clickTime -= dt\r\n        if (this.clickTime <= 0) {\r\n            this.onDragBegin(this.touchEvent)\r\n        }\r\n    }\r\n}"]}