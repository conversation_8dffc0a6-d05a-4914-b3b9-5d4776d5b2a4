import { _decorator, Component, EventTouch, Label, Node, v2, v3, Vec2, Vec3 } from "cc";
import FrameAnimationCmpt from "../cmpt/FrameAnimationCmpt";
import { getHeroFrameAnimConf } from "../../common/config/HeroFrameAnimConf";
import GameModel from "../../model/game/GameModel";
import EventType from "../../common/event/EventType";
import AttrBarCmpt from "./AttrBarCmpt";
import HeroObj from "../../model/game/HeroObj";
import { AreaType, DragTouchType, HeroState } from "../../common/constant/Enums";
import DragTouchCmpt from "./DragTouchCmpt";
import { PC_TOUCH_HEIGHT } from "../../common/constant/Constant";

const { ccclass, property } = _decorator;

// 一个英雄
@ccclass
export default class HeroCmpt extends Component {

    private key: string = ''

    public data: HeroObj = null
    private body: Node = null
    private animNode: Node = null
    private animCmpt: FrameAnimationCmpt = null
    private touchCmpt: DragTouchCmpt = null
    private currAnimName: string = ''
    private attrBar: AttrBarCmpt = null

    private preStateUid: string = ''
    private tempPosition: Vec3 = v3()
    private originalPosition: Vec3 = v3()
    private dragBeginLocation: Vec2 = v2() //拖动开始的坐标

    private _temp_vec2_1: Vec2 = v2()

    public async init(data: HeroObj, pos: Vec3, key: string) {
        this.data = data
        this.key = key
        this.originalPosition.set(pos)
        this.node.setPosition(pos)
        this.body = this.FindChild('body')
        this.animNode = this.FindChild('body/anim')
        this.animCmpt = this.animNode.getComponent(FrameAnimationCmpt)
        await this.animCmpt.init(getHeroFrameAnimConf(data.getViewId()), key)
        if (this.isValid) {
            this.animNode.setPosition(this.animCmpt.getAnimConfPositionOffset()) //设置偏移
            this.animCmpt.setUpdateModel(GameModel.ins())
            this.touchCmpt = this.FindChild('touch').addComponent(DragTouchCmpt).init(this).setCanDrag(data.isCanDrag())
            this.Child('lv', Label).string = data.lv + ''
            this.playAnimation('idle')
            this.loadAttrBar()
        }
        return this
    }

    public resync(data: any, pos: Vec3) {
        this.data.init(data)
        this.originalPosition.set(pos)
        this.node.setPosition(pos)
        this.node.zIndex = 0
        this.body.Child('shadow').active = true
        this.playAnimation('idle')
        this.touchCmpt.setCanDrag(this.data.isCanDrag())
        this.loadAttrBar()
        return this
    }

    public clean(release?: boolean) {
        this.animCmpt.clean()
        this.node.destroy()
        release && assetsMgr.releaseTempRes(this.data?.getPrefabUrl(), this.key)
        this.data = null
    }

    private async loadAttrBar() {
        // const it = await nodePoolMgr.get('animal/ATTR_BAR', this.key)
        // if (!this.isValid || !this.data) {
        //     return nodePoolMgr.put(it)
        // }
        // it.parent = this.node
        // it.sortIndex = 10
        // it.active = true
        // this.attrBar = it.getComponent(AttrBarCmpt).init(this.data)
    }

    public get uid(): string { return this.data?.uid || '' }
    public get areaType(): AreaType { return this.data?.areaType }

    public getBody() {
        return this.body
    }

    public getTempPosition() {
        return this.node.getPosition(this.tempPosition)
    }

    // 触摸事件
    public onTouchEvent(type: DragTouchType, event: EventTouch) {
        // console.log('onTouchEvent', DragTouchType[type], event)
        if (type === DragTouchType.DRAG_BEGIN) {
            this.node.y = this.originalPosition.y + PC_TOUCH_HEIGHT
            event.getUILocation(this.dragBeginLocation)
            this.node.zIndex = 10
            this.body.Child('shadow').active = false
            this.playAnimation('caught')
            eventCenter.emit(EventType.DRAG_HERO_BEGIN, this)
        } else if (type === DragTouchType.DRAG_MOVE) {
            const location = event.getUILocation(this._temp_vec2_1)
            const diff = location.subtract(this.dragBeginLocation).add(this.originalPosition.toVec2())
            this.node.setPosition(diff.x, diff.y + PC_TOUCH_HEIGHT)
            eventCenter.emit(EventType.DRAG_HERO_MOVE, this)
        } else if (type === DragTouchType.DRAG_END) {
            eventCenter.emit(EventType.DRAG_HERO_END, this)
        } else if (type === DragTouchType.CLICK) {
            console.log('click!', this.data)
        }
    }

    // 还原位置
    public restorePosition() {
        if (!this.isValid) {
            return
        }
        this.node.zIndex = 0
        this.node.setPosition(this.originalPosition)
        this.body.Child('shadow').active = true
        this.playAnimation('idle')
    }

    update(dt: number) {
        if (!this.data) {
            return
        }
        this.updateState()
    }

    // 播放动画
    public playAnimation(name: string, cb?: Function, startTime?: number) {
        this.currAnimName = name
        this.animCmpt?.play(name, cb, startTime)
    }

    // 同步状态信息
    private updateState() {
        if (!this.data?.state || this.preStateUid === this.data.state.uid) {
            return
        }
        this.preStateUid = this.data.state.uid
        const state = this.data.state.type, data = this.data.state.data
        // cc.log('updateState', this.uid, this.point.ID(), HeroState[state])
        // this.data.actioning = this.data.actioning || (state !== HeroState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动
        if (state === HeroState.STAND) { //待机
            this.doStand()
        } else if (state === HeroState.ATTACK) { //攻击
            this.doAttack(data)
        } else if (state === HeroState.HIT) { //受击
            this.doHit(data)
        } else {
            this.playAnimation('idle')
        }
    }

    // 待机
    private doStand() {
        const animName = this.animCmpt?.playAnimName
        if (animName === 'move' || animName === 'move_pull') { //只有移动的时候才强行切换成idle
            this.playAnimation('idle')
        }
        this.attrBar?.reset()
    }

    // 攻击
    private doAttack(data: any) {
        const currAttackTime = data.currAttackTime ?? 0
        const suffix = data.instabilityAttackIndex || ''
        this.playAnimation('attack' + suffix, () => this.isValid && this.playAnimation('idle'), currAttackTime)
    }

    // 受击
    private doHit(data: any) {
        let damage = data.damage ?? 0
        const isDie = this.data.isDie()
        const sound = data.sound //受击音效
        const uid = this.uid
        this.attrBar?.play()
        if (damage === 0) {
            return this.playAnimation('idle')
        }
        let animName = 'hit'
        if (isDie) {
            animName = 'die'
            // this.playSFXByKey('die_sound')
        } else if (sound) {
            // this.playSFX(sound)
        }
        this.playAnimation(animName, () => {
            if (isDie) {
                eventCenter.emit(EventType.REMOVE_HERO, uid, false)
            } else if (this.isValid) {
                this.playAnimation('idle')
            }
        })
    }

    // 直接死亡
    private doDie(data: any) {

    }
}