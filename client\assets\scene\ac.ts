import { sys } from "cc"

const NAME_SPACE = 'ac'

const VERSION = '1.0.0'
const APP_TYPE = 'global' //global, inland

let _lang = '' //语言
let _app_type = ''
let _server_area = '' // 服务器区域 china, hk

function getAppType() {
    if (_app_type) {
        return _app_type
    } else if (sys.isBrowser) {
        location?.search?.replace('?', '').split('&').forEach(m => {
            const [key, val] = m.split('=')
            if (key === 'p') {
                _app_type = val.startsWith('g') ? 'global' : 'inland'
            }
        })
    }
    if (!_app_type) {
        _app_type = APP_TYPE
    }
    return _app_type
}

function getSaveServerAreaKey() {
    return '__' + NAME_SPACE + '_server_area__'
}

function getSaveLangKey() {
    return '__' + NAME_SPACE + '_lang__'
}

// 获取服务器区域列表
function getServerAreaList() {
    if (getAppType() === 'inland') {
        return ['china']
    }
    return ['hk'/* , 'us' */]
}

// 获取服务器区域
function getServerArea() {
    if (_server_area) {
        return _server_area
    } else if (getAppType() === 'inland') {
        _server_area = 'china'
        return _server_area
    }
    _server_area = localStorage.getItem(getSaveServerAreaKey()) || ''
    if (!_server_area) { //如果没有 就根据语言给他分配一个
        const localizeList = [
            { //亚服
                area: 'hk', codeList: [
                    'zh-tw',
                    'zh_tw',
                    'zh',
                    'zh-sg',
                    'zh_sg',
                    'zh-hant',
                    'zh-mo',
                    'zh_cn_#hant',
                    'zh_hant',
                    'zh_mo',
                    'zh_hk',
                    'zh-hk',
                    'ja',
                    'ko',
                    'en-PH',
                    'tl-PH',
                    'id',
                    'th',
                    'ms',
                    'vi',
                ]
            },
            { //美服
                area: 'us', codeList: [
                    'en-US',
                    'en-CA',
                    'fr-CA',
                ]
            },
        ]
        const langugeCode = sys.languageCode
        for (let val of localizeList) {
            if (val.codeList.some(m => langugeCode.startsWith(m))) {
                _server_area = val.area
                break
            }
        }
        _server_area = _server_area || 'hk' //默认香港服
        localStorage.setItem(getSaveServerAreaKey(), _server_area)
    }
    const serverAreaList = getServerAreaList()
    if (serverAreaList.indexOf(_server_area) === -1) {
        _server_area = serverAreaList[0]
        localStorage.setItem(getSaveServerAreaKey(), _server_area)
    }
    return _server_area
}

function setServerArea(val: string) {
    if (_server_area === val) {
        return
    } else if (getServerAreaList().indexOf(val) === -1) {
        return
    }
    _server_area = val
    localStorage.setItem(getSaveServerAreaKey(), _server_area)
}

// 获取本地语言
function getLocalizeLanByCode() {
    if (getAppType() === 'inland') {
        return 'cn'
    }
    const localizeList = [
        { localize: 'hk', codeList: ['zh-hant', 'zh-mo', 'zh_cn_#hant', 'zh_hant', 'zh_mo', 'zh_hk', 'zh-hk'] }, //香港
        { localize: 'tw', codeList: ['zh-tw', 'zh_tw'] }, //台湾
        { localize: 'cn', codeList: ['zh', 'zh-cn', 'zh-sg', 'zh_sg'] }, //简体
        { localize: 'jp', codeList: ['ja'] }, //日语
        { localize: 'kr', codeList: ['ko'] }, //韩语
        { localize: 'idl', codeList: ['id'] }, //印尼
        { localize: 'th', codeList: ['th'] }, //泰国
        { localize: 'vi', codeList: ['vi'] }, //越南
        // { localize: 'en', codeList: ['en', 'en-US'] }, //英语
        // { localize: 'ms', codeList: ['ms'] }, //马来西亚
    ]
    let lang = localStorage.getItem(getSaveLangKey()) || ''
    if (lang === 'en' || localizeList.some(m => m.localize === lang)) {
        return lang
    }
    const langugeCode = sys.languageCode
    for (let val of localizeList) {
        if (val.codeList.some(m => langugeCode.startsWith(m))) {
            lang = val.localize
            break
        }
    }
    return lang || 'en' //默认英文
}

function getLocalLang() {
    if (!_lang) {
        _lang = getLocalizeLanByCode()
        localStorage.setItem(getSaveLangKey(), _lang)
    }
    return _lang
}

export default {
    NAME_SPACE,
    VERSION,
    getAppType,
    getLocalLang,
    getServerAreaList,
    getServerArea,
    setServerArea,
}

// Language string Description
// af Afrikaans
// ar-ae Arabic (U.A.E.)
// ar-bh Arabic (Kingdom of Bahrain)
// ar-dz Arabic (Algeria)
// ar-eg Arabic (Egypt)
// ar-iq Arabic (Iraq)
// ar-jo Arabic (Jordan)
// ar-kw Arabic (Kuwait)
// ar-lb Arabic (Lebanon)
// ar-ly Arabic (Libya)
// ar-ma Arabic (Morocco)
// ar-om Arabic (Oman)
// ar-qa Arabic (Qatar)
// ar-sa Arabic (Saudi Arabia)
// ar-sy Arabic (Syria)
// ar-tn Arabic (Tunisia)
// ar-ye Arabic (Yemen)
// ar Arabic
// as Assamese
// az Azerbaijani
// be Belarusian
// bg Bulgarian
// bn Bangla
// ca Catalan
// cs Czech
// da Danish
// de-at German (Austria)
// de-ch German (Switzerland)
// de-li German (Liechtenstein)
// de-lu German (Luxembourg)
// de German (Germany)
// div Divehi
// el Greek
// en-au English (Australia)
// en-bz English (Belize)
// en-ca English (Canada)
// en-gb English (United Kingdom)
// en-ie English (Ireland)
// en-jm English (Jamaica)
// en-nz English (New Zealand)
// en-ph English (Philippines)
// en-tt English (Trinidad)
// en-us English (United States)
// en-za English (South Africa)
// en-zw English (Zimbabwe)
// en English
// es-ar Spanish (Argentina)
// es-bo Spanish (Bolivia)
// es-cl Spanish (Chile)
// es-co Spanish (Colombia)
// es-cr Spanish (Costa Rica)
// es-do Spanish (Dominican Republic)
// es-ec Spanish (Ecuador)
// es-gt Spanish (Guatemala)
// es-hn Spanish (Honduras)
// es-mx Spanish (Mexico)
// es-ni Spanish (Nicaragua)
// es-pa Spanish (Panama)
// es-pe Spanish (Peru)
// es-pr Spanish (Puerto Rico)
// es-py Spanish (Paraguay)
// es-sv Spanish (El Salvador)
// es-us Spanish (United States)
// es-uy Spanish (Uruguay)
// es-ve Spanish (Venezuela)
// es Spanish
// et Estonian
// eu Basque (Basque)
// fa Persian
// fi Finnish
// fo Faeroese
// fr-be French (Belgium)
// fr-ca French (Canada)
// fr-ch French (Switzerland)
// fr-lu French (Luxembourg)
// fr-mc French (Monaco)
// fr French (France)
// gd Scottish Gaelic
// gl Galician
// gu Gujarati
// he Hebrew
// hi Hindi
// hr Croatian
// hu Hungarian
// hy Armenian
// id Indonesian
// is Icelandic
// it-ch Italian (Switzerland)
// it Italian (Italy)
// ja Japanese
// ka Georgian
// kk Kazakh
// kn Kannada
// ko Korean
// kok Konkani
// kz Kyrgyz
// lt Lithuanian
// lv Latvian
// mk Macedonian (FYROM)
// ml Malayalam
// mn Mongolian (Cyrillic)
// mr Marathi
// ms Malay
// mt Maltese
// nb-no Norwegian (Bokmal)
// ne Nepali (India)
// nl-be Dutch (Belgium)
// nl Dutch (Netherlands)
// nn-no Norwegian (Nynorsk)
// no Norwegian (Bokmal)
// or Odia
// pa Punjabi
// pl Polish
// pt-br Portuguese (Brazil)
// pt Portuguese (Portugal)
// rm Rhaeto-Romanic
// ro-md Romanian (Moldova)
// ro Romanian
// ru-md Russian (Moldova)
// ru Russian
// sa Sanskrit
// sb Sorbian
// sk Slovak
// sl Slovenian
// sq Albanian
// sr Serbian
// sv-fi Swedish (Finland)
// sv Swedish
// sw Swahili
// sx Sutu
// syr Syriac
// ta Tamil
// te Telugu
// th Thai
// tn Tswana
// tr Turkish
// ts Tsonga
// tt Tatar
// uk Ukrainian
// ur Urdu
// uz Uzbek
// vi Vietnamese
// xh Xhosa
// yi Yiddish
// zh-cn Chinese (China)
// zh-hk Chinese (Hong Kong SAR)
// zh-mo Chinese (Macao SAR)
// zh-sg Chinese (Singapore)
// zh-tw Chinese (Taiwan)
// zh Chinese
// zu Zulu