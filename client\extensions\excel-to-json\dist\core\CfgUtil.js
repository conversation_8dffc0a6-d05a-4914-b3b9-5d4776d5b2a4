"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const self = {
    cfgData: {
        excelRootPath: null,
        jsonSavePath2: null,
        jsFileName: null,
        jsonAllFileName: null,
        isMergeJson: null,
        isFormatJsCode: null,
        isFormatJson: null,
        isAutoTranslate: null,
    },
    initCfg(cb) {
        let configFilePath = this._getAppCfgPath();
        let b = fs.existsSync(configFilePath);
        if (b) {
            fs.readFile(configFilePath, 'utf-8', function (err, data) {
                if (!err) {
                    let saveData = JSON.parse(data.toString());
                    self.cfgData = saveData;
                    if (cb) {
                        cb(saveData);
                    }
                }
            });
        }
        else {
            if (cb) {
                cb({});
            }
        }
    },
    saveCfgByData(data) {
        this.cfgData.excelRootPath = data.excelRootPath;
        this.cfgData.jsonSavePath2 = data.jsonSavePath2;
        this.cfgData.jsFileName = data.jsFileName;
        this.cfgData.jsonAllFileName = data.jsonAllFileName;
        this.cfgData.isMergeJson = data.isMergeJson;
        this.cfgData.isFormatJsCode = data.isFormatJsCode;
        this.cfgData.isFormatJson = data.isFormatJson;
        this.cfgData.isAutoTranslate = data.isAutoTranslate;
        this._save();
    },
    _save() {
        let savePath = this._getAppCfgPath();
        fs.writeFileSync(savePath, JSON.stringify(this.cfgData));
    },
    _getAppCfgPath() {
        // 获取项目路径，将配置文件存储在项目目录中
        let projectPath = '';
        try {
            if (Editor && Editor.Project && Editor.Project.path) {
                projectPath = Editor.Project.path;
            }
            else {
                projectPath = process.cwd();
            }
        }
        catch (error) {
            projectPath = process.cwd();
        }
        // 在项目根目录下创建 .excel-to-json 配置目录
        const configDir = path.join(projectPath, 'local');
        if (!fs.existsSync(configDir)) {
            try {
                fs.mkdirSync(configDir, { recursive: true });
            }
            catch (error) {
                // 创建配置目录失败，使用默认路径
            }
        }
        return path.join(configDir, "excel-to-json-config.json");
    },
};
module.exports = self;
//# sourceMappingURL=data:application/json;base64,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