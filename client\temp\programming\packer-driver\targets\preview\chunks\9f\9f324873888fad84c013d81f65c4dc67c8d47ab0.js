System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Prefab, v2, v3, HeroCmpt, EventType, AnimPlayCmpt, RoleCmpt, RoleObj, vHelper, HeroObj, AreaType, MapNodeType, _class, _crd, ccclass, GameWindCtrl;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfHeroCmpt(extras) {
    _reporterNs.report("HeroCmpt", "./HeroCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameModel(extras) {
    _reporterNs.report("GameModel", "../../model/game/GameModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventType(extras) {
    _reporterNs.report("EventType", "../../common/event/EventType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAnimPlayCmpt(extras) {
    _reporterNs.report("AnimPlayCmpt", "./AnimPlayCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoleCmpt(extras) {
    _reporterNs.report("RoleCmpt", "./RoleCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoleObj(extras) {
    _reporterNs.report("RoleObj", "../../model/game/RoleObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfvHelper(extras) {
    _reporterNs.report("vHelper", "../../common/helper/ViewHelper", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHeroObj(extras) {
    _reporterNs.report("HeroObj", "../../model/game/HeroObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlayerModel(extras) {
    _reporterNs.report("PlayerModel", "../../model/game/PlayerModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAreaType(extras) {
    _reporterNs.report("AreaType", "../../common/constant/Enums", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMapNodeType(extras) {
    _reporterNs.report("MapNodeType", "../../common/constant/Enums", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Prefab = _cc.Prefab;
      v2 = _cc.v2;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      HeroCmpt = _unresolved_2.default;
    }, function (_unresolved_3) {
      EventType = _unresolved_3.default;
    }, function (_unresolved_4) {
      AnimPlayCmpt = _unresolved_4.default;
    }, function (_unresolved_5) {
      RoleCmpt = _unresolved_5.default;
    }, function (_unresolved_6) {
      RoleObj = _unresolved_6.default;
    }, function (_unresolved_7) {
      vHelper = _unresolved_7.vHelper;
    }, function (_unresolved_8) {
      HeroObj = _unresolved_8.default;
    }, function (_unresolved_9) {
      AreaType = _unresolved_9.AreaType;
      MapNodeType = _unresolved_9.MapNodeType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e0786caNBdOXb+Qh/0ZaZAG", "GameWindCtrl", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Prefab', 'EventTouch', 'Vec3', 'Vec2', 'v2', 'v3', 'Size', 'size']);

      ({
        ccclass
      } = _decorator);

      _export("default", GameWindCtrl = ccclass(_class = class GameWindCtrl extends mc.BaseWindCtrl {
        constructor() {
          super(...arguments);
          //@autocode property begin
          this.chessboardNode_ = null;
          // path://chessboard_n
          this.itemsNode_ = null;
          // path://items_n
          this.flutterNode_ = null;
          // path://flutter_n
          //@end
          this.animPlay = null;
          this.encounterArea = null;
          this.root = null;
          this.recycleRoot = null;
          this.areaPositionMap = {};
          this.recyclePosition = v2();
          this.model = null;
          this.player = null;
          this.heros = [];
          this.heroMap = {};
          this.myRole = null;
          this._temp_vec2_1 = v2();
        }

        listenEventMaps() {
          return [{
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).UPDATE_GAME_INFO]: this.onUpdateGameInfo,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).UPDATE_BATTLE_AREA]: this.onUpdateBattleArea,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).UPDATE_ENCOUNTER_AREA]: this.onUpdateEncounterArea,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).DRAG_HERO_BEGIN]: this.onDragHeroBegin,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).DRAG_HERO_MOVE]: this.onDragHeroMove,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).DRAG_HERO_END]: this.onDragHeroEnd,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).REMOVE_HERO]: this.onRemoveHero,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).PLAY_FLUTTER_HP]: this.onPlayFlutterHp,
            enter: true
          }];
        }

        onCreate() {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this.encounterArea = _this.chessboardNode_.FindChild('encounter');
            _this.root = _this.chessboardNode_.FindChild('root');
            _this.recycleRoot = _this.chessboardNode_.FindChild('recycle/root');
            _this.animPlay = _this.node.addComponent(_crd && AnimPlayCmpt === void 0 ? (_reportPossibleCrUseOfAnimPlayCmpt({
              error: Error()
            }), AnimPlayCmpt) : AnimPlayCmpt);
            _this.areaPositionMap = {};

            _this.chessboardNode_.FindChild('area').children.forEach(m => {
              var area = _this.areaPositionMap[m.name] = {};
              m.FindChild('pos').children.forEach(n => area[n.name] = ut.convertToNodeAR(n, _this.root).clone().toVec3());
            });

            ut.convertToNodeAR(_this.recycleRoot, _this.root, _this.recyclePosition);
            _this.model = _this.getModel('game');
            _this.player = _this.getModel('player');
            _this.myRole = yield _this.createRole(new (_crd && RoleObj === void 0 ? (_reportPossibleCrUseOfRoleObj({
              error: Error()
            }), RoleObj) : RoleObj)().init(110001), 0);
            yield _this.animPlay.init(_this.key);
          })();
        }

        onEnter(data) {
          this.model.enter();
          this.myRole.playAnimation('idle');
          this.recycleRoot.active = false;
          this.initHeroArea();
          this.initEncounterArea();
        }

        onClean() {
          this.model.leave();
          this.animPlay.clean();
          this.animPlay = null;
          assetsMgr.releaseTempResByTag(this.key);
        } // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        // path://chessboard_n/goon_be


        onClickGoon(event, data) {
          this.myRole.playAnimation('attack', () => this.myRole.playAnimation('idle'));
          (_crd && vHelper === void 0 ? (_reportPossibleCrUseOfvHelper({
            error: Error()
          }), vHelper) : vHelper).showPnl('game/Map');
        } //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // 刷新游戏信息


        onUpdateGameInfo() {
          this.initHeroArea();
          this.initEncounterArea();
        } // 刷新战斗区域


        onUpdateBattleArea() {
          this.initHeroArea();
        } // 刷新遭遇区域


        onUpdateEncounterArea() {
          this.initEncounterArea();
        } // 拖动英雄开始


        onDragHeroBegin(hero) {
          this.recycleRoot.active = true;
        } // 拖动英雄移动


        onDragHeroMove(hero) {
          this.recycleRoot.Child('bg').opacity = this.checkInRecycleRange(hero.getTempPosition()) ? 255 : 100;
        } // 拖动英雄结束


        onDragHeroEnd(hero) {
          this.recycleRoot.active = false;
          var heroPosition = hero.getTempPosition();
          var data = hero.data,
              areaType = data.areaType;

          if (areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).BATTLE || areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).PREPARE) {
            //英雄区域
            if (this.checkInRecycleRange(heroPosition)) {
              return this.sellHero(hero); //卖出
            }

            var [_areaType2, index] = this.findMinDisAreaIndex(heroPosition);

            if (_areaType2 === data.areaType && index === data.index) {
              return hero.restorePosition();
            }

            this.moveHero(hero, _areaType2, index);
          } else if (areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).SHOP) {
            //商店区域
            if (hero.node.x >= 0) {
              //没有超过 中线就不算购买
              return hero.restorePosition();
            }

            var [_areaType3, _index2] = this.findMinDisAreaIndex(heroPosition);

            if (_areaType3 === data.areaType && _index2 === data.index) {
              return hero.restorePosition();
            }

            this.buyHero(hero, _areaType3, _index2);
          }
        } // 删除英雄


        onRemoveHero(uid, release) {
          this.cleanHero(this.heros.remove('uid', uid), release);
        } // 播放飘字


        onPlayFlutterHp(data) {
          var _this$heros$find;

          var node = (_this$heros$find = this.heros.find(m => m.uid === data.uid)) == null ? void 0 : _this$heros$find.node;

          if (node) {
            this.animPlay.playFlutter(data, this.flutterNode_, ut.convertToNodeAR(node, this.flutterNode_));
          }
        } // ----------------------------------------- custom function ----------------------------------------------------


        update(dt) {
          var _this$model;

          (_this$model = this.model) == null || _this$model.update(dt);
        } // 创建一个角色


        createRole(data, index) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            if (!_this2.isValid || !data) {
              return null;
            }

            var pfb = yield assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, _this2.key);

            if (!pfb || !_this2.isValid) {
              return null;
            }

            var pos = _this2.chessboardNode_.Child('role_pos/' + index).getPosition();

            var role = yield mc.instantiate(pfb, _this2.itemsNode_).getComponent(_crd && RoleCmpt === void 0 ? (_reportPossibleCrUseOfRoleCmpt({
              error: Error()
            }), RoleCmpt) : RoleCmpt).init(data, pos, _this2.key);
            return role;
          })();
        } // 创建一个英雄


        createHero(data, pos) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            if (!_this3.isValid || !data) {
              return;
            }

            var hero = _this3.heros.find(m => m.uid === data.uid);

            if (!hero) {} else if (data.isDie()) {
              _this3.heros.remove('uid', data.uid);

              _this3.cleanHero(hero);

              return;
            } else {
              return hero.resync(data, pos);
            }

            var node = yield nodePoolMgr.get('hero/HERO', _this3.key);

            if (!node || !node.isValid || !_this3.isValid) {
              return;
            } else if (_this3.heroMap[data.uid]) {
              return; //防止多次创建
            } else if (!data || data.isDie()) {
              return;
            }

            node.parent = _this3.root;
            hero = yield node.Component(_crd && HeroCmpt === void 0 ? (_reportPossibleCrUseOfHeroCmpt({
              error: Error()
            }), HeroCmpt) : HeroCmpt).init(data, pos, _this3.key);

            if (_this3.isValid) {
              _this3.heros.push(hero);

              _this3.heroMap[data.uid] = hero;
            }
          })();
        }

        cleanHero(hero, release) {
          if (hero) {
            delete this.heroMap[hero.uid];
            hero.clean(release);
          }
        } // 运行战斗


        runBattle() {// await Promise.all(this.model.getEnemyAnimals().map(m => this.createHero(m, 'enemy')))
          // this.model.battleBegin()

          return _asyncToGenerator(function* () {})();
        } // 清理区域所有英雄


        cleanAreaAllHero() {
          this.heros.delete(m => m.areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).BATTLE || m.areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
            error: Error()
          }), AreaType) : AreaType).PREPARE).forEach(m => this.cleanHero(m));
        } // 初始化英雄区域


        initHeroArea() {
          this.cleanAreaAllHero();
          this.player.getHeros().forEach(m => this.createHero(m, this.areaPositionMap[m.areaType][m.index]));
        } // 初始化遭遇区域


        initEncounterArea() {
          var encounter = this.model.getEncounter(),
              type = encounter.type,
              data = encounter.getData();
          var node = this.encounterArea.Swih(type)[0];

          if (type === (_crd && MapNodeType === void 0 ? (_reportPossibleCrUseOfMapNodeType({
            error: Error()
          }), MapNodeType) : MapNodeType).TAVERN) {
            var _data$heros;

            //酒馆
            if (data != null && (_data$heros = data.heros) != null && _data$heros.length) {
              data.heros.forEach(m => {
                var hero = new (_crd && HeroObj === void 0 ? (_reportPossibleCrUseOfHeroObj({
                  error: Error()
                }), HeroObj) : HeroObj)().init(m);
                this.createHero(hero, ut.convertToNodeAR(node.Child('pos/' + hero.index), this.root).clone().toVec3());
              });
            } else {
              //清理英雄
              this.heros.delete(m => m.areaType === (_crd && AreaType === void 0 ? (_reportPossibleCrUseOfAreaType({
                error: Error()
              }), AreaType) : AreaType).SHOP).forEach(m => this.cleanHero(m));
            }
          } else if (type === (_crd && MapNodeType === void 0 ? (_reportPossibleCrUseOfMapNodeType({
            error: Error()
          }), MapNodeType) : MapNodeType).ENEMY_BATTLE) {//野怪
          }
        } // 检测是否在回收区域


        checkInRecycleRange(pos) {
          return pos.x > this.recyclePosition.x && pos.x < this.recycleRoot.Width && pos.y > this.recyclePosition.y && pos.y < this.recycleRoot.Height;
        } // 找出英雄最近的区域位置


        findMinDisAreaIndex(pos) {
          var temp = v3(),
              minMag = ut.MAX_VALUE,
              areaType = -1,
              index = -1;

          for (var _areaType in this.areaPositionMap) {
            var area = this.areaPositionMap[_areaType];

            for (var _index in area) {
              var p = temp.set(area[_index]);
              var mag = p.subtract(pos).length();

              if (mag < minMag) {
                areaType = Number(_areaType);
                index = Number(_index);
                minMag = mag;
              }
            }
          }

          return [areaType, index];
        } // 购买英雄


        buyHero(hero, areaType, index) {
          var _this4 = this;

          return _asyncToGenerator(function* () {
            var err = yield _this4.model.buyHero(hero.uid, areaType, index);

            if (err) {
              hero.restorePosition();
              return (_crd && vHelper === void 0 ? (_reportPossibleCrUseOfvHelper({
                error: Error()
              }), vHelper) : vHelper).showAlert(err);
            }

            _this4.initHeroArea();

            _this4.initEncounterArea();
          })();
        } // 出售英雄


        sellHero(hero) {
          var _this5 = this;

          return _asyncToGenerator(function* () {
            var err = yield _this5.model.sellHero(hero.uid);

            if (err) {
              hero.restorePosition();
              return (_crd && vHelper === void 0 ? (_reportPossibleCrUseOfvHelper({
                error: Error()
              }), vHelper) : vHelper).showAlert(err);
            }

            _this5.initHeroArea();
          })();
        } // 移动英雄


        moveHero(hero, areaType, index) {
          var _this6 = this;

          return _asyncToGenerator(function* () {
            var err = yield _this6.model.moveHero(hero.uid, areaType, index);

            if (err) {
              hero.restorePosition();
              return (_crd && vHelper === void 0 ? (_reportPossibleCrUseOfvHelper({
                error: Error()
              }), vHelper) : vHelper).showAlert(err);
            }

            _this6.initHeroArea();
          })();
        }

      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9f324873888fad84c013d81f65c4dc67c8d47ab0.js.map