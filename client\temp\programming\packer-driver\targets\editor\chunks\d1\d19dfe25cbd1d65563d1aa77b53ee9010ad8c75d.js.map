{"version": 3, "sources": ["file:///D:/Projects/auto-chess-client/client/assets/app/script/common/constant/DataType.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEA;mCAOA;AAoBA;AAOA", "sourcesContent": ["import { Vec3 } from \"cc\";\r\n\r\n// 提示框参数选择\r\ntype AlertOpts = {\r\n    params?: any[]; //文本参数\r\n    showTime?: number; //显示时间\r\n    cb?: Function;\r\n}\r\n\r\n// 对话框选择参数\r\ntype MessageBoxOpts = {\r\n    title?: string,\r\n    ok?: Function;\r\n    cancel?: Function;\r\n    okText?: string,\r\n    cancelText?: string,\r\n    params?: any[]; //文本参数\r\n    mask?: boolean; //是否显示mask\r\n    lockClose?: boolean; //禁止点击空白关闭\r\n    clickButtonClose?: boolean; //点击按钮是否关闭界面\r\n}\r\n\r\ntype AnimFrameInfo = {\r\n    name: string;\r\n    interval: number;\r\n    loop: boolean;\r\n    frameIndexs: string[];\r\n}\r\n\r\n// 帧动画配置信息\r\ntype FrameAnimConfInfo = {\r\n    url: string;\r\n    offset: Vec3;\r\n    anims: AnimFrameInfo[];\r\n}\r\n\r\n// 行为树节点配置信息\r\ntype BehaviorNodeConfInfo = {\r\n    type: string;\r\n    cls: string;\r\n    children: number[];\r\n    params?: any;\r\n}\r\n\r\nexport type {\r\n    AlertOpts,\r\n    MessageBoxOpts,\r\n    AnimFrameInfo,\r\n    FrameAnimConfInfo,\r\n    BehaviorNodeConfInfo,\r\n}"]}